# This workflow build and push a Docker container to Google Artifact Registry
# and deploy it on Cloud Run when a commit is pushed to the "main"
# branch.
#
# To configure this workflow:
#
# 1. Enable the following Google Cloud APIs:
#
#    - Artifact Registry (artifactregistry.googleapis.com)
#    - Cloud Run (run.googleapis.com)
#    - IAM Credentials API (iamcredentials.googleapis.com)
#
#    You can learn more about enabling APIs at
#    https://support.google.com/googleapi/answer/6158841.
#
# 2. Create and configure a Workload Identity Provider for GitHub:
#    https://github.com/google-github-actions/auth#preferred-direct-workload-identity-federation.
#
#    Depending on how you authenticate, you will need to grant an IAM principal
#    permissions on Google Cloud:
#
#    - Artifact Registry Administrator (roles/artifactregistry.admin)
#    - Cloud Run Developer (roles/run.developer)
#
#    You can learn more about setting IAM permissions at
#    https://cloud.google.com/iam/docs/manage-access-other-resources
#
# 3. Change the values in the "env" block to match your values.

name: 'Build and Deploy to Cloud Run'

on:
  push:
    branches:
      - main

env:
  PROJECT_ID: 'automated-rune-441907-i8' 
  REGION: 'asia-southeast1'
  IMG_DIR: 'nextcareer-api' 
  SERVICE: 'nextcareer-api' 
  WORKLOAD_IDENTITY_PROVIDER: 'projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/github' 
  SERVICE_ACCOUNT: '<EMAIL>'

jobs:
  deploy-dev:
    runs-on: 'ubuntu-latest'

    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
      - name: 'Checkout'
        uses: 'actions/checkout@v4'

      - name: Add SHORT_SHA env property with commit short sha
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-8`" >> $GITHUB_ENV
      # Configure Workload Identity Federation and generate an access token.
      #
      # See https://github.com/google-github-actions/auth for more options,
      # including authenticating via a JSON credentials file.
      - id: 'auth'
        name: 'Authenticate to Google Cloud'
        uses: 'google-github-actions/auth@v2'
        with:
          workload_identity_provider: '${{ env.WORKLOAD_IDENTITY_PROVIDER }}'
          service_account: ${{ env.SERVICE_ACCOUNT }}
          token_format: 'access_token'  

      # BEGIN - Docker auth and build
      #
      # If you already have a container image, you can omit these steps.
      - name: 'Docker Auth'
        uses: 'docker/login-action@v3'
        with:
          username: 'oauth2accesstoken'
          password: '${{ steps.auth.outputs.access_token }}'
          registry: '${{ env.REGION }}-docker.pkg.dev'

      - name: 'Build and Push Container'
        run: |-
          DOCKER_TAG="${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.IMG_DIR }}/${{ env.SERVICE }}:dev-${{ env.SHORT_SHA }}"
          docker build --tag "${DOCKER_TAG}" .
          docker push "${DOCKER_TAG}"
      # END - Docker auth and build

      - name: 'Deploy to Cloud Run'
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: '${{ env.SERVICE }}'
          region: '${{ env.REGION }}'
          # NOTE: If using a pre-built image, update the image name below:
          image: '${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.IMG_DIR }}/${{ env.SERVICE }}:dev-${{ env.SHORT_SHA }}'
          env_vars: |-
            DB_DEFAULT_HOST=ep-autumn-mode-a185d01a-pooler.ap-southeast-1.aws.neon.tech
            DB_DEFAULT_PORT=5432
            DB_DEFAULT_USER=nextcareer_owner
            DB_DEFAULT_DATABASE=nextcareer
            LOGGER_GENERIC_FORMATTER=sanic.logging.formatter.JSONFormatter
            LOGGER_ACCESS_FORMATTER=sanic.logging.formatter.JSONAccessFormatter
            CORS_ORIGINS=https://resume.nextcareer.ai\,https://dashboard.nextcareer.ai\,http://localhost:3000\,http://localhost:3001\,http://localhost:5173\,http://localhost:8090\,https://next-next-career-ai.vercel.app\,https://resumebuilder-************.asia-southeast1.run.app\,https://app.nextcareer.ai
            LOG_HANDLERS_CONSOLE=google.cloud.logging.handlers.StructuredLogHandler
            LOG_HANDLERS_ERROR_CONSOLE=google.cloud.logging.handlers.StructuredLogHandler
            LOG_HANDLERS_ACCESS_CONSOLE=google.cloud.logging.handlers.StructuredLogHandler
            PASSWORD_ITERATIONS=10000
            LEMONSQUEEZY_STORE_ID=145294
            LEMONSQUEEZY_API_URL=https://api.lemonsqueezy.com/v1/
            AUTH0_CLIENT_ID=vjwH4yXx5LqWKrNlJTs3Uh5fySAQ3fDI
            AUTH0_DOMAIN=ducminhgd.auth0.com
            AUTH0_CALLBACK_URL=https://app.nextcareer.ai/auth0/callback
            POLAR_SERVER=sandbox
            LOOPS_REGISTER_EMAIL_TRANSACTIONAL_ID=cmax3stky01a9ym0ium0cawyn
          secrets: |-
            DB_DEFAULT_PASSWORD=DB_DEFAULT_PASSWORD_DEV:latest
            JWT_PRIVATE_KEY=JWT_PRIVATE_KEY_DEV:latest
            JWT_PUBLIC_KEY=JWT_PUBLIC_KEY_DEV:latest
            OPENAI_API_KEY=OPENAI_API_KEY:latest
            ASSISTANT_ID=ASSISTANT_ID:latest
            FIRECRAWL_API_KEY=FIRECRAWL_API_KEY:latest
            SCRAPEOPS_API_KEY=SCRAPEOPS_API_KEY:latest
            CACHE_DEFAULT_URL=DEV_REDIS_URL:latest
            GROQ_API_KEY=GROQ_API_KEY:latest
            GROQ_QUICK_MODEL=GROQ_QUICK_MODEL:latest
            GROQ_STANDARD_MODEL=GROQ_STANDARD_MODEL:latest
            LEMONSQUEEZY_SIGNING_SECRET=DEV_LEMONSQUEEZY_SIGNING_SECRET:latest
            LEMONSQUEEZY_API_KEY=DEV_LEMONSQUEEZY_API_KEY:latest
            SERPER_API_KEY=SERPER_API_KEY:latest
            AUTH0_CLIENT_SECRET=DEV_AUTH0_CLIENT_SECRET:latest
            AUTH0_APP_SECRET_KEY=DEV_AUTH0_APP_SECRET_KEY:latest
            ADZUNA_API_KEY=ADZUNA_API_KEY:latest
            ADZUNA_APP_ID=ADZUNA_APP_ID:latest
            CAREERJET_API_KEY=CAREERJET_API_KEY:latest
            FINDWORK_API_KEY=FINDWORK_API_KEY:latest
            JOOBLE_API_KEY=JOOBLE_API_KEY:latest
            POLAR_OAT=DEV_POLAR_OAT:latest
            POLAR_SECRET=DEV_POLAR_SECRET:latest
            GOOGLE_APPLICATION_CREDENTIALS_FILENAME=DEV_GOOGLE_APPLICATION_CREDENTIALS_FILENAME:latest
            GCP_SERVICE_ACCOUNT_KEY=GCP_SERVICE_ACCOUNT_KEY:latest
            LOOPS_API_KEY=LOOPS_API_KEY:latest
      # If required, use the Cloud Run URL output in later steps
      - name: 'Show output'
        run: |2-

          echo ${{ steps.deploy.outputs.url }}

          