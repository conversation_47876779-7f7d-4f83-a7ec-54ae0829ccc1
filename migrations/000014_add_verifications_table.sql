CREATE TYPE verification_type AS ENUM ('activation', 'forgot_password', 'reset_password');

CREATE TABLE verification (
    id UUID NOT NULL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id),
    secret VARCHAR(64) NOT NULL,
    verification_type verification_type NOT NULL,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE INDEX idx_verification_lookup ON verification(user_id, secret, verification_type); 