ALTER TABLE "question"
ADD COLUMN category TEXT,
ADD COLUMN overall_strengths JSONB,
ADD COLUMN overall_improvements JSONB,
ADD COLUMN analysis_situation TEXT,
ADD COLUMN analysis_task TEXT,
ADD COLUMN analysis_action TEXT,
ADD COLUMN analysis_result TEXT,
ADD COLUMN analysis_skills TEXT,
ADD COLUMN analysis_academic TEXT,
ADD COLUMN analysis_management TEXT,
ADD COLUMN analysis_personal TEXT,
ADD COLUMN analysis_seek_info TEXT,
ADD COLUMN analysis_patient_safety TEXT,
ADD COLUMN analysis_initiative TEXT,
ADD COLUMN analysis_escalate TEXT,
ADD COLUMN analysis_support TEXT,
ADD COLUMN analysis_strategy TEXT,
ADD COLUMN analysis_technology TEXT,
ADD COLUMN analysis_analytics TEXT,
ADD COLUMN analysis_results TEXT,
ADD COLUMN analysis_transformation TEXT;

-- Create an index for the category column since it might be used for filtering
CREATE INDEX idx_question_category ON "question" (category);

-- Create GIN indexes for the JSONB columns to enable efficient searching
CREATE INDEX idx_question_overall_strengths ON "question" USING GIN (overall_strengths);
CREATE INDEX idx_question_overall_improvements ON "question" USING GIN (overall_improvements); 