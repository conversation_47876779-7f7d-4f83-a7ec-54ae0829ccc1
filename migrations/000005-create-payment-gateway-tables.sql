-- DROP TABLE IF EXISTS "payment_provider";
CREATE TABLE IF NOT EXISTS "payment_provider" (
    id UUID PRIMARY KEY,
    code TEXT,
    name TEXT,
    method SMALLINT,
    security_type SMALLINT,
    ratio FLOAT,
    private_key TEXT,
    public_key TEXT,
    status SMALLINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_payment_provider_code_method ON "payment_provider" (code, method);

CREATE INDEX idx_payment_provider_method ON "payment_provider" (method);

CREATE INDEX idx_payment_provider_status ON "payment_provider" (status);

-- DROP TABLE IF EXISTS "payment_transaction";
CREATE TABLE IF NOT EXISTS "payment_transaction" (
    ref TEXT PRIMARY KEY,
    user_id UUID NOT NULL,
    amount DECIMAL(18, 2) NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    method SMALLINT NOT NULL DEFAULT 0,
    error_detail TEXT, -- error detail from our service
    provider_ref TEXT, -- payment provider reference
    response_code TEXT, -- response code from payment provider
    provider_id UUID NOT NULL, -- payment provider id
    provider_data JSONB, -- payment provider data
    description TEXT, -- payment description
    status SMALLINT NOT NULL DEFAULT 0, -- payment status
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_payment_transaction_user_id ON "payment_transaction" (user_id);

CREATE INDEX idx_payment_transaction_provider_id ON "payment_transaction" (provider_id);

CREATE INDEX idx_payment_transaction_provider_ref ON "payment_transaction" (provider_ref);

CREATE INDEX idx_payment_transaction_amount ON "payment_transaction" USING btree (amount);

CREATE INDEX idx_payment_transaction_method ON "payment_transaction" (method);

CREATE INDEX idx_payment_transaction_status ON "payment_transaction" (status);

CREATE INDEX idx_payment_transaction_created_at ON "payment_transaction" USING btree (created_at);

CREATE INDEX idx_payment_transaction_updated_at ON "payment_transaction" USING btree (updated_at);

ALTER TABLE
    "payment_transaction"
ADD
    CONSTRAINT fk_payment_transaction_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);

ALTER TABLE
    "payment_transaction"
ADD
    CONSTRAINT fk_payment_transaction_provider_id FOREIGN KEY (provider_id) REFERENCES public."payment_provider"(id);