-- Create search_job table
CREATE TABLE IF NOT EXISTS search_job (
    id UUID PRIMARY KEY,
    title TEXT NOT NULL,
    company TEXT NOT NULL,
    location TEXT,
    salary DECIMAL(18, 2),
    description TEXT,
    link TEXT,
    posted_at TEXT,
    is_remote BOOLEAN DEFAULT FALSE,
    source TEXT NOT NULL,
    category TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE,
    keywords TEXT[] DEFAULT '{}'
);

-- Create index on source for faster lookups
CREATE INDEX idx_search_job_source ON search_job(source);

-- Create index on is_remote for filtering remote jobs
CREATE INDEX idx_search_job_is_remote ON search_job(is_remote);

-- Create index on company for faster company-based queries
CREATE INDEX idx_search_job_company ON search_job(company);

-- Create index on posted_at for sorting by date
CREATE INDEX idx_search_job_posted_at ON search_job(posted_at);

-- Create user_saved_job table
CREATE TABLE IF NOT EXISTS user_saved_job (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES search_job(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, job_id)
);

-- Create index on user_id for faster lookups
CREATE INDEX idx_user_saved_job_user_id ON user_saved_job(user_id);

-- Create index on job_id for faster lookups
CREATE INDEX idx_user_saved_job_job_id ON user_saved_job(job_id);