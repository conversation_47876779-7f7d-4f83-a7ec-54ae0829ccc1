-- DROP TABLE IF EXISTS "resume_education";
CREATE TABLE IF NOT EXISTS "resume_education" (
    id UUID PRIMARY KEY,
    resume_id UUID,
    school TEXT,
    degree TEXT,
    field_of_study TEXT,
    start_year SMALLINT,
    start_month SMALLINT,
    end_year SMALLINT,
    end_month SMALLINT,
    grade TEXT,
    activities_and_societies TEXT,
    description TEXT,
    status SMALLINT
);

CREATE INDEX idx_resume_education_status ON "resume_education" (status);

ALTER TABLE
    "resume_education"
ADD
    CONSTRAINT fk_resume_education_resume_resume_id FOREIGN KEY (resume_id) REFERENCES public."resume"(id);

-- DROP TABLE IF EXISTS "resume_experience";
CREATE TABLE IF NOT EXISTS "resume_experience" (
    id UUID PRIMARY KEY,
    resume_id UUID,
    company TEXT,
    position TEXT,
    employment_type INT,
    location TEXT,
    location_type INT,
    start_year SMALLINT,
    start_month SMALLINT,
    end_year SMALLINT,
    end_month SMALLINT,
    is_current BOOLEAN,
    description TEXT,
    status SMALLINT
);

CREATE INDEX idx_resume_experience_status ON "resume_experience" (status);

ALTER TABLE
    "resume_experience"
ADD
    CONSTRAINT fk_resume_experience_resume_resume_id FOREIGN KEY (resume_id) REFERENCES public."resume"(id);

-- DROP TABLE IF EXISTS "user_skill";
CREATE TABLE IF NOT EXISTS "user_skill" (
    id UUID PRIMARY KEY,
    user_id UUID,
    skill TEXT,
    status SMALLINT
);

CREATE INDEX idx_user_skill_status ON "user_skill" (status);

ALTER TABLE
    "user_skill"
ADD
    CONSTRAINT fk_user_skill_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);