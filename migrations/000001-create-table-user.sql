-- Create<PERSON><PERSON>
CREATE TYPE "Provider" AS ENUM ('email', 'github', 'google');

-- DROP TABLE IF EXISTS "user";
CREATE TABLE IF NOT EXISTS "user" (
    id UUID PRIMARY KEY,
    email TEXT,
    fullname TEXT,
    picture TEXT,
    min_salary DECIMAL(18, 2),
    expected_salary DECIMAL(18, 2),
    exp_level SMALLINT,
    linked_in TEXT,
    locale TEXT NOT NULL DEFAULT 'en-US',
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    status SMALLINT,
    password TEXT,
    provider "Provider" NOT NULL,
    auth0_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_user_email ON "user" (email);

-- DROP TABLE IF EXISTS "secret";
CREATE TABLE IF NOT EXISTS "secret" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    verification_token TEXT,
    last_signed_in TIMESTAMPTZ,
    two_factor_secret TEXT,
    two_factor_backup_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    refresh_token TEXT,
    reset_token TEXT
);

ALTER TABLE
    "secret"
ADD
    CONSTRAINT fk_secret_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);
