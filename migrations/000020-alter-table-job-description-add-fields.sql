-- Alter job_description table to add new fields and rename posted_date to posted_at

-- First, rename posted_date to posted_at
ALTER TABLE "job_description" RENAME COLUMN posted_date TO posted_at;

-- Change posted_at type from date to timestamptz
ALTER TABLE "job_description" ALTER COLUMN posted_at TYPE TIMESTAMPTZ USING posted_at::TIMESTAMPTZ;

-- Add salary field
ALTER TABLE "job_description" ADD COLUMN salary DECIMAL(18,2);

-- Add link field
ALTER TABLE "job_description" ADD COLUMN link TEXT;

-- Add is_remote field with default value
ALTER TABLE "job_description" ADD COLUMN is_remote BOOLEAN NOT NULL DEFAULT FALSE;

-- Add source field
ALTER TABLE "job_description" ADD COLUMN source TEXT;

-- Add category field
ALTER TABLE "job_description" ADD COLUMN category TEXT;

-- Add keywords field as an array
ALTER TABLE "job_description" ADD COLUMN keywords TEXT[] NOT NULL DEFAULT '{}';

-- Update indexes to use posted_at instead of posted_date
DROP INDEX IF EXISTS idx_job_description_posted_date;
CREATE INDEX idx_job_description_posted_at ON "job_description" (posted_at);
