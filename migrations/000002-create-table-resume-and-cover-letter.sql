-- DROP TABLE IF EXISTS "resume";
CREATE TABLE IF NOT EXISTS "resume" (
    id UUID PRIMARY KEY,
    user_id UUID,
    title TEXT,
    slug TEXT NOT NULL,
    content JSONB NOT NULL DEFAULT '{}',
    keywords TEXT[],
    status SMALLINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_resume_status ON "resume" (status);
CREATE UNIQUE INDEX idx_resume_user_id_slug ON "resume" (user_id, slug);

ALTER TABLE
    "resume"
ADD
    CONSTRAINT fk_resume_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);

-- DROP TABLE IF EXISTS "cover_letter";
CREATE TABLE IF NOT EXISTS "cover_letter" (
    id UUID PRIMARY KEY,
    user_id UUID,
    resume_id UUID,
    title TEXT,
    content TEXT,
    target_position TEXT[],
    status SMALLINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_cover_letter_status ON "cover_letter" (status);
CREATE INDEX idx_cover_letter_target_position ON "cover_letter" (target_position);

ALTER TABLE
    "cover_letter"
ADD
    CONSTRAINT fk_cover_letter_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);

ALTER TABLE
    "cover_letter"
ADD
    CONSTRAINT fk_cover_letter_resume_resume_id FOREIGN KEY (resume_id) REFERENCES public."resume"(id);