-- CreateEnum
CREATE TYPE "JobStatus" AS ENUM ('private', 'posted', 'denied', 'in_queue');
CREATE TYPE "ContractType" AS ENUM ('full_time', 'part_time', 'contract', 'freelance', 'internship');
CREATE TYPE "PromotionType" AS ENUM ('normal', 'vip', 'up', 'svip');

-- DROP TABLE IF EXISTS "job_description";
CREATE TABLE IF NOT EXISTS "job_description" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    description TEXT NOT NULL,
    position TEXT,
    company TEXT,
    city TEXT,
    country TEXT,
    posted_date DATE,
    skills TEXT[],
    status "JobStatus" NOT NULL DEFAULT 'private',
    contract_type "ContractType",
    is_favorite BOOLEAN NOT NULL DEFAULT false,
    promotion "PromotionType" NOT NULL DEFAULT 'normal',
    mock_interview_id UUID,
    resume_id UUID,
    follow_up_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraint to user table
ALTER TABLE
    "job_description"
ADD
    CONSTRAINT fk_job_description_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);

-- Create indexes for common query patterns
CREATE INDEX idx_job_description_user_id ON "job_description" (user_id);
CREATE INDEX idx_job_description_status ON "job_description" (status);
CREATE INDEX idx_job_description_posted_date ON "job_description" (posted_date); 