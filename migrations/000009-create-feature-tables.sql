-- DROP TABLE IF EXISTS "feature" CASCADE;
CREATE TABLE IF NOT EXISTS "feature" (
    id UUID PRIMARY KEY,
    code TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    description TEXT,
    default_value JSONB,
    visibility SMALLINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_feature_code ON "feature" (code);

CREATE INDEX idx_feature_visibility ON "feature" (visibility);

-- DROP TABLE IF EXISTS "subscription_plan" CASCADE;
CREATE TABLE IF NOT EXISTS "subscription_plan" (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT NOT NULL UNIQUE,
    price DECIMAL(18, 2) NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    billing_cycle SMALLINT NOT NULL DEFAULT 0,
    is_auto_renewable BOOLEAN NOT NULL DEFAULT FALSE,
    description TEXT,
    trial_period_days INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    status SMALLINT NOT NULL DEFAULT 0,
    lemon_variant_id INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX idx_subscription_plan_code ON "subscription_plan" (code);

CREATE INDEX idx_subscription_plan_status ON "subscription_plan" (status);

CREATE INDEX idx_subscription_plan_is_public ON "subscription_plan" (is_public);

CREATE INDEX idx_subscription_plan_sort_order ON "subscription_plan" (sort_order);

CREATE INDEX idx_subscription_plan_lemon_variant_id ON "subscription_plan" (lemon_variant_id);

-- DROP TABLE IF EXISTS "subscription_plan_feature" CASCADE;
CREATE TABLE IF NOT EXISTS "subscription_plan_feature" (
    id UUID PRIMARY KEY,
    subscription_plan_id UUID NOT NULL,
    feature_id UUID NOT NULL,
    feature_value JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_subscription_plan_feature_subscription_plan_id ON "subscription_plan_feature" (subscription_plan_id);

CREATE INDEX idx_subscription_plan_feature_feature_id ON "subscription_plan_feature" (feature_id);

-- Add foreign key constraints
ALTER TABLE
    "subscription_plan_feature"
ADD
    CONSTRAINT fk_subscription_plan_feature_subscription_plan_id FOREIGN KEY (subscription_plan_id) REFERENCES "subscription_plan" (id);

ALTER TABLE
    "subscription_plan_feature"
ADD
    CONSTRAINT fk_subscription_plan_feature_feature_id FOREIGN KEY (feature_id) REFERENCES "feature" (id);