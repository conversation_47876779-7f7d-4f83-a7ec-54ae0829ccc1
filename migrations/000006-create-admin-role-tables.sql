-- DROP TABLE IF EXISTS "role";
CREATE TABLE IF NOT EXISTS "role" (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status SMALLINT NOT NULL DEFAULT 2,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_role_name ON "role" (name);

-- DROP TABLE IF EXISTS "user_role";
CREATE TABLE IF NOT EXISTS "user_role" (
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    PRIMARY KEY (user_id, role_id),
    CONSTRAINT fk_user_id FOREIGN KEY (user_id) REFERENCES "user" (id) ON DELETE CASCADE,
    CONSTRAINT fk_role_id FOREIGN KEY (role_id) REFERENCES "role" (id) ON DELETE CASCADE
);

-- DROP TABLE IF EXISTS "permission";
CREATE TABLE IF NOT EXISTS "permission" (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_permission_name ON "permission" (name);

-- DROP TABLE IF EXISTS "role_permission";
CREATE TABLE IF NOT EXISTS "role_permission" (
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    CONSTRAINT fk_role_id FOREIGN KEY (role_id) REFERENCES "role" (id) ON DELETE CASCADE,
    CONSTRAINT fk_permission_id FOREIGN KEY (permission_id) REFERENCES "permission" (id) ON DELETE CASCADE
);