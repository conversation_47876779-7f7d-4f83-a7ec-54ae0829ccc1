-- Create Polar Order related tables

-- Create polar_customer table
CREATE TABLE IF NOT EXISTS "polar_customer" (
    id UUID PRIMARY KEY,
    polar_customer_id TEXT NOT NULL UNIQUE,
    external_id TEXT,
    email TEXT NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    name TEXT,
    billing_address JSONB,
    tax_id TEXT[],
    organization_id TEXT,
    deleted_at TIMESTAMPTZ,
    avatar_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_polar_customer_polar_id ON "polar_customer" (polar_customer_id);
CREATE INDEX idx_polar_customer_email ON "polar_customer" (email);

-- Create polar_product table
CREATE TABLE IF NOT EXISTS "polar_product" (
    id UUID PRIMARY KEY,
    polar_product_id TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    description TEXT,
    recurring_interval TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    organization_id TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_polar_product_polar_id ON "polar_product" (polar_product_id);

-- Create polar_discount table
CREATE TABLE IF NOT EXISTS "polar_discount" (
    id UUID PRIMARY KEY,
    polar_discount_id TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    code TEXT,
    duration TEXT,
    type TEXT,
    amount INTEGER,
    currency TEXT,
    starts_at TIMESTAMPTZ,
    ends_at TIMESTAMPTZ,
    max_redemptions INTEGER,
    redemptions_count INTEGER DEFAULT 0,
    organization_id TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_polar_discount_polar_id ON "polar_discount" (polar_discount_id);
CREATE INDEX idx_polar_discount_code ON "polar_discount" (code);

-- Create polar_subscription table
CREATE TABLE IF NOT EXISTS "polar_subscription" (
    id UUID PRIMARY KEY,
    polar_subscription_id TEXT NOT NULL UNIQUE,
    amount INTEGER,
    currency TEXT,
    recurring_interval TEXT,
    status TEXT,
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    ends_at TIMESTAMPTZ,
    ended_at TIMESTAMPTZ,
    customer_id TEXT,
    product_id TEXT,
    discount_id TEXT,
    checkout_id TEXT,
    customer_cancellation_reason TEXT,
    customer_cancellation_comment TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_polar_subscription_polar_id ON "polar_subscription" (polar_subscription_id);
CREATE INDEX idx_polar_subscription_customer_id ON "polar_subscription" (customer_id);

-- Create polar_order table
CREATE TABLE IF NOT EXISTS "polar_order" (
    id UUID PRIMARY KEY,
    polar_order_id TEXT NOT NULL UNIQUE,
    status TEXT NOT NULL,
    paid BOOLEAN DEFAULT FALSE,
    subtotal_amount INTEGER,
    discount_amount INTEGER,
    net_amount INTEGER,
    amount INTEGER,
    tax_amount INTEGER,
    total_amount INTEGER,
    refunded_amount INTEGER,
    refunded_tax_amount INTEGER,
    currency TEXT,
    billing_reason TEXT,
    billing_address JSONB,
    customer_id TEXT,
    product_id TEXT,
    discount_id TEXT,
    subscription_id TEXT,
    checkout_id TEXT,
    user_id TEXT,
    metadata JSONB DEFAULT '{}',
    custom_field_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_polar_order_polar_id ON "polar_order" (polar_order_id);
CREATE INDEX idx_polar_order_customer_id ON "polar_order" (customer_id);
CREATE INDEX idx_polar_order_user_id ON "polar_order" (user_id);
CREATE INDEX idx_polar_order_status ON "polar_order" (status);

-- Create polar_order_item table
CREATE TABLE IF NOT EXISTS "polar_order_item" (
    id UUID PRIMARY KEY,
    polar_order_id UUID NOT NULL,
    label TEXT,
    amount INTEGER,
    tax_amount INTEGER,
    proration BOOLEAN DEFAULT FALSE,
    product_price_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE "polar_order_item"
ADD CONSTRAINT fk_polar_order_item_order_id 
FOREIGN KEY (polar_order_id) REFERENCES "polar_order"(id) ON DELETE CASCADE;

CREATE INDEX idx_polar_order_item_order_id ON "polar_order_item" (polar_order_id);
