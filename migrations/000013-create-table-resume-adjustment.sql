-- DROP TABLE IF EXISTS "resume_adjustment";
CREATE TABLE IF NOT EXISTS "resume_adjustment" (
    id UUID PRIMARY KEY,
    resume_id UUID NOT NULL,
    job_title TEXT NOT NULL,
    educations JSONB NOT NULL DEFAULT '[]',
    skills JSONB NOT NULL DEFAULT '[]',
    experiences JSONB NOT NULL DEFAULT '[]',
    status SMALLINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ
);

-- Index for status field for efficient querying
CREATE INDEX idx_resume_adjustment_status ON "resume_adjustment" (status);

-- Index for job_title to support searching/filtering
CREATE INDEX idx_resume_adjustment_job_title ON "resume_adjustment" (job_title);

-- Index for resume_id for efficient joins
CREATE INDEX idx_resume_adjustment_resume_id ON "resume_adjustment" (resume_id);

-- Add foreign key constraint
ALTER TABLE
    "resume_adjustment"
ADD
    CONSTRAINT fk_resume_adjustment_resume_resume_id FOREIGN KEY (resume_id) REFERENCES public."resume"(id);