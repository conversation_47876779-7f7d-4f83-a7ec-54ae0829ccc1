-- DROP TABLE IF EXISTS "user_subscription" CASCADE;
CREATE TABLE IF NOT EXISTS "user_subscription" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    subscription_plan_id UUID NOT NULL,
    status SMALLINT NOT NULL DEFAULT 0,
    start_date TIM<PERSON><PERSON>MP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    last_billing_date TIMESTAMP WITH TIME ZONE,
    next_billing_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_user_subscription_user_id ON "user_subscription" (user_id);
CREATE INDEX idx_user_subscription_subscription_plan_id ON "user_subscription" (subscription_plan_id);
CREATE INDEX idx_user_subscription_status ON "user_subscription" (status);

-- DROP TABLE IF EXISTS "user_feature" CASCADE;
CREATE TABLE IF NOT EXISTS "user_feature" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    feature_id UUID NOT NULL,
    feature_value JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE
);

CREATE UNIQUE INDEX idx_user_feature_user_id_feature_id ON "user_feature" (user_id, feature_id);
CREATE INDEX idx_user_feature_user_id ON "user_feature" (user_id);
CREATE INDEX idx_user_feature_feature_id ON "user_feature" (feature_id);

-- Add foreign key constraints
ALTER TABLE "user_subscription"
ADD CONSTRAINT fk_user_subscription_user_id 
FOREIGN KEY (user_id) REFERENCES "user" (id);

ALTER TABLE "user_subscription"
ADD CONSTRAINT fk_user_subscription_subscription_plan_id 
FOREIGN KEY (subscription_plan_id) REFERENCES "subscription_plan" (id);

ALTER TABLE "user_feature"
ADD CONSTRAINT fk_user_feature_user_id 
FOREIGN KEY (user_id) REFERENCES "user" (id);

ALTER TABLE "user_feature"
ADD CONSTRAINT fk_user_feature_feature_id 
FOREIGN KEY (feature_id) REFERENCES "feature" (id);
