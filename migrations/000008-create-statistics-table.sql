-- DROP TABLE IF EXISTS "statistics";
CREATE TABLE IF NOT EXISTS "statistics" (
    id UUID PRIMARY KEY,
    resume_id UUID,
    views INTEGER NOT NULL DEFAULT 0,
    downloads INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP   
);

CREATE INDEX idx_statistics_resume_id ON "statistics" (resume_id);

ALTER TABLE
    "statistics"
ADD
    CONSTRAINT fk_statistics_resume_resume_id FOREIGN KEY (resume_id) REFERENCES public."resume"(id);