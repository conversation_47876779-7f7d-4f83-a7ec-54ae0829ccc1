-- DROP TABLE IF EXISTS "mock_interview";
CREATE TABLE IF NOT EXISTS "mock_interview" (
    id UUID PRIMARY KEY,
    user_id UUID,
    position TEXT,
    job_description TEXT,
    status SMALLINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_mock_interview_status ON "mock_interview" (status);

ALTER TABLE
    "mock_interview"
ADD
    CONSTRAINT fk_mock_interview_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);

-- DROP TABLE IF EXISTS "question";
CREATE TABLE IF NOT EXISTS "question" (
    id UUID PRIMARY KEY,
    question TEXT,
    answer TEXT,
    status SMALLINT,
    difficulty SMALLINT DEFAULT 1,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_question_status ON "question" (status);
CREATE INDEX idx_question_difficulty ON "question" (difficulty);

-- DROP TABLE IF EXISTS "mock_interview_question";
CREATE TABLE IF NOT EXISTS "mock_interview_question" (
    id UUID PRIMARY KEY,
    mock_interview_id UUID,
    question_id UUID,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE
    "mock_interview_question"
ADD
    CONSTRAINT fk_mock_interview_question_mock_interview_mock_interview_id FOREIGN KEY (mock_interview_id) REFERENCES public."mock_interview"(id);

ALTER TABLE
    "mock_interview_question"
ADD
    CONSTRAINT fk_mock_interview_question_question_question_id FOREIGN KEY (question_id) REFERENCES public."question"(id);