-- DROP TABLE IF EXISTS "article" CASCADE;
CREATE TABLE IF NOT EXISTS "article" (
    id UUID PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    banner_image_url TEXT,
    author TEXT NOT NULL,
    published_at TIMESTAMPTZ,
    likes_count INTEGER NOT NULL DEFAULT 0,
    tags TEXT[] NOT NULL DEFAULT ARRAY[]::TEXT[],
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for common query patterns
CREATE INDEX idx_article_author ON "article" (author);
CREATE INDEX idx_article_published_at ON "article" (published_at);
CREATE INDEX idx_article_title ON "article" (title);
CREATE INDEX idx_article_tags ON "article" USING GIN (tags);

-- Create article_like table to track user likes
CREATE TABLE IF NOT EXISTS "article_like" (
    id UUID PRIMARY KEY,
    article_id UUID NOT NULL,
    user_id UUID NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(article_id, user_id)
);

-- Create indexes for article_like
CREATE INDEX idx_article_like_article_id ON "article_like" (article_id);
CREATE INDEX idx_article_like_user_id ON "article_like" (user_id);

-- Add foreign key constraints for article_like
ALTER TABLE "article_like"
ADD CONSTRAINT fk_article_like_article_id
FOREIGN KEY (article_id) REFERENCES public."article"(id) ON DELETE CASCADE;

ALTER TABLE "article_like"
ADD CONSTRAINT fk_article_like_user_id
FOREIGN KEY (user_id) REFERENCES public."user"(id) ON DELETE CASCADE; 