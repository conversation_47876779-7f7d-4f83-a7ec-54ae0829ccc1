-- DROP TABLE IF EXISTS "access_token";
CREATE TABLE IF NOT EXISTS "access_token" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    claims JSONB NOT NULL,
    issued_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMPTZ NOT NULL,
    rt_id UUID NOT NULL,
    rt_expired_at TIMESTAMPTZ NOT NULL
);

ALTER TABLE
    "access_token"
ADD
    CONSTRAINT fk_access_token_user_user_id FOREIGN KEY (user_id) REFERENCES public."user"(id);

CREATE INDEX idx_access_token_rt_id ON "access_token" (rt_id);