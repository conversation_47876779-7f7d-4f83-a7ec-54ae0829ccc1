-- DROP TABLE IF EXISTS "follow_up_letter" CASCADE;
CREATE TABLE IF NOT EXISTS "follow_up_letter" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    jd_id UUID NOT NULL,
    resume_id UUID,
    follow_up TEXT NOT NULL,
    customization_note TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for common query patterns
CREATE INDEX idx_follow_up_letter_user_id ON "follow_up_letter" (user_id);
CREATE INDEX idx_follow_up_letter_jd_id ON "follow_up_letter" (jd_id);

-- DROP TABLE IF EXISTS "job_insights" CASCADE;
CREATE TABLE IF NOT EXISTS "job_insights" (
    id UUID PRIMARY KEY,
    jd_id UUID NOT NULL,
    resume_id UUID,
    job_title TEXT NOT NULL,
    overview TEXT,
    core_skills JSONB NOT NULL DEFAULT '[]',
    trending_skills JSONB NOT NULL DEFAULT '[]',
    soft_skills JSONB NOT NULL DEFAULT '[]',
    professional_courses JSONB NOT NULL DEFAULT '[]',
    certifications JSONB NOT NULL DEFAULT '[]',
    projects JSONB NOT NULL DEFAULT '[]',
    expected_salary JSONB NOT NULL DEFAULT '{}',
    advises TEXT[] NOT NULL DEFAULT ARRAY[]::TEXT[],
    other_insights TEXT[] NOT NULL DEFAULT ARRAY[]::TEXT[],
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for job_insights
CREATE INDEX idx_job_insights_jd_id ON "job_insights" (jd_id);
CREATE INDEX idx_job_insights_resume_id ON "job_insights" (resume_id);

-- DROP TABLE IF EXISTS "application_kit" CASCADE;
CREATE TABLE IF NOT EXISTS "application_kit" (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    jd_id UUID NOT NULL,
    resume_id UUID,
    cover_letter_id UUID,
    follow_up_id UUID,
    mock_interview_id UUID,
    job_insight_id UUID,
    status int2 NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for application_kit
CREATE INDEX idx_application_kit_user_id ON "application_kit" (user_id);
CREATE INDEX idx_application_kit_jd_id ON "application_kit" (jd_id);
CREATE INDEX idx_application_kit_status ON "application_kit" (status);

-- Add foreign key constraints for application_kit
ALTER TABLE "application_kit"
ADD CONSTRAINT fk_application_kit_user_user_id 
FOREIGN KEY (user_id) REFERENCES public."user"(id);

ALTER TABLE "application_kit"
ADD CONSTRAINT fk_application_kit_jd_id 
FOREIGN KEY (jd_id) REFERENCES public."job_description"(id);

ALTER TABLE "application_kit"
ADD CONSTRAINT fk_application_kit_resume_id 
FOREIGN KEY (resume_id) REFERENCES public."resume"(id);

ALTER TABLE "application_kit"
ADD CONSTRAINT fk_application_kit_cover_letter_id 
FOREIGN KEY (cover_letter_id) REFERENCES public."cover_letter"(id);

ALTER TABLE "application_kit"
ADD CONSTRAINT fk_application_kit_follow_up_id 
FOREIGN KEY (follow_up_id) REFERENCES public."follow_up_letter"(id);

ALTER TABLE "application_kit"
ADD CONSTRAINT fk_application_kit_job_insight_id 
FOREIGN KEY (job_insight_id) REFERENCES public."job_insights"(id); 