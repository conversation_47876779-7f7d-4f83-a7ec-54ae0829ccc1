import os
from sanic import Sanic
from sanic.response import html
from jinja2 import Environment, FileSystemLoader

app = Sanic(__name__)

# Set up Jinja2
env = Environment(loader=FileSystemLoader('tools'))
template = env.get_template('check_cors.html')

URL = os.getenv('DEBUG_CORS_URL', 'http://localhost:8000/users')
TOKEN = os.getenv('DEBUG_CORS_TOKEN', '')


@app.route('/')
def home(request):
    return html(template.render(url=URL, token=TOKEN))


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=3000, debug=True)
