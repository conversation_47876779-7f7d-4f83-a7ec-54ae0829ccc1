import asyncio
import os
from tortoise import Tortoise
from next_api.models.role_permission import Role, Permission


async def admin_site():
    role, _ = await Role.get_or_create(
        name='Administrator site',
        description='Login into Adminstrator site',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:admin:login', 'description': 'Login to admin site'},
        {'name': 'admin:admin:self_update',
            'description': 'Admin self-update permission'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_user():
    role, _ = await Role.get_or_create(
        name='User management',
        description='Manage users of system',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:user:create', 'description': 'Create a new user'},
        {'name': 'admin:user:update', 'description': 'Update a user'},
        {'name': 'admin:user:delete', 'description': 'Delete a user'},
        {'name': 'admin:user:get', 'description': 'Get a user'},
        {'name': 'admin:user:get_all', 'description': 'Get all users'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)
        
    role, _ = await Role.get_or_create(
        name='Administrator site',
        description='Login into Adminstrator site',
        status=Role.Status.ACTIVE
    )
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_role():
    role, _ = await Role.get_or_create(
        name='Role management',
        description='Manage roles of system',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:role:create', 'description': 'Create a new role'},
        {'name': 'admin:role:update', 'description': 'Update a role'},
        {'name': 'admin:role:delete', 'description': 'Delete a role'},
        {'name': 'admin:role:get', 'description': 'Get a role'},
        {'name': 'admin:role:get_all', 'description': 'Get all roles'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_permission():
    role, _ = await Role.get_or_create(
        name='Permission management',
        description='Manage permissions of system',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:permission:create',
            'description': 'Create a new permission'},
        {'name': 'admin:permission:update', 'description': 'Update a permission'},
        {'name': 'admin:permission:delete', 'description': 'Delete a permission'},
        {'name': 'admin:permission:get', 'description': 'Get a permission'},
        {'name': 'admin:permission:get_all', 'description': 'Get all permissions'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_resume():
    role, _ = await Role.get_or_create(
        name='Resume management',
        description='Manage resumes from admin site',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:resume:create', 'description': 'Create a new resume'},
        {'name': 'admin:resume:update', 'description': 'Update a resume'},
        {'name': 'admin:resume:delete', 'description': 'Delete a resume'},
        {'name': 'admin:resume:get', 'description': 'Get a resume'},
        {'name': 'admin:resume:get_all', 'description': 'Get all resumes'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_cover_letter():
    role, _ = await Role.get_or_create(
        name='Cover letter management',
        description='Manage cover letters from admin site',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:cover-letter:create',
            'description': 'Create a new cover letter'},
        {'name': 'admin:cover-letter:update',
            'description': 'Update a cover letter'},
        {'name': 'admin:cover-letter:delete',
            'description': 'Delete a cover letter'},
        {'name': 'admin:cover-letter:get', 'description': 'Get a cover letter'},
        {'name': 'admin:cover-letter:get_all',
            'description': 'Get all cover letters'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_mock_interview():
    role, _ = await Role.get_or_create(
        name='Mock interview management',
        description='Manage mock interviews from admin site',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:mock_interview:create',
            'description': 'Create a new mock interview'},
        {'name': 'admin:mock_interview:update',
            'description': 'Update a mock interview'},
        {'name': 'admin:mock_interview:delete',
            'description': 'Delete a mock interview'},
        {'name': 'admin:mock_interview:get', 'description': 'Get a mock interview'},
        {'name': 'admin:mock_interview:get_all',
            'description': 'Get all mock interviews'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def admin_manages_question():
    role, _ = await Role.get_or_create(
        name='Question management',
        description='Manage questions from admin site',
        status=Role.Status.ACTIVE
    )
    print('Insert role: %s' % role.name)
    permissions = [
        {'name': 'admin:question:create', 'description': 'Create a new question'},
        {'name': 'admin:question:update', 'description': 'Update a question'},
        {'name': 'admin:question:delete', 'description': 'Delete a question'},
        {'name': 'admin:question:get', 'description': 'Get a question'},
        {'name': 'admin:question:get_all', 'description': 'Get all questions'},
    ]
    for p in permissions:
        permission, _ = await Permission.get_or_create(**p)
        await role.permissions.add(permission)
        print('\tGranted permission: %s' % permission.name)


async def run():
    await Tortoise.init({
        'connections': {
            'default': {
                'engine': 'tortoise.backends.asyncpg',
                'credentials': {
                    'host': os.getenv('DB_DEFAULT_HOST', 'localhost'),
                    'port': int(os.getenv('DB_DEFAULT_PORT', '5432')),
                    'user': os.getenv('DB_DEFAULT_USER', 'postgres_user'),
                    'password': os.getenv('DB_DEFAULT_PASSWORD', 'postgres_pw'),
                    'database': os.getenv('DB_DEFAULT_DATABASE', 'next_ai'),
                },
            },
        },
        'apps': {
            'next_api': {
                'models': ['next_api.models',],
                'default_connection': 'default',
            },
        },
        # 'routers': ['utils.db.Router'],
        'use_tz': True,
        # 'timezone': 'UTC',
    })

    await admin_site()
    await admin_manages_user()
    await admin_manages_role()
    await admin_manages_permission()
    await admin_manages_resume()
    await admin_manages_cover_letter()
    await admin_manages_mock_interview()
    await admin_manages_question()

if __name__ == '__main__':
    asyncio.run(run())
