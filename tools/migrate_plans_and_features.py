import asyncio
import sys
from datetime import datetime
from decimal import Decimal
from uuid_extensions import uuid7
from tortoise import Tortoise
from settings import DAT<PERSON>ASE_CONFIG, MOCK_INTERVIEW_FREE_QUESTIONS_LIMIT
from models.feature import Feature
from models.subscription_plan import SubscriptionPlan
from models.subscription_plan_feature import SubscriptionPlanFeature


async def create_features():
    """Create all features available in the system."""
    features = [
        {
            "id": uuid7(),
            "code": Feature.Code.RESUME_BUILDER,
            "name": "Resume Builder",
            "description": "Create professional resumes with our easy-to-use builder",
            "default_value": {"enabled": True},
            "visibility": Feature.Visibility.PUBLIC,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": Feature.Code.RESUME_AI_BUILDER,
            "name": "AI Resume Builder",
            "description": "Use AI to help create and optimize your resume",
            "default_value": {"enabled": True},
            "visibility": Feature.Visibility.PUBLIC,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": Feature.Code.COVER_LETTER_BUILDER,
            "name": "Cover Letter Builder",
            "description": "Create matching cover letters for your applications",
            "default_value": {"enabled": True},
            "visibility": Feature.Visibility.PUBLIC,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": Feature.Code.COVER_LETTER_AI_BUILDER,
            "name": "AI Cover Letter Builder",
            "description": "Use AI to generate personalized cover letters",
            "default_value": {"enabled": True},
            "visibility": Feature.Visibility.PUBLIC,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": Feature.Code.MOCK_INTERVIEW_AI,
            "name": "AI Mock Interviews",
            "description": "Practice interviews with AI and get feedback",
            "default_value": {"enabled": True, "no_questions": MOCK_INTERVIEW_FREE_QUESTIONS_LIMIT},
            "visibility": Feature.Visibility.PUBLIC,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": Feature.Code.INTERVIEW_AI_SUGGESTION,
            "name": "AI Interview Suggestions",
            "description": "Get AI-powered suggestions for interview answers",
            "default_value": {"enabled": True},
            "visibility": Feature.Visibility.PUBLIC,
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
    ]

    for feature_data in features:
        await Feature.create(**feature_data)

    return features


async def create_subscription_plans(features):
    """Create subscription plans with different billing cycles."""
    plans = [
        {
            "id": uuid7(0),
            "code": "free",
            "name": "Free Plan",
            "description": "Basic feature only",
            "price_description": "Free Forever!",
            "price": Decimal("0"),
            "currency": "USD",
            "billing_cycle": SubscriptionPlan.BillingCycle.LIFETIME,
            "is_auto_renewable": False,
            "trial_period_days": 0,
            "sort_order": 1,
            "is_public": True,
            "status": SubscriptionPlan.Status.ACTIVE,
            "lemon_variant_id": 654216,
            "polar_product_id": "37cabc3e-b0fe-4ae6-b4dd-361bccfd303a",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": "premium_monthly",
            "name": "Premium Monthly",
            "description": "All features",
            "price_description": "$19/month",
            "price": Decimal("19"),
            "currency": "USD",
            "billing_cycle": SubscriptionPlan.BillingCycle.MONTHLY,
            "is_auto_renewable": True,
            "trial_period_days": 0,
            "sort_order": 2,
            "is_public": True,
            "status": SubscriptionPlan.Status.ACTIVE,
            "lemon_variant_id": 658250,
            "polar_product_id": "5a6d2aad-4061-4f8b-b18e-4d78fd60d096",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": "education_monthly",
            "name": "Education Plan",
            "description": "All features",
            "price_description": "$12/month",
            "price": Decimal("12"),
            "currency": "USD",
            "billing_cycle": SubscriptionPlan.BillingCycle.MONTHLY,
            "is_auto_renewable": True,
            "trial_period_days": 0,
            "sort_order": 3,
            "is_public": True,
            "status": SubscriptionPlan.Status.ACTIVE,
            "lemon_variant_id": 658251,
            "polar_product_id": "8baa3fbf-7a36-4fff-910a-b0d2c80aa129",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": "premium_yearly_59",
            "name": "Premium Yearly",
            "description": "All features",
            "price_description": "$4.99/month",
            "price": Decimal("59"),
            "currency": "USD",
            "billing_cycle": SubscriptionPlan.BillingCycle.ANNUAL,
            "is_auto_renewable": True,
            "trial_period_days": 0,
            "sort_order": 4,
            "is_public": True,
            "status": SubscriptionPlan.Status.ACTIVE,
            "lemon_variant_id": 658253,
            "polar_product_id": "b3130200-cc71-4243-a7d1-6071e9398fed",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        },
        {
            "id": uuid7(),
            "code": "education_yearly",
            "name": "Education yearly",
            "description": "All features",
            "price_description": "$3.2/month",
            "price": Decimal("39"),
            "currency": "USD",
            "billing_cycle": SubscriptionPlan.BillingCycle.ANNUAL,
            "is_auto_renewable": True,
            "trial_period_days": 0,
            "sort_order": 5,
            "is_public": True,
            "status": SubscriptionPlan.Status.ACTIVE,
            "lemon_variant_id": 703046,
            "polar_product_id": "432c5ac2-9020-40af-a0a3-3c8640869873",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
    ]

    # Create plans
    created_plans = []
    for plan_data in plans:
        plan = await SubscriptionPlan.create(**plan_data)
        created_plans.append(plan)

    # Map all features to each plan with the same values
    feature_values = {
        Feature.Code.RESUME_BUILDER: {"enabled": True},
        Feature.Code.RESUME_AI_BUILDER: {"enabled": True},
        Feature.Code.COVER_LETTER_BUILDER: {"enabled": True},
        Feature.Code.COVER_LETTER_AI_BUILDER: {"enabled": True},
        Feature.Code.MOCK_INTERVIEW_AI: {"enabled": True, "no_questions": MOCK_INTERVIEW_FREE_QUESTIONS_LIMIT},
        Feature.Code.INTERVIEW_AI_SUGGESTION: {"enabled": True}
    }

    # Create feature mappings for each plan
    plan: SubscriptionPlan
    for plan in created_plans:
        for feature in features:
            feature_value = feature_values[feature['code']]
            if feature['code'] == Feature.Code.MOCK_INTERVIEW_AI:
                if plan.id == uuid7(0):
                    feature_value['no_questions'] = MOCK_INTERVIEW_FREE_QUESTIONS_LIMIT
                else:
                    feature_value['no_questions'] = sys.maxsize
            await SubscriptionPlanFeature.create(
                id=uuid7(),
                subscription_plan_id=plan.id,
                feature_id=feature['id'],
                feature_value=feature_value,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

    return created_plans


async def run():
    await Tortoise.init(DATABASE_CONFIG)
    features = await create_features()
    await create_subscription_plans(features)
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(run())
