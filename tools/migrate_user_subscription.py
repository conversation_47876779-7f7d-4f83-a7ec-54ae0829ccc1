import asyncio
from datetime import datetime
from uuid_extensions import uuid7
from tortoise import Tortoise
from settings import DATABASE_CONFIG
from models.user import User
from repositories.user_subscription import UserSubscriptionRepository


async def run():
    await Tortoise.init(DATABASE_CONFIG)
    users = await User.all()

    for user in users:
        await UserSubscriptionRepository.subscribe_plan(
            user_id=user.id,
            plan_id=uuid7(0),
            start_date=datetime.now(),
            partner=None,
            partner_id=None,
        )

    user_ids = [
    ]
    for user_id in user_ids:
        await UserSubscriptionRepository.subscribe_plan(
            user_id=user_id,
            plan_id=uuid7(0),
            start_date=datetime.now(),
            partner=None,
            partner_id=None,
        )

if __name__ == "__main__":
    asyncio.run(run())
