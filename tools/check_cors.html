<!DOCTYPE html>
<html>
<head>
    <title>CORS Checker</title>
</head>
<body>
    <h1>CORS Policy Checker</h1>
    <button id="checkCors">Check CORS</button>
    <p id="result"></p>
    <script>
        document.getElementById("checkCors").addEventListener("click", () => {
            fetch("{{ url }}", {
                method: "GET",
                mode: "cors",
                headers: {
                    "Authorization": "Bearer {{ token }}"
                }
            })
            .then(response => {
                if (response.ok) {
                    document.getElementById("result").textContent = "CORS is allowed. Status: " + response.status;
                } else {
                    document.getElementById("result").textContent = "CORS check failed. Status: " + response.status;
                }
            })
            .catch(error => {
                document.getElementById("result").textContent = "Error: " + error.message;
            });
        });
    </script>
</body>
</html>
