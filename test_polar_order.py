#!/usr/bin/env python3
"""
Test script for Polar Order webhook functionality.
"""

import asyncio
import json
from datetime import datetime
from uuid_extensions import uuid7

# Sample webhook payload for testing
SAMPLE_WEBHOOK_PAYLOAD = {
    "type": "order.created",
    "data": {
        "id": "test-order-123",
        "created_at": "2023-11-07T05:31:56Z",
        "modified_at": "2023-11-07T05:31:56Z",
        "status": "paid",
        "paid": True,
        "subtotal_amount": 123,
        "discount_amount": 123,
        "net_amount": 123,
        "amount": 123,
        "tax_amount": 123,
        "total_amount": 123,
        "refunded_amount": 123,
        "refunded_tax_amount": 123,
        "currency": "USD",
        "billing_reason": "purchase",
        "billing_address": {
            "line1": "123 Main St",
            "line2": "Apt 4B",
            "postal_code": "12345",
            "city": "New York",
            "state": "NY",
            "country": "US"
        },
        "customer_id": "test-customer-456",
        "product_id": "test-product-789",
        "discount_id": "test-discount-101",
        "subscription_id": "test-subscription-112",
        "checkout_id": "test-checkout-131",
        "metadata": {},
        "custom_field_data": {},
        "customer": {
            "id": "test-customer-456",
            "created_at": "2023-11-07T05:31:56Z",
            "modified_at": "2023-11-07T05:31:56Z",
            "metadata": {},
            "external_id": "usr_1337",
            "email": "<EMAIL>",
            "email_verified": True,
            "name": "John Doe",
            "billing_address": {
                "line1": "123 Main St",
                "line2": "Apt 4B",
                "postal_code": "12345",
                "city": "New York",
                "state": "NY",
                "country": "US"
            },
            "tax_id": ["*********", "us_ein"],
            "organization_id": "1dbfc517-0bbf-4301-9ba8-555ca42b9737",
            "deleted_at": None,
            "avatar_url": "https://www.gravatar.com/avatar/xxx?d=blank"
        },
        "user_id": "test-user-151",
        "product": {
            "metadata": {},
            "created_at": "2023-11-07T05:31:56Z",
            "modified_at": "2023-11-07T05:31:56Z",
            "id": "test-product-789",
            "name": "Premium Plan",
            "description": "Premium subscription plan",
            "recurring_interval": "month",
            "is_recurring": True,
            "is_archived": False,
            "organization_id": "test-org-161"
        },
        "discount": {
            "duration": "once",
            "type": "fixed",
            "amount": 123,
            "currency": "USD",
            "created_at": "2023-11-07T05:31:56Z",
            "modified_at": "2023-11-07T05:31:56Z",
            "id": "test-discount-101",
            "metadata": {},
            "name": "Welcome Discount",
            "code": "WELCOME10",
            "starts_at": "2023-11-07T05:31:56Z",
            "ends_at": "2023-12-07T05:31:56Z",
            "max_redemptions": 100,
            "redemptions_count": 5,
            "organization_id": "1dbfc517-0bbf-4301-9ba8-555ca42b9737"
        },
        "subscription": {
            "metadata": {},
            "created_at": "2023-11-07T05:31:56Z",
            "modified_at": "2023-11-07T05:31:56Z",
            "id": "test-subscription-112",
            "amount": 123,
            "currency": "USD",
            "recurring_interval": "month",
            "status": "active",
            "current_period_start": "2023-11-07T05:31:56Z",
            "current_period_end": "2023-12-07T05:31:56Z",
            "cancel_at_period_end": False,
            "canceled_at": None,
            "started_at": "2023-11-07T05:31:56Z",
            "ends_at": None,
            "ended_at": None,
            "customer_id": "test-customer-456",
            "product_id": "test-product-789",
            "discount_id": "test-discount-101",
            "checkout_id": "test-checkout-131",
            "customer_cancellation_reason": None,
            "customer_cancellation_comment": None
        },
        "items": [
            {
                "created_at": "2023-11-07T05:31:56Z",
                "modified_at": "2023-11-07T05:31:56Z",
                "id": "test-item-171",
                "label": "Premium Plan - Monthly",
                "amount": 123,
                "tax_amount": 23,
                "proration": False,
                "product_price_id": "test-price-181"
            }
        ]
    }
}


def test_payload_structure():
    """Test that the sample payload has the expected structure."""
    print("Testing payload structure...")
    
    data = SAMPLE_WEBHOOK_PAYLOAD["data"]
    
    # Check required fields
    required_fields = ["id", "status", "customer", "product", "items"]
    for field in required_fields:
        assert field in data, f"Missing required field: {field}"
    
    # Check customer structure
    customer = data["customer"]
    assert "id" in customer, "Missing customer.id"
    assert "email" in customer, "Missing customer.email"
    
    # Check product structure
    product = data["product"]
    assert "id" in product, "Missing product.id"
    assert "name" in product, "Missing product.name"
    
    # Check items structure
    items = data["items"]
    assert isinstance(items, list), "Items should be a list"
    assert len(items) > 0, "Items list should not be empty"
    
    print("✓ Payload structure test passed")


def test_datetime_parsing():
    """Test datetime parsing functionality."""
    print("Testing datetime parsing...")
    
    test_datetime = "2023-11-07T05:31:56Z"
    parsed = datetime.fromisoformat(test_datetime.replace('Z', '+00:00'))
    
    assert isinstance(parsed, datetime), "Should parse to datetime object"
    print(f"✓ Parsed datetime: {parsed}")


if __name__ == "__main__":
    print("Running Polar Order tests...")
    test_payload_structure()
    test_datetime_parsing()
    print("All tests passed! ✓")
