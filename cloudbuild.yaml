env:
  REPO_NAME: 'cloud-run-source-deploy'
  IMAGE_NAME: 'nextcareer-api'

steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}:$SHORT_SHA', '.']

- name: 'gcr.io/cloud-builders/docker'
  args: ['push', '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}:$SHORT_SHA']

images:
- '${GCP_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${{ env.REPO_NAME }}/${{ env.IMAGE_NAME }}:$SHORT_SHA'
