---
description: 
globs: 
alwaysApply: true
---
You are an expert in Python, Sanic Framework, and TortoiseORM.

Key principles:
- Use Sanic Framework for web development.
- Use TortoiseORM for database operations.
- Use Redis for caching and session management.
- Use Docker for containerization.
- Use Github Actions for CI/CD pipelines.
- Create a RESTFul API server with Sanic Framework.
- Most of the response should be in JSON format. With required fields are:
  - `error`: value is an integer, 0 means no error, other numbers means error
  - `message`: a message to the user
  - `data`: a JSON object for the response data depending on the endpoint.
- Directory structure:
  - `next_api` is the root directory for application
  - `next_api/api`: contains the API endpoints
  - `next_api/models`: contains the database models
  - `next_api/repositories`: contains the database repositories
  - `next_api/utils`: contains the utility functions
  - `next_api/settings.py`: a module containing the settings for the application
- The data flow is: Model -> Repository -> API
- `migrations` directory is used for database migrations, contains SQL Files.
  - The prefix of the file is `dddddd` with `d` is a digit.
  - The file names should be `dddddd-do-something.sql`.
- PYTHONPATH is set to the `./next_api` directory.
- Use blueprints for the API endpoints.
- Use docstrings to define the OpenAPI specification for the API endpoints.
- We use Bruno as API client app, the docs for API are in `docs/api`

Dependencies:
- Use Python 3.12
- Use Sanic Framework version 24.6.0
- Use TortoiseORM version 0.21.0
- Utilize async/await syntax
- Utilize type hints
- Use PostgreSQL version 16
- Use Redis version 4.6.0
- Use Docker version 24.0.1
- Use Git version 2.42.0
- Use Auth0 as the authentication provider

About the project:
- The project uses AI to generate Resumes, Cover Letters for users.
- The project gives suggestions for the user to improve their resume and cover letter.
- The project generates questions based on user's resumes for user to answer as mock interview.
- The project uses OpenAI API for the AI generation.
