---
description: 
globs: 
alwaysApply: true
---
# General

1. In Python script, don't import unused members.
2. Format the files to follow PEP-8
3. If it is possible, use logging with lazy interpolation
4. AUTO Remove unused imports.
5. No trailing white spaces (Pylint: C0303)

# Order of import

1. Built-in modules.
2. 3rd party modules.
3. Project's modules.
4. `import` first, `from ... import ...` later.
5. Order of project's modules: models -> repository -> api -> utils.