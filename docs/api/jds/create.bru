meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/jds
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "description": "We are looking for a Python Developer to join our team. The ideal candidate will have experience with Django, PostgreSQL, and AWS. Responsibilities include developing and maintaining web applications, collaborating with cross-functional teams, and implementing best practices for code quality and security.",
    "position": "Senior Python Developer",
    "company": "Tech Corp",
    "city": "San Francisco",
    "country": "USA",
    "posted_date": "2024-03-20T00:00:00Z",
    "skills": ["Python", "Django", "PostgreSQL", "AWS", "Docker", "Kubernetes"],
    "status": "private",
    "contract_type": "full_time",
    "is_favorite": true,
    "promotion": "normal"
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("jd-id", record_id);
}
