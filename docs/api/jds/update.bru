meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/jds/{{jd-id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "description": "We are looking for an experienced Python Developer to join our growing team. The ideal candidate will have strong experience with Django, PostgreSQL, and AWS. Responsibilities include developing and maintaining scalable web applications, collaborating with cross-functional teams, and implementing best practices for code quality and security.",
    "position": "Senior Python Developer",
    "company": "Tech Corp",
    "city": "San Francisco",
    "country": "USA",
    "skills": ["Python", "Django", "PostgreSQL", "AWS", "Docker", "Kubernetes", "CI/CD"],
    "is_favorite": true
  }
}
