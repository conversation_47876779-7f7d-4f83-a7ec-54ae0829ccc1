meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/cover-letters
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "user_id": "{{user_id}}",
    "resume_id": "{{resume_id}}",
    "title": "Software Engineer Cover Letter",
    "content": "Dear Hiring Manager,\n\nI am writing to express my interest in the Software Engineer position at your company...",
    "target_position": ["Software Engineer", "Full Stack Developer"],
    "status": 2
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("cover-letter-id", record_id);
}
