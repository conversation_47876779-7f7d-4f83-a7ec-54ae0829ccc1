meta {
  name: ai/mock-interviews/generate
  type: http
  seq: 1
}

post {
  url: {{HTTP_API_HOST}}/ai/mock-interviews/generate
  body: json
  auth: bearer
}

headers {
  Content-Type: application/json
}

auth:bearer {
  token: eyJhbGciOiJQUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************.YZMkUTFXTOeL1fbEDyRl9C8su0n2lGCsLas8yhVgIlDMOEMMhf-8H5yEVldEPQL-s86DqlLbArD7VjbeWrKG_r7injxdqe1o88_wHhu2ZG0pVibh4wOL2zb5XW_tuP2DbYCuLlDpSCJ-BtITnmpMB6tOtg6k7UgauUbP6UolkqHvxO_sGC9hcx1-HkGd97RXZ0jlYxh2X4583dhd2QhPX6MkoE731qL53Eq9VWGWQp0o9msJinm-HjMDOljXNb0DpiHYUxwFSVyqtg2_DDi7LqPp1a3tlk_TMIr3mVNa_BlHX1DziM3Xj7NEgBnapi6NvrC0iRVajiFnN93Fnx8hdw
}

body:json {
  {
    "number_of_questions": "10",
    "resume_id": "6895b544-89c8-4d87-b6de-36de857b6c21",
    "job_description_id": "067a96d2-7484-7806-8000-f0aea3d3679f",
    "job_position": "prepare for momo"
  }
}
