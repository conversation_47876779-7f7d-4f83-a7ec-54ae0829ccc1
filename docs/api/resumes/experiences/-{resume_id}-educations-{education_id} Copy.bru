meta {
  name: /{resume_id}/educations/{education_id} Copy
  type: http
  seq: 3
}

put {
  url: {{HTTP_API_HOST}}/resumes/{{resume_id}}/experiences/{{experience_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "company": "Current company",
      "position": "Senior Software Engineer",
      "start_year": 2015,
      "start_month": 3,
      "end_year": 2017,
      "end_month": 10,
      "is_current": false
  }
}
