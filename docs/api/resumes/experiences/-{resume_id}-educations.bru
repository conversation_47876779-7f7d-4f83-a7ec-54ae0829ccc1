meta {
  name: /{resume_id}/educations
  type: http
  seq: 1
}

post {
  url: {{HTTP_API_HOST}}/resumes/{{resume_id}}/experiences
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "experiences": [
          {
              "company": "First company",
              "position": "Software Engineer",
              "start_year": 2012,
              "start_month": 11,
              "end_year": 2015,
              "end_month": 3,
              "is_current": false
          },
          {
              "company": "Current company",
              "position": "Senior Software Engineer",
              "start_year": 2015,
              "start_month": 3,
              "end_year": 2017,
              "end_month": 10,
              "is_current": false
          }
      ]
  }
}
