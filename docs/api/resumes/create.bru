meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/resumes
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "user_id": "{{user_id}}",
    "title": "Software Engineer Resume",
    "slug": "software-engineer-resume",
    "content": {
      "personal_info": {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+****************",
        "location": "San Francisco, CA"
      },
      "summary": "Experienced software engineer with expertise in Python, JavaScript, and cloud technologies.",
      "skills": ["Python", "JavaScript", "React", "Node.js", "AWS", "Docker", "Kubernetes"],
      "experience": [
        {
          "company": "Tech Company",
          "position": "Senior Software Engineer",
          "start_date": "2020-01",
          "end_date": "Present",
          "description": "Led development of microservices architecture using Python and AWS."
        },
        {
          "company": "Startup Inc.",
          "position": "Software Engineer",
          "start_date": "2017-06",
          "end_date": "2019-12",
          "description": "Developed and maintained web applications using React and Node.js."
        }
      ],
      "education": [
        {
          "institution": "University of Technology",
          "degree": "Bachelor of Science in Computer Science",
          "graduation_date": "2017-05"
        }
      ]
    },
    "keywords": ["software engineer", "python", "javascript", "aws", "cloud"],
    "status": 2
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("resume_id", record_id);
}
