meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/resumes/{{resume_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "title": "Senior Software Engineer Resume",
    "content": {
      "personal_info": {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+****************",
        "location": "San Francisco, CA",
        "linkedin": "linkedin.com/in/johndoe"
      },
      "summary": "Senior software engineer with 5+ years of experience in Python, JavaScript, and cloud technologies. Expertise in microservices architecture and distributed systems.",
      "skills": ["Python", "JavaScript", "React", "Node.js", "AWS", "Docker", "Kubernetes", "Microservices", "CI/CD"],
      "experience": [
        {
          "company": "Tech Company",
          "position": "Senior Software Engineer",
          "start_date": "2020-01",
          "end_date": "Present",
          "description": "Led development of microservices architecture using Python and AWS. Implemented CI/CD pipelines and improved system performance by 40%."
        },
        {
          "company": "Startup Inc.",
          "position": "Software Engineer",
          "start_date": "2017-06",
          "end_date": "2019-12",
          "description": "Developed and maintained web applications using React and Node.js. Collaborated with cross-functional teams to deliver features on time."
        }
      ],
      "education": [
        {
          "institution": "University of Technology",
          "degree": "Bachelor of Science in Computer Science",
          "graduation_date": "2017-05",
          "gpa": "3.8/4.0"
        }
      ]
    },
    "keywords": ["senior software engineer", "python", "javascript", "aws", "cloud", "microservices"],
    "status": 2
  }
}
