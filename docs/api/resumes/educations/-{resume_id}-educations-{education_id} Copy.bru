meta {
  name: /{resume_id}/educations/{education_id} Copy
  type: http
  seq: 3
}

put {
  url: {{HTTP_API_HOST}}/resumes/{{resume_id}}/educations/{{education_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "school": "HCMUS EDITED",
      "start_year": 2007,
      "start_month": 10,
      "end_year": 2012,
      "end_month": 9,
      "status": 1
  }
}
