meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/subscription-plans/{{subscription_plan_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Premium Plus Plan",
    "description": "Access to all premium features with higher limits",
    "price": 29.99,
    "currency": "USD",
    "interval": "month",
    "interval_count": 1,
    "trial_period_days": 7,
    "status": 2
  }
}
