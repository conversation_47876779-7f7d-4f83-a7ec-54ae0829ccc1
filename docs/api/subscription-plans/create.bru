meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/subscription-plans
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Premium Plan",
    "description": "Access to all premium features",
    "price": 19.99,
    "currency": "USD",
    "interval": "month",
    "interval_count": 1,
    "trial_period_days": 14,
    "status": 2,
    "features": [
      {
        "feature_id": "{{feature_id_1}}",
        "feature_value": {
          "max": 100
        }
      },
      {
        "feature_id": "{{feature_id_2}}",
        "feature_value": {
          "max": 50
        }
      }
    ]
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("subscription_plan_id", record_id);
}
