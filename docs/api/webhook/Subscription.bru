meta {
  name: Subscription
  type: http
  seq: 1
}

post {
  url: {{HTTP_API_HOST}}/webhooks/lemon-events/subscriptions
  body: json
  auth: none
}

headers {
  X-Signature: 520aee0f184939266ee1da17e7ea237a8b4a8df42f2966efd36bfe4d6e6f553b
  X-Event-Name: subscription_updated
  Content-Type: application/json
}

body:json {
  {
    "meta": {
      "test_mode": true,
      "event_name": "subscription_updated",
      "webhook_id": "81ed5718-9081-4ad4-b152-6f0150fc7ff6"
    },
    "data": {
      "type": "subscriptions",
      "id": "1083223",
      "attributes": {
        "store_id": 145294,
        "customer_id": 5403885,
        "order_id": 5162564,
        "order_item_id": 5102918,
        "product_id": 425973,
        "variant_id": 658250,
        "product_name": "Subscription plans",
        "variant_name": "Premium",
        "user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (MGD)",
        "user_email": "<EMAIL>",
        "status": "active",
        "status_formatted": "Active",
        "card_brand": "visa",
        "card_last_four": "4242",
        "pause": null,
        "cancelled": false,
        "trial_ends_at": null,
        "billing_anchor": 28,
        "first_subscription_item": {
          "id": 1412876,
          "subscription_id": 1083223,
          "price_id": 1075652,
          "quantity": 1,
          "is_usage_based": false,
          "created_at": "2025-03-28T21:57:40.000000Z",
          "updated_at": "2025-03-28T21:58:09.000000Z"
        },
        "urls": {
          "update_payment_method": "https://ducminhgd.lemonsqueezy.com/subscription/1083223/payment-details?expires=1743220690&signature=1d9dd2c2a1cb9866398d0bf7d7bffdcc10a693753e61e7938d785c287667e5ea",
          "customer_portal": "https://ducminhgd.lemonsqueezy.com/billing?expires=1743220690&test_mode=1&user=4634132&signature=5780ee4835ecfcf365c12c02b1095a132565520ff96de2889e12efcf1f104e2b",
          "customer_portal_update_subscription": "https://ducminhgd.lemonsqueezy.com/billing/1083223/update?expires=1743220690&user=4634132&signature=2f9b1ccc6ee51db316cf6c434d0478d8392aa733229bfc98d867f2cd71218b96"
        },
        "renews_at": "2025-04-28T21:57:33.000000Z",
        "ends_at": null,
        "created_at": "2025-03-28T21:57:34.000000Z",
        "updated_at": "2025-03-28T21:57:39.000000Z",
        "test_mode": true
      },
      "relationships": {
        "store": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/store",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/store"
          }
        },
        "customer": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/customer",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/customer"
          }
        },
        "order": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/order",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/order"
          }
        },
        "order-item": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/order-item",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/order-item"
          }
        },
        "product": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/product",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/product"
          }
        },
        "variant": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/variant",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/variant"
          }
        },
        "subscription-items": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/subscription-items",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/subscription-items"
          }
        },
        "subscription-invoices": {
          "links": {
            "related": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/subscription-invoices",
            "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223/relationships/subscription-invoices"
          }
        }
      },
      "links": {
        "self": "https://api.lemonsqueezy.com/v1/subscriptions/1083223"
      }
    }
  }
}
