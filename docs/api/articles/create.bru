meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/articles
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "title": "How to Ace Your Next Job Interview",
    "content": "In today's competitive job market, preparing for your interview is crucial...",
    "banner_image_url": "https://example.com/images/interview-tips-banner.jpg",
    "author": "<PERSON>",
    "tags": ["interview", "career advice", "job search"],
    "published_at": "2024-03-21T10:00:00Z"
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("article-id", record_id);
}
