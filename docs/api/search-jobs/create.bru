meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/search-jobs
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "title": "Software Engineer",
    "company": "Google",
    "location": "Mountain View, CA",
    "salary": "120000.00",
    "description": "We are looking for a Software Engineer to join our team. The ideal candidate will have experience with Python, JavaScript, and cloud technologies.",
    "link": "https://careers.google.com/jobs/12345",
    "posted_at": "2023-06-15T10:00:00Z",
    "is_remote": false,
    "source": "Google Careers",
    "category": "Engineering",
    "keywords": ["software engineer", "python", "javascript", "cloud"]
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("search-job-id", record_id);
}
