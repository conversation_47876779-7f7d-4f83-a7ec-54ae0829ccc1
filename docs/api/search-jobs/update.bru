meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/search-jobs/{{search-job-id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "title": "Senior Software Engineer 2",
    "company": "Google",
    "location": "Mountain View, CA",
    "salary": 140000.00,
    "description": "We are looking for a Senior Software Engineer to join our team. The ideal candidate will have extensive experience with Python, JavaScript, and cloud technologies.",
    "link": "https://careers.google.com/jobs/12345",
    "posted_at": "2023-06-15T10:00:00Z",
    "is_remote": true,
    "source": "Google Careers",
    "category": "Engineering",
    "keywords": ["senior software engineer", "python", "javascript", "cloud"]
  }
}
