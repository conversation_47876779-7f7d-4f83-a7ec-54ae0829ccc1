meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/mock-interviews
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "user_id": "{{user_id}}",
    "position": "Senior Software Engineer",
    "job_description": "We are looking for an experienced software engineer with strong skills in Python, cloud technologies, and microservices architecture. The ideal candidate will have experience with AWS, Docker, and Kubernetes.",
    "job_description_id": "{{jd-id}}",
    "status": 2
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("mock-interview-id", record_id);
}
