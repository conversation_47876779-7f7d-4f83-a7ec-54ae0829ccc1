meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/mock-interviews/{{mock-interview-id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "position": "Lead Software Engineer",
    "job_description": "We are looking for a Lead Software Engineer with extensive experience in Python, cloud technologies, and microservices architecture. The ideal candidate will have proven leadership skills and experience with AWS, Docker, and Kubernetes.",
    "status": 2
  }
}
