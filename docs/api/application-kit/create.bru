meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/application-kit
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "user_id": "{{user_id}}",
      "jd_id": "{{jd-id}}",
      "resume_id": "{{resume_id}}",
      "cover_letter_id": "{{cover-letter-id}}",
      "follow_up_id": "{{follow-up-letters-id}}",
      "mock_interview_id": "{{mock_interview_id}}",
      "job_insight_id": "{{job-insights-id}}"
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("application-kit-id", record_id);
}
