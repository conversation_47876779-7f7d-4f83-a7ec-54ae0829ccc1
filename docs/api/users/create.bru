meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/users
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "email": "<EMAIL>",
    "fullname": "<PERSON>",
    "password": "securepassword123",
    "min_salary": 80000.00,
    "expected_salary": 100000.00,
    "exp_level": 50,
    "linked_in": "linkedin.com/in/johndoe",
    "locale": "en-US",
    "profession": ["Software Engineer", "Web Developer"],
    "location": ["San Francisco, CA", "Remote"],
    "status": 3
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("new_user_id", record_id);
}
