meta {
  name: /_1
  type: http
  seq: 8
}

post {
  url: {{HTTP_API_HOST}}/users
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "username": "ducminhgd222",
      "fullname": "ducminhgd",
      "password": "ee10c315eba2c75b403ea99136f5b48d",
      "min_salary": 1000.00,
      "expected_salary": null,
      "exp_level": 30,
      "status": 1
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("user_id", record_id);
}
