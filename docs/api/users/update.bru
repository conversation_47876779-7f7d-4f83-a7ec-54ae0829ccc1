meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/users/{{user_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "fullname": "<PERSON>",
    "min_salary": 90000.00,
    "expected_salary": 120000.00,
    "exp_level": 60,
    "linked_in": "linkedin.com/in/johnadoe",
    "profession": ["Senior Software Engineer", "Web Developer"],
    "location": ["San Francisco, CA", "Remote"]
  }
}
