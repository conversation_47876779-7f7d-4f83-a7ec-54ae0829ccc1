meta {
  name: Get Google Email
  type: http
  seq: 1
}

get {
  url: https://people.googleapis.com/v1/people/me?personFields=names,emailAddresses
  body: none
  auth: bearer
}

params:query {
  personFields: names,emailAddresses
}

auth:bearer {
  token: ********************************************************************************************************************************************************************************************************************************
}
