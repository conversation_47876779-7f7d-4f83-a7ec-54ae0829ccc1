meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/job-insights
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "jd_id": "{{jd-id}}",
      "resume_id": "{{resume_id}}",
      "job_title": "Senior Software Engineer",
      "overview": "This position requires an experienced software engineer with expertise in cloud technologies and microservices architecture.",
      "core_skills": [
          {
              "name": "Python",
              "level": "Expert",
              "years_required": 5,
              "importance": "Critical"
          },
          {
              "name": "Kubernetes",
              "level": "Advanced",
              "years_required": 3,
              "importance": "High"
          }
      ],
      "trending_skills": [
          {
              "name": "Rust",
              "growth_rate": "High",
              "market_demand": "Increasing",
              "learning_resources": ["Official Rust Book", "Rust by Example"]
          },
          {
              "name": "WebAssembly",
              "growth_rate": "Medium",
              "market_demand": "Steady",
              "learning_resources": ["MDN Web Docs", "WebAssembly.org"]
          }
      ],
      "soft_skills": [
          {
              "name": "Communication",
              "importance": "High",
              "description": "Ability to clearly communicate technical concepts to non-technical stakeholders"
          },
          {
              "name": "Leadership",
              "importance": "Medium",
              "description": "Experience leading small teams and mentoring junior developers"
          }
      ],
      "professional_courses": [
          {
              "name": "AWS Certified Solutions Architect",
              "provider": "Amazon Web Services",
              "duration": "3 months",
              "cost": "$150",
              "value": "High"
          },
          {
              "name": "Kubernetes Certified Administrator",
              "provider": "Cloud Native Computing Foundation",
              "duration": "2 months",
              "cost": "$300",
              "value": "High"
          }
      ],
      "certifications": [
          {
              "name": "AWS Certified Developer",
              "issuer": "Amazon Web Services",
              "validity": "3 years",
              "difficulty": "Medium",
              "value": "High"
          },
          {
              "name": "Certified Kubernetes Application Developer",
              "issuer": "Cloud Native Computing Foundation",
              "validity": "2 years",
              "difficulty": "High",
              "value": "High"
          }
      ],
      "projects": [
          {
              "name": "Microservices Migration",
              "description": "Lead the migration of monolithic application to microservices architecture",
              "duration": "6-8 months",
              "skills_required": ["Kubernetes", "Docker", "AWS", "Python"],
              "complexity": "High"
          },
          {
              "name": "CI/CD Pipeline Optimization",
              "description": "Implement and optimize automated deployment pipelines",
              "duration": "3-4 months",
              "skills_required": ["Jenkins", "GitOps", "Terraform"],
              "complexity": "Medium"
          }
      ],
      "expected_salary": {
          "min": 120000,
          "max": 180000,
          "currency": "USD",
          "period": "yearly",
          "benefits": [
              "Health insurance",
              "401k matching",
              "Remote work options",
              "Professional development budget"
          ],
          "market_rate": "Competitive"
      },
      "advises": [
          "Focus on cloud-native development experience",
          "Highlight system design and architecture experience",
          "Emphasize team leadership and mentoring experience",
          "Showcase successful microservices projects"
      ],
      "other_insights": [
          "Company values innovation and continuous learning",
          "Team is distributed across multiple time zones",
          "Strong emphasis on code quality and testing",
          "Regular opportunities for technical presentations"
      ]
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("job-insights-id", record_id);
}
