meta {
  name: /{{job-insights-id}
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/job-insights/{{job-insights-id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "jd_id": "{{jd-id}}",
      "resume_id": "{{resume_id}}",
      "job_title": "Senior Software Engineer",
      "overview": "This position requires a skilled software engineer with expertise in cloud computing, distributed systems, and modern web technologies. The role involves designing and implementing scalable solutions while mentoring junior developers.",
      "core_skills": [
          {
              "name": "Python",
              "level": "Expert",
              "years_of_experience": 5,
              "is_required": true
          },
          {
              "name": "Cloud Computing (AWS)",
              "level": "Advanced",
              "years_of_experience": 3,
              "is_required": true
          },
          {
              "name": "Microservices Architecture",
              "level": "Advanced",
              "years_of_experience": 3,
              "is_required": true
          }
      ],
      "trending_skills": [
          {
              "name": "Kubernetes",
              "popularity": "High",
              "market_demand": "Growing",
              "learning_resources": ["Kubernetes Documentation", "Cloud Native Foundation Courses"]
          },
          {
              "name": "Machine Learning",
              "popularity": "High",
              "market_demand": "Very High",
              "learning_resources": ["TensorFlow Tutorials", "Fast.ai Course"]
          }
      ],
      "soft_skills": [
          {
              "name": "Leadership",
              "importance": "High",
              "description": "Ability to lead and mentor team members"
          },
          {
              "name": "Communication",
              "importance": "Critical",
              "description": "Strong verbal and written communication skills"
          }
      ],
      "professional_courses": [
          {
              "name": "AWS Solutions Architect",
              "provider": "Amazon Web Services",
              "duration": "6 months",
              "cost": "$300",
              "relevance": "High"
          },
          {
              "name": "System Design for Senior Engineers",
              "provider": "Educative.io",
              "duration": "3 months",
              "cost": "$200",
              "relevance": "High"
          }
      ],
      "certifications": [
          {
              "name": "AWS Certified Solutions Architect - Professional",
              "issuer": "Amazon Web Services",
              "validity": "3 years",
              "estimated_cost": "$300",
              "priority": "High"
          },
          {
              "name": "Certified Kubernetes Administrator (CKA)",
              "issuer": "Cloud Native Computing Foundation",
              "validity": "2 years",
              "estimated_cost": "$375",
              "priority": "Medium"
          }
      ],
      "projects": [
          {
              "name": "Microservices Migration",
              "description": "Lead the migration of monolithic application to microservices architecture",
              "duration": "6-8 months",
              "skills_required": ["Kubernetes", "Docker", "AWS", "Python"],
              "complexity": "High"
          },
          {
              "name": "CI/CD Pipeline Optimization",
              "description": "Implement and optimize automated deployment pipelines",
              "duration": "3-4 months",
              "skills_required": ["Jenkins", "GitOps", "Terraform"],
              "complexity": "Medium"
          }
      ],
      "expected_salary": {
          "min": 120000,
          "max": 180000,
          "currency": "USD",
          "period": "yearly",
          "benefits": [
              "Health insurance",
              "401k matching",
              "Remote work options",
              "Professional development budget"
          ],
          "market_rate": "Competitive"
      },
      "advises": [
          "Focus on cloud-native development experience",
          "Highlight system design and architecture experience",
          "Emphasize team leadership and mentoring experience",
          "Showcase successful microservices projects"
      ],
      "other_insights": [
          "Company values innovation and continuous learning",
          "Team is distributed across multiple time zones",
          "Strong emphasis on code quality and testing",
          "Regular opportunities for technical presentations"
      ]
  }
}
