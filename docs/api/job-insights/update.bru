meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/job-insights/{{job-insights-id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
      "job_title": "Senior Software Engineer - Cloud Infrastructure",
      "overview": "This position requires an experienced software engineer with deep expertise in cloud technologies, microservices architecture, and distributed systems.",
      "core_skills": [
          {
              "name": "Python",
              "level": "Expert",
              "years_required": 5,
              "importance": "Critical"
          },
          {
              "name": "Kubernetes",
              "level": "Expert",
              "years_required": 4,
              "importance": "Critical"
          },
          {
              "name": "AWS",
              "level": "Advanced",
              "years_required": 3,
              "importance": "High"
          }
      ],
      "advises": [
          "Focus on cloud-native development experience",
          "Highlight system design and architecture experience",
          "Emphasize team leadership and mentoring experience",
          "Showcase successful microservices projects",
          "Demonstrate experience with distributed systems"
      ],
      "other_insights": [
          "Company values innovation and continuous learning",
          "Team is distributed across multiple time zones",
          "Strong emphasis on code quality and testing",
          "Regular opportunities for technical presentations",
          "Growing focus on infrastructure as code practices"
      ]
  }
}
