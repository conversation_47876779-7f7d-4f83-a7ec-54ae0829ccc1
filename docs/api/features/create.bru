meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/features
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "code": "RESUME_AI_BUILDER",
    "name": "Generate resumes with AI",
    "description": "Generate professional resumes with AI assistance",
    "default_value": {
      "max": 10
    },
    "visibility": 2
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("feature_id", record_id);
}
