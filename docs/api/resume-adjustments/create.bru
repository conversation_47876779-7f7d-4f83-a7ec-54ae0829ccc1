meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/resume-adjustments
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "resume_id": "{{resume_id}}",
    "job_title": "Senior Software Engineer",
    "educations": [
      {
        "degree": "Master of Science",
        "field": "Computer Science",
        "school": "Stanford University",
        "start_date": "2018-09",
        "end_date": "2020-06",
        "description": "Focus on Machine Learning and Artificial Intelligence"
      }
    ],
    "skills": [
      {
        "name": "Python",
        "level": "Expert",
        "years": 5
      },
      {
        "name": "Machine Learning",
        "level": "Advanced",
        "years": 3
      },
      {
        "name": "Docker",
        "level": "Intermediate",
        "years": 2
      }
    ],
    "experiences": [
      {
        "company": "Tech Corp",
        "position": "Software Engineer",
        "start_date": "2020-07",
        "end_date": "2023-12",
        "description": "Led development of microservices architecture using Python and Docker",
        "achievements": [
          "Improved system performance by 40%",
          "Implemented CI/CD pipeline reducing deployment time by 60%"
        ]
      }
    ],
    "status": 1
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("resume-adjustment-id", record_id);
}
