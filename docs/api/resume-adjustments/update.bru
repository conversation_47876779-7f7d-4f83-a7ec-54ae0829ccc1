meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/resume-adjustments/{{resume-adjustment-id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "job_title": "Senior Software Engineer - Machine Learning",
    "skills": [
      {
        "name": "Python",
        "level": "Expert",
        "years": 5
      },
      {
        "name": "Machine Learning",
        "level": "Expert",
        "years": 4
      },
      {
        "name": "Docker",
        "level": "Advanced",
        "years": 3
      },
      {
        "name": "TensorFlow",
        "level": "Advanced",
        "years": 3
      }
    ],
    "status": 2
  }
}
