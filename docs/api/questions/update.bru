meta {
  name: Update
  type: http
  seq: 4
}

put {
  url: {{HTTP_API_HOST}}/questions/{{question_id}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "question": "What are the key differences between Python 2 and Python 3?",
    "answer": "Key differences include: print is a function in Python 3, division operator behavior changes (/ performs true division), Unicode string handling (all strings are Unicode in Python 3), exception handling syntax changes, range vs xrange (range in Python 3 behaves like xrange in Python 2), and more.",
    "hint": "Think about syntax changes, performance improvements, and string handling",
    "difficulty": 2,
    "status": 2
  }
}
