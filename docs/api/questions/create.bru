meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/questions
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "question": "What are the key differences between Python 2 and Python 3?",
    "answer": "Key differences include: print is a function in Python 3, division operator behavior changes, Unicode string handling, exception handling syntax, range vs xrange, and more.",
    "hint": "Think about syntax changes and performance improvements",
    "difficulty": 1,
    "status": 2
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("question_id", record_id);
}
