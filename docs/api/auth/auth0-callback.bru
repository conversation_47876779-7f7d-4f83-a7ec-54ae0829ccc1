meta {
  name: Auth0 callback
  type: http
  seq: 6
}

get {
  url: {{HTTP_API_HOST}}/auth/auth0/callback
  body: none
  auth: none
}

params:query {
  code: authorization_code_from_auth0
}

tests {
  var jsonData = res.getBody();
  if (jsonData.data.access_token) {
    bru.setEnvVar("access_token", jsonData.data.access_token);    
  }
  if (jsonData.data.refresh_token) {
    bru.setEnvVar("refresh_token", jsonData.data.refresh_token);
  }
}
