meta {
  name: Refresh token
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/auth/refresh-token
  body: none
  auth: bearer
}

auth:bearer {
  token: {{refresh_token}}
}

tests {
  var jsonData = res.getBody();
  if (jsonData.data.access_token) {
    bru.setEnvVar("access_token", jsonData.data.access_token);    
  }
  if (jsonData.data.refresh_token) {
    bru.setEnvVar("refresh_token", jsonData.data.refresh_token);
  }
}
