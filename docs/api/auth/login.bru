meta {
  name: Login
  type: http
  seq: 2
}

post {
  url: {{HTTP_API_HOST}}/auth/login
  body: json
  auth: none
}

body:json {
  {
      "email": "<EMAIL>",
      "password": "W312345678"
  }
}

script:post-response {
  var jsonData = res.getBody();
  if (jsonData.data.access_token) {
    bru.setEnvVar("access_token", jsonData.data.access_token);    
  }
  if (jsonData.data.refresh_token) {
    bru.setEnvVar("refresh_token", jsonData.data.refresh_token);
  }
}
