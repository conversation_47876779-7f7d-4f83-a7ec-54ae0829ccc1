meta {
  name: Create
  type: http
  seq: 3
}

post {
  url: {{HTTP_API_HOST}}/follow-up-letters
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "user_id": "{{user_id}}",
    "jd_id": "{{jd-id}}",
    "resume_id": "{{resume_id}}",
    "follow_up": "Dear [Hiring Manager],\n\nI hope this email finds you well. I am writing to follow up on my application for the [Position] role at [Company Name] that I submitted on [Date].\n\nI remain very interested in the opportunity to join your team and contribute to [Company Name]'s success. My experience in [relevant skills/experience] aligns well with the requirements outlined in the job description, and I am confident that I can make valuable contributions to your team.\n\nPlease let me know if you need any additional information from me or if there are any next steps in the process.\n\nThank you for considering my application. I look forward to hearing from you.\n\nBest regards,\n[Your Name]",
    "customization_note": "This follow-up letter emphasizes my relevant experience and maintains a professional yet enthusiastic tone. Key points customized: position name, company name, submission date, and specific skills matching the job requirements."
  }
}

tests {
  var jsonData = res.getBody();
  record_id = jsonData.data.id;
  bru.setEnvVar("follow-up-letter-id", record_id);
}
