# Contributing

## Requirements

- Python 3.12
- <PERSON><PERSON>
- <PERSON><PERSON>ise ORM
- PostgreSQL

## Installation

1. Install requirements

    ```bash
    pip install -U pip
    pip install -r requirements.txt
    ```

2. Install developer tools

    ```bash
    pip install -r requirements-dev.txt
    ```

## Start project for development

1. Start PostgreSQL: `docker compose up -d postgresql`
2. Start Redis: `docker compose up -d redis`
3. Start application:
   1. With VSCode: start using **Python Debugger: API**, `.env` is required, you can disable it in `.vscode/launch.json`.
   2. With command line: `python -m sanic next_api.api.main:create_app() --host=127.0.0.1 --port=8000 --dev`

## Project layout

1. `deployments`: insfrastructure or script for deployment or development
2. `migrations`: database scripts.
3. `next_api`:
   1. `api`: the API application, its endpoints.
   2. `models`: ORMs for database
   3. `repositories`: for getting data, parsing data, and updating data.
   4. `db.py`: database helpers.
   5. `helpers.py`: helper functions
   6. `settings.py`: defined constants or settings for the project.

## Principles

1. There should be the module `use_cases` to define the logics, but it seems like there is no complicated logic, so they can be defined in `next_api.repositories`.
2. `next_api.api` is an interface to receive the requests, to process them by using `repositories` and to response to requesters. If there is any background job, it is considered as an interface, and should be defined in `next_api.job`.