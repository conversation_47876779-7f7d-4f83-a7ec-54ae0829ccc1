{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "env": {
                "PYTHONPATH": "${workspaceFolder}/next_api"
            },
            "envFile": "${workspaceFolder}/.env",
            "justMyCode": false,
            "console": "integratedTerminal",
            "postDebugTask": "Run pyclean"
        },
        {
            "name": "Python: API",
            "type": "debugpy",
            "request": "launch",
            "module": "sanic",
            "args": [
                "next_api.api.main:create_app()",
                "--host=127.0.0.1",
                "--port=8000",
                "--debug",
                "--dev"
            ],
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}/next_api"
            },
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal",
            "postDebugTask": "Run pyclean"
        },
        {
            "name": "Python: API (no reload)",
            "type": "debugpy",
            "request": "launch",
            "module": "sanic",
            "args": [
                "next_api.api.main:create_app()",
                "--host=127.0.0.1",
                "--port=8000"
            ],
            "justMyCode": false,
            "env": {
                "PYTHONPATH": "${workspaceFolder}/next_api"
            },
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal",
            "postDebugTask": "Run pyclean"
        }
    ]
}