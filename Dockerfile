FROM python:3.12-slim AS python-builder

WORKDIR /install
COPY requirements.txt ./
RUN apt-get update && apt-get install -y gcc libssl-dev g++ make git build-essential \
    && pip install --upgrade pip \
    && pip install --upgrade pyclean \
    && apt-get clean autoclean && apt-get autoremove --yes && rm -rf /var/lib/{apt,dpkg,cache,log}/
RUN pip install --prefix=/install -r requirements.txt && pyclean .

FROM python:3.12-slim

WORKDIR /app
COPY --from=python-builder /install /usr/local
ADD ./next_api /app/
ENV PYTHONPATH="/app/"
EXPOSE 8000

CMD ["python", "-m", "sanic", "api.main:create_app", "--host=0.0.0.0", "--port=8000"]