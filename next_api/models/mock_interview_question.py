from tortoise.models import Model
from tortoise.fields import UUIDField, DatetimeField, ForeignKeyField, ForeignKeyRelation
from uuid_extensions import uuid7


class MockInterviewQuestion(Model):
    class Meta:
        table = "mock_interview_question"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    mock_interview: ForeignKeyRelation["MockInterview"] = ForeignKeyField(
        "next_api.MockInterview", related_name="mock_interview_questions"
    )
    question: ForeignKeyRelation["Question"] = ForeignKeyField(
        "next_api.Question", related_name="mock_interview_questions"
    )
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
