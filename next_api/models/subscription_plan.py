from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import (
    UUIDField, TextField, SmallIntField, DatetimeField, DecimalField,
    BooleanField, ManyToManyRelation, IntField, JSONField, CharField,
)
from uuid_extensions import uuid7


class SubscriptionPlan(Model):
    class Meta:
        table = "subscription_plan"
        schema = "public"
        ordering = ["sort_order", "created_at"]

    class Status(IntEnum):
        INACTIVE = 0
        ACTIVE = 1
        DEPRECATED = 2

    class BillingCycle(IntEnum):
        UNKNOWN = 0
        MONTHLY = 1
        QUARTERLY = 3
        SEMI_ANNUAL = 6
        ANNUAL = 12
        BI_ANNUAL = 24
        TRI_ANNUAL = 36
        LIFETIME = 9999

    id = UUIDField(pk=True, default=uuid7)
    code = CharField(unique=True, max_length=500)
    name = TextField()
    price = DecimalField(max_digits=18, decimal_places=2, default=0)
    currency = TextField(default="USD")
    billing_cycle = SmallIntField(null=False, default=BillingCycle.UNKNOWN)
    is_auto_renewable = <PERSON>oleanField(default=False)
    description = TextField(null=True)
    trial_period_days = IntField(default=0)
    sort_order = IntField(default=0)
    is_public = BooleanField(default=True)
    metadata = JSONField(null=True)
    status = SmallIntField(null=False, default=Status.INACTIVE)
    lemon_variant_id = IntField(null=True)
    polar_product_id = TextField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    mapped_features: ManyToManyRelation["SubscriptionPlanFeature"]
    subscribers: ManyToManyRelation["UserSubscription"]
