from tortoise.models import Model
from tortoise.fields import (
    UUIDField, TextField, DatetimeField, JSONField,
    ForeignKeyField, ForeignKeyRelation, OnDelete
)
from tortoise.contrib.postgres.fields import ArrayField
from uuid_extensions import uuid7


class JobInsights(Model):
    class Meta:
        table = "job_insights"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    jd: ForeignKeyRelation["JobDescription"] = ForeignKeyField(
        "next_api.JobDescription", related_name="job_insights", on_delete=OnDelete.CASCADE
    )
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="job_insights", null=True, on_delete=OnDelete.SET_NULL
    )
    job_title = TextField()
    overview = TextField(null=True)
    core_skills = JSONField(default=[])
    trending_skills = JSONField(default=[])
    soft_skills = JSONField(default=[])
    professional_courses = <PERSON><PERSON><PERSON>ield(default=[])
    certifications = JSONField(default=[])
    projects = JSONField(default=[])
    expected_salary = JSONField(default={})
    advises = ArrayField(element_type="text", default=list)
    other_insights = ArrayField(element_type="text", default=list)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
