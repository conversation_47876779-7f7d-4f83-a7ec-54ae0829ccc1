from tortoise.models import Model
from tortoise.fields import (
    UUIDField,
    TextField,
    DecimalField,
    BooleanField,
    DatetimeField,
)
from tortoise.contrib.postgres.fields import ArrayField
from uuid_extensions import uuid7


class SearchJob(Model):
    class Meta:
        table = "search_job"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    title = TextField()
    company = TextField()
    location = TextField(null=True)
    salary = DecimalField(max_digits=18, decimal_places=2, null=True)
    description = TextField(null=True)
    link = TextField(null=True)
    posted_at = TextField(null=True)
    is_remote = BooleanField(default=False)
    source = TextField()
    category = TextField(null=True)
    keywords = ArrayField(element_type="text", default=[])
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(null=True)

    def __str__(self):
        return f"{self.title} at {self.company}"
