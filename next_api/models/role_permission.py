from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import <PERSON><PERSON><PERSON>ield, TextField, SmallIntField, \
    DatetimeField, ManyToManyRelation, ManyToManyField
from uuid_extensions import uuid7


class Role(Model):

    class Meta:
        table = "role"
        ordering = ["created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        INACTIVE = 1
        ACTIVE = 2

    id = UUIDField(pk=True, default=uuid7)
    name = TextField(null=False)
    description = TextField(null=True)
    status = SmallIntField(default=Status.ACTIVE)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    users: ManyToManyRelation["User"] = ManyToManyField(
        "next_api.User", related_name="roles", through="role_user"
    )
    permissions: ManyToManyRelation["Permission"] = ManyToManyField(
        "next_api.Permission", related_name="roles", through="role_permission"
    )


class Permission(Model):

    class Meta:
        table = "permission"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    name = TextField(null=False)
    description = TextField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    roles: ManyToManyRelation["Role"]
