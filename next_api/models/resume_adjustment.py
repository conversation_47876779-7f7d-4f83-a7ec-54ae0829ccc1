from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import (
    UUIDField,
    TextField,
    JSONField,
    SmallIntField,
    DatetimeField,
    ForeignKeyField,
    ForeignKeyRelation
)
from uuid_extensions import uuid7


class ResumeAdjustment(Model):
    class Meta:
        table = "resume_adjustment"
        ordering = ["created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        SUGGESTED = 1
        ACCEPTED = 2
        REJECTED = 3

    id = UUIDField(pk=True, default=uuid7)
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="adjustments"
    )
    job_title = TextField()
    educations = JSONField(null=False, default=[])
    skills = JSONField(null=False, default=[])
    experiences = JSONField(null=False, default=[])
    status = SmallIntField(default=Status.SUGGESTED)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(null=True)
