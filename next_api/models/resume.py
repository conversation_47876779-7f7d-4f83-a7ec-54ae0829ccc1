from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import <PERSON><PERSON><PERSON>ield, TextField, BooleanField, \
    ForeignKeyRelation, ForeignKeyField, \
    SmallIntField, DatetimeField, ReverseRelation,JSONField, FloatField
from tortoise.contrib.postgres.fields import <PERSON>rray<PERSON>ield
from tortoise.signals import pre_save
from uuid_extensions import uuid7


class Resume(Model):

    class Meta:
        table = "resume"
        ordering = ["created_at"]
        unique_together = (("user_id", "slug"),)

    class Status(IntEnum):
        UNKNOWN = 0
        INACTIVE = 1
        PRIVATE = 2
        PUBLIC = 3

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField("next_api.User")
    title = TextField()
    slug = TextField(null=False, default="")
    content = JSONField(null=False, default={})
    keywords = ArrayField(element_type="TEXT", null=False, default=list)
    status = SmallIntField(default=Status.PRIVATE)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
    
    # ATS scoring fields
    ats_score = SmallIntField(null=True)  # Overall ATS score (0-100)
    keyword_score = FloatField(null=True)  # Score for keyword matching
    structure_score = FloatField(null=True)  # Score for resume structure
    content_quality_score = FloatField(null=True)  # Score for content quality

    cover_letters: ReverseRelation["CoverLetter"]
    # experiences: ReverseRelation["ResumeExperience"]
    # educations: ReverseRelation["ResumeEducation"]
    adjustments: ReverseRelation["ResumeAdjustment"]


@pre_save(Resume)
async def resume_pre_save(
    sender: "Type[Signal]", instance: Resume, using_db, update_fields  # pylint:disable=unused-argument
) -> None:
    if instance.keywords is None:
        instance.keywords = []


class ResumeEducation(Model):
    class Meta:
        table = "resume_education"
        ordering = ["-start_year", "-start_month"]

    class Status(IntEnum):
        UNKNOWN = 0
        HIDDEN = 1
        SHOW = 2

    id = UUIDField(pk=True, default=uuid7)
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="educations")
    school = TextField()
    degree = TextField()
    field_of_study = TextField()
    start_year = SmallIntField()
    start_month = SmallIntField()
    end_year = SmallIntField(null=True)
    end_month = SmallIntField(null=True)
    grade = TextField(null=True)
    activities_and_societies = TextField(null=True)
    description = TextField(null=True)
    status = SmallIntField(default=Status.SHOW, null=False)


class ResumeExperience(Model):
    class Meta:
        table = "resume_experience"
        ordering = ["-start_year", "-start_month"]

    class Status(IntEnum):
        UNKNOWN = 0
        HIDDEN = 1
        SHOW = 2

    class EmploymentType(IntEnum):
        UNKNOWN = 0
        FULL_TIME = 1
        PART_TIME = 2
        CONTRACT = 3
        INTERNSHIP = 4
        FREELANCE = 5

    class LocationType(IntEnum):
        UNKNOWN = 0
        ON_SITE = 1
        HYBRID = 2
        REMOTE = 3

    id = UUIDField(pk=True, default=uuid7)
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="experiences")
    company = TextField()
    position = TextField()
    employment_type = SmallIntField(null=False, default=EmploymentType.UNKNOWN)
    location = TextField()
    location_type = SmallIntField(null=False, default=LocationType.UNKNOWN)
    start_year = SmallIntField()
    start_month = SmallIntField()
    end_year = SmallIntField(null=True)
    end_month = SmallIntField(null=True)
    is_current = BooleanField()
    description = TextField(null=True)
    status = SmallIntField(default=Status.SHOW, null=False)
