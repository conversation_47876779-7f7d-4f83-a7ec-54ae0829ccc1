from tortoise.models import Model
from tortoise.fields import (
    UUIDField, TextField, DatetimeField, IntField,
    ForeignKeyField, ForeignKeyRelation,
    ReverseRelation, OnDelete
)
from tortoise.contrib.postgres.fields import ArrayField
from uuid_extensions import uuid7


class Article(Model):
    """
    Model representing an article in the system.
    """
    class Meta:
        table = "article"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    title = TextField()
    content = TextField()
    banner_image_url = TextField(null=True)
    author = TextField()
    published_at = DatetimeField(null=True)
    likes_count = IntField(default=0)
    tags = ArrayField(element_type="TEXT", null=False, default=list)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    # Reverse relations
    likes: ReverseRelation["ArticleLike"]


class ArticleLike(Model):
    """
    Model representing likes on articles.
    """
    class Meta:
        table = "article_like"
        schema = "public"
        ordering = ["created_at"]
        unique_together = (("article_id", "user_id"),)

    id = UUIDField(pk=True, default=uuid7)
    article: ForeignKeyRelation[Article] = ForeignKeyField(
        "next_api.Article", related_name="likes", on_delete=OnDelete.CASCADE
    )
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="article_likes", on_delete=OnDelete.CASCADE
    )
    created_at = DatetimeField(auto_now_add=True)
