from datetime import datetime, UTC
from enum import Enum
from tortoise import fields
from tortoise.models import Model
from uuid_extensions import uuid7


class Verification(Model):

    class Meta:
        table = "verification"
        schema = "public"

    class Type(str, Enum):
        ACTIVATION = "activation"
        FORGOT_PASSWORD = "forgot_password"
        RESET_PASSWORD = "reset_password"

    id = fields.UUIDField(pk=True, default=uuid7)
    user_id = fields.UUIDField()
    secret = fields.CharField(max_length=64)
    verification_type = fields.CharEnumField(Type)
    is_used = fields.BooleanField(default=False)
    created_at = fields.DatetimeField(auto_now_add=True)
    expired_at = fields.DatetimeField()

    @property
    def is_expired(self) -> bool:
        return datetime.now(UTC) > self.expired_at
