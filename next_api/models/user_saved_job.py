from tortoise.models import Model
from tortoise.fields import (
    UUIDField,
    ForeignKeyField,
    DatetimeField,
    ForeignKeyRelation
)
from uuid_extensions import uuid7


class UserSavedJob(Model):
    class Meta:
        table = "user_saved_job"
        schema = "public"
        ordering = ["created_at"]
        unique_together = (("user", "job"),)

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User",
        related_name="saved_jobs"
    )
    job: ForeignKeyRelation["SearchJob"] = ForeignKeyField(
        "next_api.SearchJob",
        related_name="saved_by_users"
    )
    created_at = DatetimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user} saved {self.job}" 