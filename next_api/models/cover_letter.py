from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import UUIDField, TextField, ForeignKeyRelation, ForeignKeyField, SmallIntField, DatetimeField
from tortoise.contrib.postgres.fields import ArrayField


class CoverLetter(Model):

    class Meta:
        table = "cover_letter"
        ordering = ["created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        INACTIVE = 1
        ACTIVE = 2

    id = UUIDField(pk=True)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="cover_letters")
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="cover_letters")
    title = TextField()
    content = TextField()
    target_position = ArrayField(element_type="TEXT", null=False, default=list)
    status = SmallIntField(default=Status.ACTIVE)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
