from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import (
    UUIDField, DatetimeField, IntField,
    ForeignKeyField, ForeignKeyRelation, OnDelete
)
from uuid_extensions import uuid7


class ApplicationKit(Model):
    class Meta:
        table = "application_kit"
        schema = "public"
        ordering = ["created_at"]

    class Status(IntEnum):
        INACTIVE = 0
        ACTIVE = 1

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="application_kits", on_delete=OnDelete.CASCADE
    )
    jd: ForeignKeyRelation["JobDescription"] = ForeignKeyField(
        "next_api.JobDescription", related_name="application_kits", on_delete=OnDelete.CASCADE
    )
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="application_kits", null=True, on_delete=OnDelete.SET_NULL
    )
    cover_letter: ForeignKeyRelation["CoverLetter"] = ForeignKeyField(
        "next_api.CoverLetter", related_name="application_kits", null=True, on_delete=OnDelete.SET_NULL
    )
    follow_up: ForeignKeyRelation["FollowUpLetter"] = ForeignKeyField(
        "next_api.FollowUpLetter", related_name="application_kits", null=True, on_delete=OnDelete.SET_NULL
    )
    mock_interview_id = UUIDField(null=True)
    job_insight: ForeignKeyRelation["JobInsights"] = ForeignKeyField(
        "next_api.JobInsights", related_name="application_kits", null=True, on_delete=OnDelete.SET_NULL
    )
    status = IntField(default=Status.ACTIVE)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
