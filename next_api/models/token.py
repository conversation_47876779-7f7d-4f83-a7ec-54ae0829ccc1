from tortoise.models import Model
from tortoise.fields import UUIDField, ForeignKeyField, JSONField, DatetimeField, ForeignKeyRelation, OnDelete


class AccessToken(Model):

    class Meta:
        table = "access_token"
        schema = "public"
        ordering = ["issued_at"]

    id = UUIDField(pk=True)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="access_tokens", on_delete=OnDelete.CASCADE)
    claims = JSONField()
    issued_at = DatetimeField(auto_now_add=True)
    expired_at = DatetimeField(null=False)
    rt_id = UUIDField(null=False)
    rt_expired_at = DatetimeField(null=False)
