from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import (
    <PERSON><PERSON><PERSON><PERSON>, TextField, DatetimeField, ManyToManyRelation,
    SmallIntField, JSONField
)
from uuid_extensions import uuid7


class Question(Model):

    class Meta:
        table = "question"
        ordering = ["created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        INACTIVE = 1
        ACTIVE = 2

    class Difficulty(IntEnum):
        EASY = 0
        MEDIUM = 1
        HARD = 2

    id = UUIDField(pk=True, default=uuid7)
    question = TextField()
    answer = TextField()
    user_answer = TextField(null=True)
    hint = TextField(null=True)
    point = SmallIntField(default=0)
    status = SmallIntField(default=Status.ACTIVE)
    difficulty = SmallIntField(default=Difficulty.MEDIUM)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    mock_interview_questions: ManyToManyRelation["MockInterviewQuestion"]

    # New fields
    category = TextField(null=True)
    overall_strengths = J<PERSON><PERSON><PERSON>(null=True)  # JSONB array
    overall_improvements = JSONField(null=True)  # JSONB array
    analysis_situation = TextField(null=True)
    analysis_task = TextField(null=True)
    analysis_action = TextField(null=True)
    analysis_result = TextField(null=True)
    analysis_skills = TextField(null=True)
    analysis_academic = TextField(null=True)
    analysis_management = TextField(null=True)
    analysis_personal = TextField(null=True)
    analysis_seek_info = TextField(null=True)
    analysis_patient_safety = TextField(null=True)
    analysis_initiative = TextField(null=True)
    analysis_escalate = TextField(null=True)
    analysis_support = TextField(null=True)
    analysis_strategy = TextField(null=True)
    analysis_technology = TextField(null=True)
    analysis_analytics = TextField(null=True)
    analysis_results = TextField(null=True)
    analysis_transformation = TextField(null=True)
