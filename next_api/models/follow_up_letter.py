from tortoise.models import Model
from tortoise.fields import (
    UUIDField, TextField, DatetimeField,
    ForeignKeyField, ForeignKeyRelation, OnDelete
)
from uuid_extensions import uuid7


class FollowUpLetter(Model):
    class Meta:
        table = "follow_up_letter"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="follow_up_letters", on_delete=OnDelete.CASCADE
    )
    jd: ForeignKeyRelation["JobDescription"] = ForeignKeyField(
        "next_api.JobDescription", related_name="follow_up_letters", on_delete=OnDelete.CASCADE
    )
    resume: ForeignKeyRelation["Resume"] = ForeignKeyField(
        "next_api.Resume", related_name="follow_up_letters", null=True, on_delete=OnDelete.SET_NULL
    )
    follow_up = TextField()
    customization_note = TextField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
