from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import date

class JobSearchRequest(BaseModel):
    keyword: Optional[str] = Field(None, description="Search keyword or job title")
    location: Optional[str] = Field(None, description="Location to search in")
    page: Optional[int] = Field(1, description="Page number for pagination")
    per_page: Optional[int] = Field(20, description="Number of results per page")
    is_remote: Optional[bool] = Field(None, description="Filter for remote jobs only")

class JobResponse(BaseModel):
    id: Optional[str] = Field(None, description="Unique identifier for the job")
    # provider_id: Optional[str] = Field(None, description="Unique identifier string for the job from the provider")
    title: Optional[str] = Field(None, description="Job title")
    company: Optional[str] = Field(None, description="Company name")
    location: Optional[str] = Field(None, description="Job location")
    description: Optional[str] = Field(None, description="Job description")
    salary: Optional[str] = Field(None, description="Salary information")
    posted_at: Optional[date] = Field(None, description="Date when the job was posted")
    is_remote: Optional[bool] = Field(None, description="Whether the job is remote")
    url: Optional[str] = Field(None, description="URL to the job posting")
    source: Optional[str] = Field(None, description="Source of the job posting (provider name)") 
    working_type: Optional[str] = Field(None, description="Working type of the job")
    category: Optional[str] = Field(None, description="Category of the job")
    keywords: Optional[List[str]] = Field(None, description="Keywords of the job")
    # number_of_employees: Optional[int] = Field(None, description="Number of employees in the company")
    # logo: Optional[str] = Field(None, description="Logo of the company")
