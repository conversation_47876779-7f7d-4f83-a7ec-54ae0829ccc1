from tortoise.models import Model
from tortoise.fields import (
    UUIDField, TextField, BooleanField, IntField, DatetimeField, 
    JSONField, ForeignKeyField, ForeignKeyRelation, OnDelete, ReverseRelation
)
from tortoise.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from uuid_extensions import uuid7


class PolarCustomer(Model):
    class Meta:
        table = "polar_customer"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    polar_customer_id = TextField(unique=True)
    external_id = TextField(null=True)
    email = TextField()
    email_verified = BooleanField(default=False)
    name = TextField(null=True)
    billing_address = JSONField(null=True)
    tax_id = ArrayField(element_type="TEXT", null=True, default=list)
    organization_id = TextField(null=True)
    deleted_at = DatetimeField(null=True)
    avatar_url = TextField(null=True)
    metadata = JSONField(default=dict)
    created_at = DatetimeField(auto_now_add=True)
    modified_at = DatetimeField(auto_now=True)

    # Reverse relations
    orders: ReverseRelation["PolarOrder"]


class PolarProduct(Model):
    class Meta:
        table = "polar_product"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    polar_product_id = TextField(unique=True)
    name = TextField()
    description = TextField(null=True)
    recurring_interval = TextField(null=True)
    is_recurring = BooleanField(default=False)
    is_archived = BooleanField(default=False)
    organization_id = TextField(null=True)
    metadata = JSONField(default=dict)
    created_at = DatetimeField(auto_now_add=True)
    modified_at = DatetimeField(auto_now=True)

    # Reverse relations
    orders: ReverseRelation["PolarOrder"]


class PolarDiscount(Model):
    class Meta:
        table = "polar_discount"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    polar_discount_id = TextField(unique=True)
    name = TextField()
    code = TextField(null=True)
    duration = TextField(null=True)
    type = TextField(null=True)
    amount = IntField(null=True)
    currency = TextField(null=True)
    starts_at = DatetimeField(null=True)
    ends_at = DatetimeField(null=True)
    max_redemptions = IntField(null=True)
    redemptions_count = IntField(default=0)
    organization_id = TextField(null=True)
    metadata = JSONField(default=dict)
    created_at = DatetimeField(auto_now_add=True)
    modified_at = DatetimeField(auto_now=True)

    # Reverse relations
    orders: ReverseRelation["PolarOrder"]


class PolarSubscription(Model):
    class Meta:
        table = "polar_subscription"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    polar_subscription_id = TextField(unique=True)
    amount = IntField(null=True)
    currency = TextField(null=True)
    recurring_interval = TextField(null=True)
    status = TextField(null=True)
    current_period_start = DatetimeField(null=True)
    current_period_end = DatetimeField(null=True)
    cancel_at_period_end = BooleanField(default=False)
    canceled_at = DatetimeField(null=True)
    started_at = DatetimeField(null=True)
    ends_at = DatetimeField(null=True)
    ended_at = DatetimeField(null=True)
    customer_id = TextField(null=True)
    product_id = TextField(null=True)
    discount_id = TextField(null=True)
    checkout_id = TextField(null=True)
    customer_cancellation_reason = TextField(null=True)
    customer_cancellation_comment = TextField(null=True)
    metadata = JSONField(default=dict)
    created_at = DatetimeField(auto_now_add=True)
    modified_at = DatetimeField(auto_now=True)

    # Reverse relations
    orders: ReverseRelation["PolarOrder"]


class PolarOrder(Model):
    class Meta:
        table = "polar_order"
        schema = "public"
        ordering = ["-created_at"]

    id = UUIDField(pk=True, default=uuid7)
    polar_order_id = TextField(unique=True)
    status = TextField()
    paid = BooleanField(default=False)
    subtotal_amount = IntField(null=True)
    discount_amount = IntField(null=True)
    net_amount = IntField(null=True)
    amount = IntField(null=True)
    tax_amount = IntField(null=True)
    total_amount = IntField(null=True)
    refunded_amount = IntField(null=True)
    refunded_tax_amount = IntField(null=True)
    currency = TextField(null=True)
    billing_reason = TextField(null=True)
    billing_address = JSONField(null=True)
    customer_id = TextField(null=True)
    product_id = TextField(null=True)
    discount_id = TextField(null=True)
    subscription_id = TextField(null=True)
    checkout_id = TextField(null=True)
    user_id = TextField(null=True)
    metadata = JSONField(default=dict)
    custom_field_data = JSONField(default=dict)
    created_at = DatetimeField(auto_now_add=True)
    modified_at = DatetimeField(auto_now=True)

    # Reverse relations
    items: ReverseRelation["PolarOrderItem"]


class PolarOrderItem(Model):
    class Meta:
        table = "polar_order_item"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    polar_order: ForeignKeyRelation["PolarOrder"] = ForeignKeyField(
        "next_api.PolarOrder", related_name="items", on_delete=OnDelete.CASCADE
    )
    label = TextField(null=True)
    amount = IntField(null=True)
    tax_amount = IntField(null=True)
    proration = BooleanField(default=False)
    product_price_id = TextField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    modified_at = DatetimeField(auto_now=True)
