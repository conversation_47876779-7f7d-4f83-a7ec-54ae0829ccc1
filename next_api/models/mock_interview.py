from enum import IntEnum
from tortoise.models import Model
from tortoise.fields import UUIDField, TextField, SmallIntField, DatetimeField, \
    ForeignKeyRelation, ForeignKeyField, ManyToManyRelation
from uuid_extensions import uuid7
from tortoise import fields
from uuid import UUID


class MockInterview(Model):

    class Meta:
        table = "mock_interview"
        ordering = ["created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        INACTIVE = 1
        ACTIVE = 2

    id = UUIDField(pk=True, default=uuid7)
    # user_id = UUIDField()
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="mock_interviews")
    position = TextField()
    job_description = TextField()
    job_description_id = UUIDField(null=True)
    status = SmallIntField(default=Status.ACTIVE)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    mock_interview_questions: ManyToManyRelation["MockInterviewQuestion"]
