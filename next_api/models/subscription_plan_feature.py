from tortoise.models import Model
from tortoise.fields import UUIDField, ForeignKeyField, JSONField, DatetimeField
from uuid_extensions import uuid7


class SubscriptionPlanFeature(Model):
    class Meta:
        table = "subscription_plan_feature"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    subscription_plan = ForeignKeyField(
        "next_api.SubscriptionPlan",
        related_name="mapped_features"
    )
    feature = ForeignKeyField(
        "next_api.Feature",
        related_name="mapped_plans"
    )
    feature_value = JSONField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
