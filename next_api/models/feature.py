from enum import IntEnum, Enum
from tortoise.models import Model
from tortoise.fields import <PERSON><PERSON><PERSON>ield, TextField, CharField, SmallIntField, \
    DatetimeField, JSONField, ManyToManyRelation
from uuid_extensions import uuid7


class Feature(Model):
    class Meta:
        table = "feature"
        schema = "public"
        ordering = ["created_at"]

    class Code(str, Enum):
        RESUME_BUILDER = "RESUME_BUILDER"
        RESUME_AI_BUILDER = "RESUME_AI_BUILDER"
        COVER_LETTER_BUILDER = "COVER_LETTER_BUILDER"
        COVER_LETTER_AI_BUILDER = "COVER_LETTER_AI_BUILDER"
        MOCK_INTERVIEW_AI = "MOCK_INTERVIEW_AI"
        INTERVIEW_AI_SUGGESTION = "INTERVIEW_AI_SUGGESTION"

    class Visibility(IntEnum):
        HIDDEN = 0
        INTERNAL = 1
        PUBLIC = 2

    id = UUIDField(pk=True, default=uuid7)
    code = CharField(unique=True, max_length=500)
    name = TextField()
    description = TextField(null=True)
    default_value = J<PERSON>NField(null=True)
    visibility = SmallIntField(null=False, default=Visibility.HIDDEN)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    mapped_plans: ManyToManyRelation["SubscriptionPlanFeature"]