from tortoise.models import Model
from tortoise.fields import (
    UUIDField,
    ForeignKeyField,
    JSONField,
    DatetimeField,
    ForeignKeyRelation
)
from uuid_extensions import uuid7


class UserFeature(Model):
    class Meta:
        table = "user_feature"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User",
        related_name="features"
    )
    feature: ForeignKeyRelation["Feature"] = ForeignKeyField(
        "next_api.Feature",
        related_name="user_features"
    )
    feature_value = JSONField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
