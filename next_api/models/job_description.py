from enum import Enum
from tortoise.models import Model
from tortoise.fields import (
    UUIDField, TextField, DateField, DatetimeField, CharEnumField,
    BooleanField, ForeignKeyField, ForeignKeyRelation, OnDelete,
    DecimalField
)
from tortoise.contrib.postgres.fields import Array<PERSON>ield
from uuid_extensions import uuid7


class JobDescription(Model):
    class Meta:
        table = "job_description"
        schema = "public"
        ordering = ["-posted_at", "-created_at"]

    class Status(str, Enum):
        PRIVATE = "private"
        POSTED = "posted"
        DENIED = "denied"
        IN_QUEUE = "in_queue"

    class ContractType(str, Enum):
        FULL_TIME = "full_time"
        PART_TIME = "part_time"
        CONTRACT = "contract"
        FREELANCE = "freelance"
        INTERNSHIP = "internship"

    class PromotionType(str, Enum):
        NORMAL = "normal"
        VIP = "vip"
        UP = "up"
        SVIP = "svip"

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="job_descriptions", on_delete=OnDelete.CASCADE
    )
    description = TextField()
    position = TextField(null=True)
    company = TextField(null=True)
    city = TextField(null=True)
    country = TextField(null=True)
    posted_at = DatetimeField(null=True)
    salary = DecimalField(max_digits=18, decimal_places=2, null=True)
    link = TextField(null=True)
    is_remote = BooleanField(default=False)
    source = TextField(null=True)
    category = TextField(null=True)
    keywords = ArrayField(element_type="text", default=[])
    skills = ArrayField(element_type="text", default=[])
    status = CharEnumField(
        enum_type=Status,
        default=Status.PRIVATE
    )
    contract_type = CharEnumField(
        enum_type=ContractType,
        null=True
    )
    is_favorite = BooleanField(default=False)
    promotion = CharEnumField(
        enum_type=PromotionType,
        default=PromotionType.NORMAL
    )
    mock_interview_id = UUIDField(null=True)
    resume_id = UUIDField(null=True)
    follow_up_id = UUIDField(null=True)
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)
