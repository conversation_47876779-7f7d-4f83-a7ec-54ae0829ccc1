from enum import IntEnum, Enum
from tortoise.models import Model
from tortoise.fields import UUIDField, TextField, DecimalField, SmallIntField, \
    CharEnumField, DatetimeField, ReverseRelation, ManyToManyRelation, \
    ForeignKeyField, JSONField, BooleanField, ForeignKeyRelation, OnDelete
from uuid_extensions import uuid7


class User(Model):

    class Meta:
        table = "user"
        schema = "public"
        ordering = ["created_at"]

    class Status(IntEnum):
        UNKNOWN = 0
        INACTIVE = 1
        IN_VERIFICATION = 2
        ACTIVE = 3
        BLOCKED = 4

    class ExpLevel(IntEnum):
        UNKNOWN = 0
        ENTRY = 10
        INTERMEDIATE = 20
        JUNIOR = 30
        ASSOCIATE = 40
        MIDDLE = 50
        SENIOR = 60
        LEAD = 70
        MANAGER = 80
        DIRECTOR = 90
        EXECUTIVE = 100

    class Provider(str, Enum):
        UNKNOWN = ""
        EMAIL = "email"
        GITHUB = "github"
        GOOGLE = "google"

    id = UUIDField(pk=True, default=uuid7)
    email = TextField()
    fullname = TextField()
    picture = TextField()
    password = TextField(null=True)
    min_salary = DecimalField(max_digits=18, decimal_places=2)
    expected_salary = DecimalField(max_digits=18, decimal_places=2, null=True)
    exp_level = SmallIntField(null=False, default=ExpLevel.UNKNOWN)
    linked_in = TextField()
    locale = TextField(default="en-US")
    two_factor_enabled = BooleanField(default=False)
    provider = CharEnumField(enum_type=Provider, default=Provider.UNKNOWN)
    status = SmallIntField()
    auth0_id = TextField(null=True)
    profession = JSONField(default=list)  # List of professions
    location = JSONField(default=list)  # List of locations
    created_at = DatetimeField(auto_now_add=True)
    updated_at = DatetimeField(auto_now=True)

    resumes: ReverseRelation["Resume"]
    cover_letters: ReverseRelation["CoverLetter"]
    roles: ManyToManyRelation["Role"]
    secrets: ReverseRelation["Secret"]
    features: ReverseRelation["UserFeature"]
    subscriptions: ReverseRelation["UserSubscription"]
    job_descriptions: ReverseRelation["JobDescription"]


class Secret(Model):

    class Meta:
        table = "secret"
        schema = "public"
        ordering = ["created_at"]

    id = UUIDField(pk=True, default=uuid7)
    user: ForeignKeyRelation["User"] = ForeignKeyField(
        "next_api.User", related_name="secrets", on_delete=OnDelete.CASCADE)
    last_signed_in = DatetimeField()
    verification_token = TextField()
    two_factor_secret = TextField()
    two_factor_backup_codes = JSONField()
    refresh_token = TextField()
    reset_token = TextField()
