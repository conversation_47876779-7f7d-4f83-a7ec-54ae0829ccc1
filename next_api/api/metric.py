from tortoise import Tortoise
from sanic import Blueprint, text, Request
from sanic_ext import openapi
from sanic.log import error_logger

bp = Blueprint("metric")


@bp.route("/liveness")
@openapi.tag("Metric")
@openapi.definition(
    summary="Check if the API is alive",
    description="This endpoint is used to check if the API is alive.",
    response=openapi.definitions.Response(
        status=200, content="OK", description="Application is still alive"),
)
async def liveness(_: Request):
    return text("OK")


@bp.route("/readiness")
@openapi.tag("Metric")
@openapi.definition(
    summary="Check if the API is ready",
    description="This endpoint is used to check if the API is ready.",
    response=[
        openapi.definitions.Response(
            status=200, content="OK", description="Application is ready"),
        openapi.definitions.Response(
            status=503, content="ERROR", description="Application is not ready"),
    ],
)
async def readiness(_: Request):
    tortoise_conn = Tortoise.get_connection('default')
    try:
        _ = await tortoise_conn.execute_query("SELECT 1")
    except Exception as e:
        error_logger.exception(e)
        return text("ERROR", 503)
    return text("OK")
