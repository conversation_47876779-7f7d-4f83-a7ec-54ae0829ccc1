from sanic import Blueprint, Request
from sanic_ext import openapi
from typing import List
import asyncio
from datetime import datetime
import os

from models.job_search import JobSearchRequest, JobResponse
from clients.job_search.adzuna import <PERSON>zunaProvider
from clients.job_search.findwork import FindworkProvider
from clients.job_search.careerjet import CareerjetProvider
from clients.job_search.jooble import JoobleProvider
from clients.job_search.base import JobSearchProvider
from api.base import json_response, Response, ResponseCode
from sanic.log import logger

bp = Blueprint("job_search")

# Initialize providers
providers: List[JobSearchProvider] = [
    AdzunaProvider(
        api_key=os.getenv("ADZUNA_API_KEY"),
        app_id=os.getenv("ADZUNA_APP_ID")
    ),
    FindworkProvider(
        api_key=os.getenv("FINDWORK_API_KEY")
    ),
    CareerjetProvider(
        api_key=os.getenv("CAREERJET_API_KEY")
    ),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
        api_key=os.getenv("JOOBLE_API_KEY")
    )
]

def sort_jobs(jobs: List[JobResponse], request: JobSearchRequest) -> List[JobResponse]:
    """
    Sort job results based on relevance to the search request.
    This is a simple implementation that can be enhanced with more sophisticated ranking.
    """
    # Create a scoring function for each job
    def score_job(job: JobResponse) -> float:
        score = 0.0
        
        # Score based on keyword matches in title and description
        keyword = request.keyword.lower()
        if keyword in job.title.lower():
            score += 10.0  # Higher weight for title matches
        
        if job.description and keyword in job.description.lower():
            score += 5.0  # Lower weight for description matches
        
        # Score based on location match
        if request.location and job.location:
            if request.location.lower() in job.location.lower():
                score += 8.0
        
        # Score based on recency (newer jobs get higher scores)
        if job.posted_at:
            days_old = (datetime.now().date() - job.posted_at).days
            if days_old < 7:  # Less than a week old
                score += 7.0
            elif days_old < 30:  # Less than a month old
                score += 5.0
            elif days_old < 90:  # Less than 3 months old
                score += 3.0
        
        # Score based on whether it's remote (if requested)
        if job.is_remote:
            score += 2.0
        
        # Score based on whether salary information is available
        if job.salary:
            score += 1.0
        
        return score
    
    # Sort jobs by score in descending order
    return sorted(jobs, key=score_job, reverse=True)

@openapi.tag("Job Search")
@bp.route("/search", methods=["POST"], strict_slashes=False)
async def search_jobs(request: Request):
    """
    Search for jobs across all providers

    openapi:
    ---
    summary: Search for jobs across multiple job boards
    description: This endpoint searches for jobs across multiple job boards and returns aggregated, sorted results.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/JobSearchRequest"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: array
              items:
                ref: "#/components/schemas/JobResponse"
      400:
        description: Bad Request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ErrorResponse"
      500:
        description: Server Error
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ErrorResponse"
    """
    try:
        # Parse request body
        search_request = JobSearchRequest(**request.json)
        
        # Search jobs concurrently across all providers
        tasks = []
        for provider in providers:
            try:
                task = provider.search_jobs(search_request)
                tasks.append(task)
            except Exception as e:
                logger.error(f"Error creating task for provider {provider.__class__.__name__}: {e}")
                continue
        
        # Gather results, handling individual provider failures
        results = []
        provider_results = {}
        completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, task_result in enumerate(completed_tasks):
            provider_name = providers[i].__class__.__name__ if i < len(providers) else f"Provider_{i}"
            
            if isinstance(task_result, Exception):
                logger.error(f"Provider {provider_name} error: {task_result}")
                provider_results[provider_name] = []
                continue
                
            if isinstance(task_result, list):
                provider_results[provider_name] = task_result
                results.extend(task_result)
            else:
                logger.error(f"Unexpected result type from {provider_name}: {type(task_result)}")
                provider_results[provider_name] = []
        
        # Log summary of results from each provider
        logger.info("Search results summary:")
        provider_jobs_count = {}
        for provider_name, provider_jobs in provider_results.items():
            provider_jobs_count[provider_name] = len(provider_jobs)
            logger.info(f"  {provider_name}: {len(provider_jobs)} jobs")
        
        # Sort the combined results
        sorted_results = sort_jobs(results, search_request)
        
        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Success",
            data={"providers_count": provider_jobs_count, 
                  "jobs_found": sorted_results}
        ), status_code=200)
        
    except Exception as e:
        logger.error(f"Error in search_jobs: {e}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message=str(e),
            data={}
        ), status_code=500) 