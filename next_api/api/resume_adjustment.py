from typing import Optional
from datetime import datetime
from pydantic import BaseModel, <PERSON>
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.resume_adjustment import ResumeAdjustmentRepository, ResumeAdjustmentQuery
from models.resume_adjustment import ResumeAdjustment

bp = Blueprint("resume_adjustment")


@openapi.component
class RequestResumeAdjustmentSchema(BaseModel):
    """Schema for resume adjustment creation/update requests"""
    resume_id: str = Field(..., description="UUID of resume",
                           example="123e4567-e89b-12d3-a456-************")
    job_title: str = Field(..., description="Job title", example="Software Engineer")
    educations: list = Field([], description="Education adjustments")
    skills: list = Field([], description="Skills adjustments")
    experiences: list = Field([], description="Experience adjustments")
    status: Optional[int] = Field(ResumeAdjustment.Status.SUGGESTED,
                                  description="Status of adjustment",
                                  example=ResumeAdjustment.Status.SUGGESTED)


@openapi.component
class ResponseResumeAdjustmentSchema(BaseModel):
    """Schema for resume adjustment responses"""
    id: str = Field(..., description="UUID of adjustment",
                    example="123e4567-e89b-12d3-a456-************")
    resume_id: str = Field(..., description="UUID of resume",
                           example="123e4567-e89b-12d3-a456-************")
    job_title: str = Field(..., description="Job title", example="Software Engineer")
    educations: list = Field(..., description="Education adjustments")
    skills: list = Field(..., description="Skills adjustments")
    experiences: list = Field(..., description="Experience adjustments")
    status: int = Field(..., description="Status of adjustment", example=1)
    created_at: datetime = Field(..., description="Created at",
                                 example="2024-01-01T00:00:00")
    updated_at: Optional[datetime] = Field(None, description="Updated at",
                                           example="2024-01-01T00:00:00")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Resume Adjustment")
@require_token
async def get_list(request: Request):
    """Get a paginated list of resume adjustments.

    openapi:
    ---
    summary: Get a list of resume adjustments
    description: This endpoint is used to get resume adjustment list.
    parameters:
    - name: Authorization
      in: header
      schema:
        type: string
        format: Bearer <JWT token>
      description: Access token
      required: true
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Number of records per page
      example: 10
    - name: resume_id
      in: query
      schema:
        type: string
        format: uuid
      description: Resume ID
    - name: job_title
      in: query
      schema:
        type: string
      description: Job title
    - name: job_title__contains
      in: query
      schema:
        type: string
      description: Job title contains
    - name: job_title__icontains
      in: query
      schema:
        type: string
      description: Job title contains (case insensitive)
    - name: status
      in: query
      schema:
        type: integer
      description: Status
    - name: status__in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Status list
    - name: status__not_in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Excluded status list
    - name: order_by
      in: query
      schema:
        type: array
        items:
          type: string
          enum: ["id", "-id", "job_title", "-job_title", "status", "-status",
                 "created_at", "-created_at", "updated_at", "-updated_at"]
      description: Order by
    responses:
      200:
        description: Successfully retrieved resume adjustments
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        ref: "#/components/schemas/ResponseResumeAdjustmentSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = ResumeAdjustmentQuery(**request.args)
    records, total = await ResumeAdjustmentRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Resume Adjustment")
@require_token
async def create(request: Request):
    """Create a new resume adjustment.

    openapi:
    ---
    summary: Create a new resume adjustment
    description: This endpoint is used to create a new resume adjustment.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestResumeAdjustmentSchema"
    responses:
      201:
        description: Successfully created resume adjustment
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseResumeAdjustmentSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        record = await ResumeAdjustmentRepository.create_one(request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot create resume adjustment",
                         context={"error": ResponseCode.CREATE_FAILED})

    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<adjustment_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("Resume Adjustment")
@require_token
async def update(request: Request, adjustment_id: str):
    """Update an existing resume adjustment.

    openapi:
    ---
    summary: Update an existing resume adjustment
    description: This endpoint is used to update an existing resume adjustment.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: adjustment_id
      in: path
      type: string
      required: true
      description: Resume Adjustment ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestResumeAdjustmentSchema"
    responses:
      200:
        description: Successfully updated resume adjustment
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseResumeAdjustmentSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Resume adjustment not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    try:
        affected_rows, record = await ResumeAdjustmentRepository.update(adjustment_id, request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot update resume adjustment")

    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="Cannot update resume adjustment"),
        404
    )


@bp.route("/<adjustment_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Resume Adjustment")
@require_token
async def get_one(_: Request, adjustment_id: str):
    """Get a single resume adjustment by ID.

    openapi:
    ---
    summary: Get a single resume adjustment
    description: This endpoint is used to get a single resume adjustment by ID.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: adjustment_id
      in: path
      type: string
      required: true
      description: Resume Adjustment ID
    responses:
      200:
        description: Successfully retrieved resume adjustment
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseResumeAdjustmentSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Resume adjustment not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    record = await ResumeAdjustmentRepository.get_one(ResumeAdjustmentQuery(id=adjustment_id))

    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.GET_ONE_FAILED,
                 message="Resume adjustment not found"),
        404
    )
