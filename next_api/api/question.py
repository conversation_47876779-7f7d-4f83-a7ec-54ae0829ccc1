from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest, NotFound
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import (
    json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode,
)
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.question import QuestionRepository, QuestionQuery

bp = Blueprint("question")


@openapi.component
class RequestQuestionSchema(BaseModel):
    content: str = Field(..., description="Question content")
    answer: str = Field(..., description="Answer content")
    user_answer: str | None = Field(None, description="User's answer to the question")
    hint: str | None = Field(None, description="Hint for the question")
    point: int = Field(default=0, description="Points for the question")
    difficulty: int = Field(..., description="Difficulty level (0: Easy, 1: Medium, 2: Hard)")
    status: int = Field(..., description="Status of question")
    category: str | None = Field(None, description="Question category")
    overall_strengths: list[str] | None = Field(None, description="List of overall strengths")
    overall_improvements: list[str] | None = Field(None, description="List of areas for improvement")
    analysis_situation: str | None = Field(None, description="Analysis of the situation")
    analysis_task: str | None = Field(None, description="Analysis of the task")
    analysis_action: str | None = Field(None, description="Analysis of the action taken")
    analysis_result: str | None = Field(None, description="Analysis of the result")
    analysis_skills: str | None = Field(None, description="Analysis of skills demonstrated")
    analysis_academic: str | None = Field(None, description="Academic analysis")
    analysis_management: str | None = Field(None, description="Management analysis")
    analysis_personal: str | None = Field(None, description="Personal qualities analysis")
    analysis_seek_info: str | None = Field(None, description="Information seeking analysis")
    analysis_patient_safety: str | None = Field(None, description="Patient safety analysis")
    analysis_initiative: str | None = Field(None, description="Initiative analysis")
    analysis_escalate: str | None = Field(None, description="Escalation analysis")
    analysis_support: str | None = Field(None, description="Support analysis")
    analysis_strategy: str | None = Field(None, description="Strategic thinking analysis")
    analysis_technology: str | None = Field(None, description="Technology usage analysis")
    analysis_analytics: str | None = Field(None, description="Analytical skills analysis")
    analysis_results: str | None = Field(None, description="Results analysis")
    analysis_transformation: str | None = Field(None, description="Transformation analysis")


@openapi.component
class ResponseQuestionSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of question")
    mock_interview_id: str = Field(..., format="UUID", description="UUID of mock interview")
    content: str = Field(..., description="Question content")
    answer: str = Field(..., description="Answer content")
    user_answer: str | None = Field(None, description="User's answer to the question")
    hint: str | None = Field(None, description="Hint for the question")
    point: int = Field(default=0, description="Points for the question")
    difficulty: int = Field(..., description="Difficulty level (0: Easy, 1: Medium, 2: Hard)")
    status: int = Field(..., description="Status of question")
    category: str | None = Field(None, description="Question category")
    overall_strengths: list[str] | None = Field(None, description="List of overall strengths")
    overall_improvements: list[str] | None = Field(None, description="List of areas for improvement")
    analysis_situation: str | None = Field(None, description="Analysis of the situation")
    analysis_task: str | None = Field(None, description="Analysis of the task")
    analysis_action: str | None = Field(None, description="Analysis of the action taken")
    analysis_result: str | None = Field(None, description="Analysis of the result")
    analysis_skills: str | None = Field(None, description="Analysis of skills demonstrated")
    analysis_academic: str | None = Field(None, description="Academic analysis")
    analysis_management: str | None = Field(None, description="Management analysis")
    analysis_personal: str | None = Field(None, description="Personal qualities analysis")
    analysis_seek_info: str | None = Field(None, description="Information seeking analysis")
    analysis_patient_safety: str | None = Field(None, description="Patient safety analysis")
    analysis_initiative: str | None = Field(None, description="Initiative analysis")
    analysis_escalate: str | None = Field(None, description="Escalation analysis")
    analysis_support: str | None = Field(None, description="Support analysis")
    analysis_strategy: str | None = Field(None, description="Strategic thinking analysis")
    analysis_technology: str | None = Field(None, description="Technology usage analysis")
    analysis_analytics: str | None = Field(None, description="Analytical skills analysis")
    analysis_results: str | None = Field(None, description="Results analysis")
    analysis_transformation: str | None = Field(None, description="Transformation analysis")


@openapi.tag("Question")
@bp.route("", methods=["GET"], strict_slashes=False)
@require_token
async def get_list(request: Request):
    """Get list of all questions

    openapi:
    ---
    summary: Get list of all questions
    description: This endpoint is used to get list of all questions.
    parameters:
    - name: Authorization
      in: header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
      description: Access token
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Page size
      example: 10
    - name: id
      in: query
      schema:
        type: string
        format: uuid
      description: Filter by exact ID match
    - name: id__in
      in: query
      schema:
        type: array
        items:
          type: string
          format: uuid
      description: Filter by ID in list (comma separated)
    - name: id__not_in
      in: query
      schema:
        type: array
        items:
          type: string
          format: uuid
      description: Filter by ID not in list (comma separated)
    - name: question
      in: query
      schema:
        type: string
      description: Filter by exact question content match
    - name: question__contains
      in: query
      schema:
        type: string
      description: Filter by question content containing string (case sensitive)
    - name: question__icontains
      in: query
      schema:
        type: string
      description: Filter by question content containing string (case insensitive)
    - name: answer
      in: query
      schema:
        type: string
      description: Filter by exact answer match
    - name: answer__contains
      in: query
      schema:
        type: string
      description: Filter by answer containing string (case sensitive)
    - name: answer__icontains
      in: query
      schema:
        type: string
      description: Filter by answer containing string (case insensitive)
    - name: user_answer
      in: query
      schema:
        type: string
      description: Filter by exact user answer match
    - name: user_answer__contains
      in: query
      schema:
        type: string
      description: Filter by user answer containing string (case sensitive)
    - name: user_answer__icontains
      in: query
      schema:
        type: string
      description: Filter by user answer containing string (case insensitive)
    - name: hint
      in: query
      schema:
        type: string
      description: Filter by exact hint match
    - name: hint__contains
      in: query
      schema:
        type: string
      description: Filter by hint containing string (case sensitive)
    - name: hint__icontains
      in: query
      schema:
        type: string
      description: Filter by hint containing string (case insensitive)
    - name: point
      in: query
      schema:
        type: integer
      description: Filter by exact point value
    - name: point__gte
      in: query
      schema:
        type: integer
      description: Filter by points greater than or equal to
    - name: point__lte
      in: query
      schema:
        type: integer
      description: Filter by points less than or equal to
    - name: difficulty
      in: query
      schema:
        type: integer
      description: "Filter by exact difficulty match (0: Easy, 1: Medium, 2: Hard)"
    - name: difficulty__in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Filter by difficulty in list (comma separated)
    - name: difficulty__not_in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Filter by difficulty not in list (comma separated)
    - name: status
      in: query
      schema:
        type: integer
      description: "Filter by exact status match (0: Unknown, 1: Inactive, 2: Active)"
    - name: status__in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Filter by status in list (comma separated)
    - name: status__not_in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Filter by status not in list (comma separated)
    - name: created_at
      in: query
      schema:
        type: string
        format: date-time
      description: Filter by exact created_at match
    - name: created_at__gte
      in: query
      schema:
        type: string
        format: date-time
      description: Filter by created_at greater than or equal to
    - name: created_at__lte
      in: query
      schema:
        type: string
        format: date-time
      description: Filter by created_at less than or equal to
    - name: updated_at
      in: query
      schema:
        type: string
        format: date-time
      description: Filter by exact updated_at match
    - name: updated_at__gte
      in: query
      schema:
        type: string
        format: date-time
      description: Filter by updated_at greater than or equal to
    - name: updated_at__lte
      in: query
      schema:
        type: string
        format: date-time
      description: Filter by updated_at less than or equal to
    - name: order_by
      in: query
      schema:
        type: array
        items:
          type: string
          enum: ["id", "-id", "status", "-status", "question", "-question", 
                 "answer", "-answer", "created_at", "-created_at", 
                 "updated_at", "-updated_at"]
      description: "Order by fields (prefix with `-` for descending)"
    - name: category
      in: query
      schema:
        type: string
      description: Filter by exact category match
    - name: category__contains
      in: query
      schema:
        type: string
      description: Filter by category containing string (case sensitive)
    - name: category__icontains
      in: query
      schema:
        type: string
      description: Filter by category containing string (case insensitive)
    - name: overall_strengths
      in: query
      schema:
        type: array
        items:
          type: string
      description: Filter by overall strengths array
    - name: overall_strengths__contains
      in: query
      schema:
        type: string
      description: Filter by overall strengths containing string
    - name: overall_strengths__icontains
      in: query
      schema:
        type: string
      description: Filter by overall strengths containing string (case insensitive)
    - name: overall_improvements
      in: query
      schema:
        type: array
        items:
          type: string
      description: Filter by overall improvements array
    - name: overall_improvements__contains
      in: query
      schema:
        type: string
      description: Filter by overall improvements containing string
    - name: overall_improvements__icontains
      in: query
      schema:
        type: string
      description: Filter by overall improvements containing string (case insensitive)
    - name: analysis_situation
      in: query
      schema:
        type: string
      description: Filter by exact analysis_situation match
    - name: analysis_situation__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_situation containing string (case sensitive)
    - name: analysis_situation__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_situation containing string (case insensitive)
    - name: analysis_task
      in: query
      schema:
        type: string
      description: Filter by exact analysis_task match
    - name: analysis_task__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_task containing string (case sensitive)
    - name: analysis_task__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_task containing string (case insensitive)
    - name: analysis_action
      in: query
      schema:
        type: string
      description: Filter by exact analysis_action match
    - name: analysis_action__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_action containing string (case sensitive)
    - name: analysis_action__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_action containing string (case insensitive)
    - name: analysis_result
      in: query
      schema:
        type: string
      description: Filter by exact analysis_result match
    - name: analysis_result__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_result containing string (case sensitive)
    - name: analysis_result__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_result containing string (case insensitive)
    - name: analysis_skills
      in: query
      schema:
        type: string
      description: Filter by exact analysis_skills match
    - name: analysis_skills__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_skills containing string (case sensitive)
    - name: analysis_skills__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_skills containing string (case insensitive)
    - name: analysis_academic
      in: query
      schema:
        type: string
      description: Filter by exact analysis_academic match
    - name: analysis_academic__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_academic containing string (case sensitive)
    - name: analysis_academic__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_academic containing string (case insensitive)
    - name: analysis_management
      in: query
      schema:
        type: string
      description: Filter by exact analysis_management match
    - name: analysis_management__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_management containing string (case sensitive)
    - name: analysis_management__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_management containing string (case insensitive)
    - name: analysis_personal
      in: query
      schema:
        type: string
      description: Filter by exact analysis_personal match
    - name: analysis_personal__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_personal containing string (case sensitive)
    - name: analysis_personal__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_personal containing string (case insensitive)
    - name: analysis_seek_info
      in: query
      schema:
        type: string
      description: Filter by exact analysis_seek_info match
    - name: analysis_seek_info__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_seek_info containing string (case sensitive)
    - name: analysis_seek_info__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_seek_info containing string (case insensitive)
    - name: analysis_patient_safety
      in: query
      schema:
        type: string
      description: Filter by exact analysis_patient_safety match
    - name: analysis_patient_safety__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_patient_safety containing string (case sensitive)
    - name: analysis_patient_safety__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_patient_safety containing string (case insensitive)
    - name: analysis_initiative
      in: query
      schema:
        type: string
      description: Filter by exact analysis_initiative match
    - name: analysis_initiative__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_initiative containing string (case sensitive)
    - name: analysis_initiative__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_initiative containing string (case insensitive)
    - name: analysis_escalate
      in: query
      schema:
        type: string
      description: Filter by exact analysis_escalate match
    - name: analysis_escalate__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_escalate containing string (case sensitive)
    - name: analysis_escalate__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_escalate containing string (case insensitive)
    - name: analysis_support
      in: query
      schema:
        type: string
      description: Filter by exact analysis_support match
    - name: analysis_support__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_support containing string (case sensitive)
    - name: analysis_support__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_support containing string (case insensitive)
    - name: analysis_strategy
      in: query
      schema:
        type: string
      description: Filter by exact analysis_strategy match
    - name: analysis_strategy__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_strategy containing string (case sensitive)
    - name: analysis_strategy__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_strategy containing string (case insensitive)
    - name: analysis_technology
      in: query
      schema:
        type: string
      description: Filter by exact analysis_technology match
    - name: analysis_technology__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_technology containing string (case sensitive)
    - name: analysis_technology__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_technology containing string (case insensitive)
    - name: analysis_analytics
      in: query
      schema:
        type: string
      description: Filter by exact analysis_analytics match
    - name: analysis_analytics__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_analytics containing string (case sensitive)
    - name: analysis_analytics__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_analytics containing string (case insensitive)
    - name: analysis_results
      in: query
      schema:
        type: string
      description: Filter by exact analysis_results match
    - name: analysis_results__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_results containing string (case sensitive)
    - name: analysis_results__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_results containing string (case insensitive)
    - name: analysis_transformation
      in: query
      schema:
        type: string
      description: Filter by exact analysis_transformation match
    - name: analysis_transformation__contains
      in: query
      schema:
        type: string
      description: Filter by analysis_transformation containing string (case sensitive)
    - name: analysis_transformation__icontains
      in: query
      schema:
        type: string
      description: Filter by analysis_transformation containing string (case insensitive)
    responses:
      200:
        description: Successfully retrieved questions
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array      
                      items:
                        ref: "#/components/schemas/ResponseQuestionSchema"
                  required: ["total", "page", "page_size", "records"]
              required: ["error", "message", "data"]
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = QuestionQuery(**request.args)
    records, total = await QuestionRepository.get_list(query, pagination)

    body = Response(
        error=ResponseCode.SUCCESS,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@openapi.tag("Question")
@bp.route("", methods=["POST"], strict_slashes=False)
@require_token
async def create(request: Request):
    """Create a new question

    openapi:
    ---
    summary: Create a new question
    description: Create a new question record
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestQuestionSchema"
    responses:
      200:
        description: Successfully created question
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseQuestionSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        question = await QuestionRepository.create_one(request.json)
        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=dict(question)
        )
        return json_response(body, 200)
    except Exception as e:
        error_logger.exception("Failed to create question: %s", e)
        raise BadRequest("Failed to create question")


@openapi.tag("Question")
@bp.route("/<question_id:str>", methods=["GET"], strict_slashes=False)
@require_token
async def get_one(_: Request, question_id: str):
    """Get a specific question

    openapi:
    ---
    summary: Get a question
    description: Get a specific question by ID
    parameters:
    - name: Authorization
      in: header
      type: string
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: question_id
      in: path
      type: string
      required: true
      description: Question ID
    responses:
      200:
        description: Successfully retrieved question
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseQuestionSchema"
      404:
        description: Question not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    query = QuestionQuery(id=question_id)
    question = await QuestionRepository.get_one(query)

    if not question:
        raise NotFound("Question not found")

    body = Response(
        error=ResponseCode.SUCCESS,
        message=SUCCESS_MESSAGE,
        data=dict(question)
    )
    return json_response(body, 200)


@openapi.tag("Question")
@bp.route("/<question_id:str>", methods=["PUT"], strict_slashes=False)
@require_token
async def update(request: Request, question_id: str):
    """Update a question

    openapi:
    ---
    summary: Update a question
    description: Update an existing question
    parameters:
    - name: Authorization
      in: header
      type: string
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: question_id
      in: path
      type: string
      required: true
      description: Question ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestQuestionSchema"
    responses:
      200:
        description: Successfully updated question
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseQuestionSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      404:
        description: Question not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        affected_rows, updated_question = await QuestionRepository.update(question_id, request.json)

        if affected_rows == 0:
            raise NotFound("Question not found")

        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=dict(updated_question)
        )
        return json_response(body, 200)
    except Exception as e:
        error_logger.exception("Failed to update question: %s", e)
        raise BadRequest("Failed to update question")
