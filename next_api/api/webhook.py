from datetime import datetime
from typing import Dict, Any, Union
from pydantic import BaseModel, Field, ConfigDict
from sanic import Blueprint, Request
from sanic.log import error_logger
from sanic.exceptions import BadRequest, Forbidden
from sanic_ext import openapi
from polar_sdk.webhooks import validate_event, WebhookVerificationError
from polar_sdk.models import WebhookOrderPaidPayload, WebhookSubscriptionCanceledPayload, WebhookSubscriptionUpdatedPayload
from api.base import json_response, Response, SUCCESS_MESSAGE, ResponseCode
from api.middlewares import check_lemon_sign
from repositories.subscription_plan import SubscriptionPlanRepository
from repositories.user import UserRepository
from repositories.user_subscription import UserSubscriptionRepository, UserSubscriptionQuery
from repositories.user_feature import UserFeatureRepository
from models.user_subscription import UserSubscription
from models.subscription_plan import SubscriptionPlan
from settings import LEMONSQUEEZY_EDU_VARIANT, POLAR_SECRET, POLAR_EDU_PRODUCT_ID
from utils.helpers import is_edu

bp = Blueprint("webhook")


@openapi.component
class LemonEventSubscriptionRequest(BaseModel):
    """Request model for LemonSqueezy subscription webhook events."""
    model_config = ConfigDict(extra="allow")

    meta: Dict[str, Any] = Field(..., description="Event metadata")
    data: Dict[str, Any] = Field(..., description="Event data")


@openapi.tag("Webhook")
@bp.route("/polar-events/subscriptions", methods=["POST"])
async def handle_polar_subscription(request: Request):
    """Handle Polar subscription webhook events.
    """
    try:
        event: Union[WebhookOrderPaidPayload, WebhookSubscriptionCanceledPayload, WebhookSubscriptionUpdatedPayload] = validate_event(
            body=request.body,
            headers=request.headers,
            secret=POLAR_SECRET,
        )
        polar_product_id = event.data.product_id
        email = event.data.customer.email
        user = await UserRepository.get_by_email(email)
        if not user:
            raise BadRequest(
                message=f"User not found for email: {email}",
                context={"error": ResponseCode.GET_ONE_FAILED}
            )
        plan: SubscriptionPlan = await SubscriptionPlanRepository.get_by_polar_product_id(polar_product_id)
        if not plan:
            raise BadRequest(
                message=f"Subscription plan not found for product_id: {polar_product_id}",
                context={"error": ResponseCode.GET_ONE_FAILED}
            )

        if event.TYPE == "order.paid" and event.data.status == "paid":
            if polar_product_id == POLAR_EDU_PRODUCT_ID and not is_edu(email):
                error_logger.exception(
                    "Non-educational email for educational plan | type: %s | id: %s | event: %s | email: %s | product_id: %s",
                    event.TYPE,
                    event.data.id,
                    event.TYPE,
                    email,
                    polar_product_id
                )
                raise BadRequest(
                    message="Educational email address required for this plan",
                    context={"error": ResponseCode.CLIENT_BAD_REQUEST}
                )
            await UserSubscriptionRepository.subscribe_plan(
                user_id=user['id'],
                plan_id=plan['id'],
                partner="polar",
                partner_id=event.data.subscription.id,
                start_date=datetime.now()
            )
        elif event.TYPE == "subscription.updated" and event.data.status == "canceled":
            await UserSubscriptionRepository.update_by_query(
                update_data={
                    "status": UserSubscription.Status.INACTIVE,
                    "updated_at": datetime.now(),
                    "end_date": datetime.now()  # Set end date when subscription expires
                },
                q=UserSubscriptionQuery(
                    user_id=user['id'],
                    subscription_plan_id=plan['id'],
                    partner="polar",
                    partner_id=event.data.id
                )
            )
        elif event.TYPE in ["subscription.canceled", "order.refunded"]:
            await UserSubscriptionRepository.update_by_query(
                update_data={
                    "status": UserSubscription.Status.CANCELLED,
                    "updated_at": datetime.now(),
                },
                q=UserSubscriptionQuery(
                    user_id=user['id'],
                    subscription_plan_id=plan['id'],
                    partner="polar",
                    partner_id=event.data.id
                )
            )

        UserFeatureRepository.clear_cache(user['id'])
        return json_response(Response(
            error=0,
            message=SUCCESS_MESSAGE
        ), 200)
    except WebhookVerificationError as e:
        error_logger.exception(e)
        raise Forbidden(
            message="Failed to verify webhook event",
            context={"error": ResponseCode.UNAUTHORIZED}
        )


@openapi.tag("Webhook")
@bp.route("/lemon-events/subscriptions", methods=["POST"])
@check_lemon_sign
async def handle_lemonsqueezy_subscription(request: Request):
    """Handle LemonSqueezy subscription webhook events.

    We process 2 events:
    - subscription_cancelled  
    - subscription_updated 

    We will process those events later:
    - subscription_created  
    - subscription_resumed  
    - subscription_expired  
    - subscription_paused  
    - subscription_unpaused  
    - subscription_payment_failed  
    - subscription_payment_success  
    - subscription_payment_recovered  
    - subscription_payment_refunded  
    - subscription_plan_changed  

    openapi:
    ---
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/LemonEventSubscriptionRequest"
    responses:
      200:
        description: Event processed successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
    """
    try:
        # Extract data type and ID
        data_obj = request.json.get('data', {})
        event_name = request.json.get('meta', {}).get('event_name')
        attributes = data_obj.get('attributes', {})
        variant_id = attributes.get('variant_id', 0)
        user_email = attributes.get('user_email', '')

        # Check if this is an educational variant and validate email
        if str(variant_id) in LEMONSQUEEZY_EDU_VARIANT and not is_edu(user_email):
            error_logger.exception(
                "Non-educational email for educational plan | type: %s | id: %s | event: %s | email: %s | variant_id: %s",
                data_obj.get('type'),
                data_obj.get('id'),
                event_name,
                user_email,
                variant_id
            )
            raise BadRequest(
                message="Educational email address required for this plan",
                context={"error": ResponseCode.CLIENT_BAD_REQUEST}
            )

        if not variant_id or not user_email:
            error_logger.exception(
                "Invalid event | type: %s | id: %s | event: %s | email: %s | variant_id: %s",
                data_obj.get('type'),
                data_obj.get('id'),
                event_name,
                user_email,
                variant_id
            )
            raise BadRequest(
                message="Invalid event: missing variant_id or user_email",
                context={"error": ResponseCode.CLIENT_BAD_REQUEST}
            )

        # Get subscription plan by variant_id
        plan = await SubscriptionPlanRepository.get_by_lemon_variant_id(variant_id)
        if not plan:
            error_logger.exception(
                "Plan not found | type: %s | id: %s | event: %s | variant_id: %s",
                data_obj.get('type'),
                data_obj.get('id'),
                event_name,
                variant_id
            )
            raise BadRequest(
                message=f"Subscription plan not found for variant_id: {variant_id}",
                context={"error": ResponseCode.GET_ONE_FAILED}
            )

        # Get user by email
        user = await UserRepository.get_by_email(user_email)
        if not user:
            error_logger.exception(
                "User not found | type: %s | id: %s | event: %s | email: %s",
                data_obj.get('type'),
                data_obj.get('id'),
                event_name,
                user_email
            )
            raise BadRequest(
                message=f"User not found for email: {user_email}",
                context={"error": ResponseCode.GET_ONE_FAILED}
            )

        # Handle different subscription events
        subscription_status = attributes.get('status', '').lower()
        mapped_status = UserSubscriptionRepository.map_lemon_status(subscription_status)

        if event_name in ["subscription_created", "subscription_updated"]:
            if mapped_status in [UserSubscription.Status.ACTIVE]:
                # Subscribe or update subscription
                subscription_data = {
                    "user_id": user['id'],
                    "plan_id": plan['id'],
                    "start_date": datetime.now(),
                    "partner": "lemonsqueezy",
                    "partner_id": data_obj.get('id')
                }
                await UserSubscriptionRepository.subscribe_plan(**subscription_data)
                UserFeatureRepository.clear_cache(user['id'])
            elif mapped_status == UserSubscription.Status.INACTIVE:
                # Update subscription status to inactive
                await UserSubscriptionRepository.update_by_query(
                    update_data={
                        "status": UserSubscription.Status.INACTIVE,
                        "updated_at": datetime.now(),
                        "end_date": datetime.now()  # Set end date when subscription expires
                    },
                    q=UserSubscriptionQuery(
                        user_id=user['id'],
                        subscription_plan_id=plan['id'],
                        partner="lemonsqueezy",
                        partner_id=data_obj.get('id')
                    )
                )
                UserFeatureRepository.clear_cache(user['id'])
            else:
                error_logger.warning(
                    "Subscription created/updated with unexpected status | type: %s | id: %s | event: %s | status: %s",
                    data_obj.get('type'),
                    data_obj.get('id'),
                    event_name,
                    subscription_status
                )
        elif event_name == "subscription_cancelled":
            # Update subscription status to cancelled
            await UserSubscriptionRepository.update_by_query(
                update_data={
                    "status": UserSubscription.Status.CANCELLED,
                    "updated_at": datetime.now(),
                },
                q=UserSubscriptionQuery(
                    user_id=user['id'],
                    subscription_plan_id=plan['id'],
                    partner="lemonsqueezy",
                    partner_id=data_obj.get('id')
                )
            )
            UserFeatureRepository.clear_cache(user['id'])

        return json_response(Response(
            error=0,
            message=SUCCESS_MESSAGE
        ), 200)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="Failed to process webhook event",
            context={"error": ResponseCode.SERVER_ERROR}
        )
