import os
from enum import IntEnum
from json import dumps
from typing import Union
from pydantic import BaseModel, Field
from sanic import json
from sanic.response import J<PERSON><PERSON>esponse
from sanic_ext import openapi
from utils.helpers import ExtendedJsonEncoder

SUCCESS_MESSAGE = os.getenv("SUCCESS_MESSAGE", "success")
INVALID_REQUEST_MESSAGE = os.getenv(
    "INVALID_REQUEST_MESSAGE", "invalid request")


class ResponseCode(IntEnum):
    SUCCESS = 0
    UNKNOWN_ERROR = 1
    SERVER_ERROR = 2
    DATABASE_ERROR = 3
    UNAUTHORIZED = 4
    CLIENT_BLOCKED = 5
    METHOD_NOT_ALLOWED = 6
    CLIENT_BAD_REQUEST = 7
    EXTERNAL_SERVICE_ERROR = 8
    PERMISSION_DENIED = 9

    CREATE_FAILED = 10
    UPDATE_FAILED = 11
    DELETE_FAILED = 12
    GET_LIST_FAILED = 13
    GET_ONE_FAILED = 14  # Not found
    INSUFFICIENT_FUND = 15


@openapi.component
class ListResponse(BaseModel):
    total: int = Field(..., example=1)
    page: int = Field(..., example=1)
    page_size: int = Field(..., example=10)
    records: list = Field(...)


@openapi.component
class Response(BaseModel):
    error: int = Field(...)
    message: str = Field(...)
    data: Union[dict, ListResponse] = Field(default_factory=dict)


@openapi.component
class EmptyObject(BaseModel):
    pass


@openapi.component(name="UnauthorizedResponse")
class UnauthorizedResponse(BaseModel):
    error: int = Field(..., example=ResponseCode.UNAUTHORIZED)
    message: str = Field(..., example="you are unauthorized")
    data: EmptyObject = EmptyObject()


@openapi.component(name="NotFoundResponse")
class NotFoundResponse(BaseModel):
    error: int = Field(..., example=ResponseCode.GET_ONE_FAILED)
    message: str = Field(..., example="not found")
    data: EmptyObject = EmptyObject()


@openapi.component(name="BadRequestResponse")
class BadRequestResponse(BaseModel):
    error: int = Field(..., example=ResponseCode.CREATE_FAILED)
    message: str = Field(..., example="bad request")
    data: dict = Field(..., example={})


OPENAPI_BAD_REQUEST_RESPONSE = openapi.definitions.Response(
    status=400, description="Bad request",
    content={
        "application/json": {
            "schema": {
                "type": "object",
                "properties": {
                    "error": {"type": "integer", "description": "Error code, 7 means bad request", "example": 7},
                    "message": {"type": "string", "description": "Response message", "example": "Bad request"},
                    "data": {"type": "object", "description": "Response data", "example": {}},
                },
            },
        },
    }
)

OPENAPI_NOT_FOUND_RESPONSE = openapi.definitions.Response(
    status=404, description="Not found",
    content={
        "application/json": {
            "schema": {
                "type": "object",
                "properties": {
                    "error": {"type": "integer", "description": "Error code, 14 means not found", "example": 14},
                    "message": {"type": "string", "description": "Response message", "example": "Not found"},
                    "data": {"type": "object", "description": "Response data", "example": {}},
                },
            },
        },
    }
)


OPENAPI_UNAUTHORIZED_RESPONSE = openapi.definitions.Response(
    status=401, description="Unauthorized",
    content={
        "application/json": {
            "schema": {
                "type": "object",
                "properties": {
                    "error": {"type": "integer", "description": "Error code, 4 means unauthorized", "example": 4},
                    "message": {"type": "string", "description": "Response message", "example": "you are unauthorized"},
                    "data": {"type": "object", "description": "Response data", "example": {}},
                },
            },
        },
    }
)


def json_response(body: Union[Response, dict], status_code: int, headers: dict[str, str] = None) -> JSONResponse:
    """
    Construct a JSON response for a given response body and status code.

    Args:
        body (Response): the response body
        status_code (int): the HTTP status code
        headers (dict[str, str], optional): additional headers to include in the response.
            Defaults to None.

    Returns:
        JSONResponse: the constructed JSON response
    """
    return json(body.model_dump(), status=status_code, headers=headers, dumps=dumps, cls=ExtendedJsonEncoder)
