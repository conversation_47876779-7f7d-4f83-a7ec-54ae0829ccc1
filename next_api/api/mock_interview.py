from typing import Union
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest, NotFound
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode, \
    OPENAPI_BAD_REQUEST_RESPONSE, OPENAPI_NOT_FOUND_RESPONSE, OPENAPI_UNAUTHORIZED_RESPONSE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.mock_interview import MockInterviewRepository, MockInterviewQuery

bp = Blueprint("mock_interview")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Mock Interview")
@openapi.definition(
    summary="Get a list of mock interviews",
    description="This endpoint is used to get mock interview list.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "page", int, location="query", description="Page number", required=False, default=1),
        openapi.definitions.Parameter(
            "page_size", int, location="query", description="Number of records per page", required=False, default=10),
        openapi.definitions.Parameter(
            "id", str, location="query", description="Mock Interview ID", required=False),
        openapi.definitions.Parameter(
            "id__in", Union[list[str], str], location="query", description="Mock Interview ID list", required=False),
        openapi.definitions.Parameter(
            "id__not_in", Union[list[str], str],
            location="query", description="Mock Interview ID list", required=False
        ),
        openapi.definitions.Parameter(
            "user_id", str, location="query", description="User ID", required=False),
        openapi.definitions.Parameter(
            "user_id__in", Union[list[str], str], location="query", description="User ID list", required=False),
        openapi.definitions.Parameter(
            "user_id__not_in", Union[list[str], str], location="query", description="User ID list", required=False),
        openapi.definitions.Parameter(
            "position", str, location="query", description="Position", required=False),
        openapi.definitions.Parameter(
            "position__contains", str, location="query", description="Position contains", required=False),
        openapi.definitions.Parameter(
            "position__icontains", str, location="query",
            description="Position contains, case insensitive", required=False
        ),
        openapi.definitions.Parameter(
            "job_description__contains", str, location="query",
            description="Job description contains", required=False
        ),
        openapi.definitions.Parameter(
            "job_description__icontains", str, location="query",
            description="Job description contains, case insensitive", required=False
        ),
        openapi.definitions.Parameter(
            "status", int, location="query",
            description="Status", required=False
        ),
        openapi.definitions.Parameter(
            "status__in", Union[list[int], int], location="query",
            description="Status list", required=False
        ),
        openapi.definitions.Parameter(
            "status__not_in", Union[list[int], int], location="query",
            description="Status list", required=False
        ),
        openapi.definitions.Parameter(
            "created_at", str, location="query",
            description="Created at", required=False
        ),
        openapi.definitions.Parameter(
            "created_at__gte", str, location="query",
            description="Created at greater than or equal to", required=False
        ),
        openapi.definitions.Parameter(
            "created_at__lte", str, location="query",
            description="Created at less than or equal to", required=False
        ),
        openapi.definitions.Parameter(
            "updated_at", str, location="query",
            description="Updated at", required=False
        ),
        openapi.definitions.Parameter(
            "updated_at__gte", str, location="query",
            description="Updated at greater than or equal to", required=False
        ),
        openapi.definitions.Parameter(
            "updated_at__lte", str, location="query",
            description="Updated at less than or equal to", required=False
        ),
        openapi.definitions.Parameter(
            "order_by", str, location="query", description="Order by", required=False),
    ],
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully retrieved mock interview list",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {"type": "integer", "description": "Total number of records"},
                                    "page": {"type": "integer", "description": "Current page number"},
                                    "page_size": {"type": "integer", "description": "Number of records per page"},
                                    "records": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "string", "description": "UUID of mock interview"
                                                },
                                                "user_id": {
                                                    "type": "string", "description": "UUID of user"
                                                },
                                                "position": {
                                                    "type": "string", "description": "Position title"
                                                },
                                                "job_description": {
                                                    "type": "string", "description": "Job description"
                                                },
                                                "job_description_id": {
                                                    "type": "string", "description": "UUID of job description"
                                                },
                                                "status": {
                                                    "type": "integer", "description": "Status of mock interview"
                                                },
                                                "created_at": {
                                                    "type": "string", "format": "date-time",
                                                    "description": "Creation time"
                                                },
                                                "updated_at": {
                                                    "type": "string", "format": "date-time",
                                                    "description": "Update time"
                                                },
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def get_list(request: Request):
    """Get a paginated list of mock interviews."""
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = MockInterviewQuery(**request.args)
    records, total = await MockInterviewRepository.get_list(query, pagination)

    # If records is empty, return empty list response
    if not records:
        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=ListResponse(
                total=0,
                page=pagination.page,
                page_size=pagination.page_size,
                records=[]
            )
        )
        return json_response(body, 200)

    body = Response(
        error=ResponseCode.SUCCESS,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Mock Interview")
@openapi.definition(
    summary="Create a mock interview",
    description="Create a new mock interview record",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
    ],
    body=openapi.definitions.RequestBody(
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "required": ["user_id", "position", "job_description"],
                    "properties": {
                        "user_id": {"type": "string", "description": "UUID of user"},
                        "position": {"type": "string", "description": "Position title"},
                        "job_description": {"type": "string", "description": "Job description"},
                        "status": {"type": "integer", "description": "Status of mock interview", "default": 0},
                    }
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully created mock interview",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "user_id": {"type": "string"},
                                    "position": {"type": "string"},
                                    "job_description": {"type": "string"},
                                    "job_description_id": {"type": "string"},
                                    "status": {"type": "integer"},
                                    "questions": {"type": "array", "items": {"type": "object"}},
                                    "created_at": {"type": "string", "format": "date-time"},
                                    "updated_at": {"type": "string", "format": "date-time"}
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def create(request: Request):
    """Create a new mock interview."""
    try:
        mock_interview = await MockInterviewRepository.create_one(request.json)
        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=dict(mock_interview)
        )
        return json_response(body, 200)
    except Exception as e:
        error_logger.exception("Failed to create mock interview: %s", e)
        raise BadRequest("Failed to create mock interview")


@bp.route("/<mock_interview_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Mock Interview")
@openapi.definition(
    summary="Get a mock interview",
    description="Get a specific mock interview by ID",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "mock_interview_id", str, location="path", required=True, description="Mock Interview ID"),
    ],
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully retrieved mock interview",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "user_id": {"type": "string"},
                                    "position": {"type": "string"},
                                    "job_description": {"type": "string"},
                                    "job_description_id": {"type": "string"},
                                    "status": {"type": "integer"},
                                    "created_at": {"type": "string", "format": "date-time"},
                                    "updated_at": {"type": "string", "format": "date-time"}
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def get_one(_: Request, mock_interview_id: str):
    """Get a specific mock interview."""
    query = MockInterviewQuery(id=mock_interview_id)
    mock_interview = await MockInterviewRepository.get_one(query)

    if not mock_interview:
        raise NotFound("Mock interview not found")

    body = Response(
        error=ResponseCode.SUCCESS,
        message=SUCCESS_MESSAGE,
        data=dict(mock_interview)
    )
    return json_response(body, 200)


@bp.route("/<mock_interview_id:str>", methods=["PUT"], strict_slashes=False)
@ openapi.definition(
    summary="Update a mock interview",
    description="Update an existing mock interview",
    tag=["Mock Interview"],
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "mock_interview_id", str, location="path", required=True, description="Mock Interview ID"),
    ],
    body=openapi.definitions.RequestBody(
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "position": {"type": "string", "description": "Position title"},
                        "job_description": {"type": "string", "description": "Job description"},
                        "status": {"type": "integer", "description": "Status of mock interview"},
                    }
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully updated mock interview",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "user_id": {"type": "string"},
                                    "position": {"type": "string"},
                                    "job_description": {"type": "string"},
                                    "job_description_id": {"type": "string"},
                                    "status": {"type": "integer"},
                                    "questions": {"type": "array", "items": {"type": "object"}},
                                    "created_at": {"type": "string", "format": "date-time"},
                                    "updated_at": {"type": "string", "format": "date-time"}
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@ require_token
async def update(request: Request, mock_interview_id: str):
    """Update a specific mock interview."""
    try:
        affected_rows, updated_mock_interview = await MockInterviewRepository.update(mock_interview_id, request.json)

        if affected_rows == 0:
            raise NotFound("Mock interview not found")

        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=dict(updated_mock_interview)
        )
        return json_response(body, 200)
    except Exception as e:
        error_logger.exception("Failed to update mock interview: %s", e)
        raise BadRequest("Failed to update mock interview")


@bp.route("/<mock_interview_id:str>/questions", methods=["POST"], strict_slashes=False)
@ openapi.definition(
    summary="Add questions to a mock interview",
    description="Add one or more questions to an existing mock interview by their IDs",
    tag=["Mock Interview"],
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "mock_interview_id", str, location="path", required=True, description="Mock Interview ID"),
    ],
    body=openapi.definitions.RequestBody(
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "required": ["questions"],
                    "properties": {
                        "questions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Array of question IDs to add to the mock interview"
                        }
                    }
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully added questions",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "mock_interview": {
                                        "type": "object",
                                        "properties": {
                                            "id": {"type": "string"},
                                            "user_id": {"type": "string"},
                                            "position": {"type": "string"},
                                            "job_description": {"type": "string"},
                                            "job_description_id": {"type": "string"},
                                            "status": {"type": "integer"},
                                            "created_at": {"type": "string", "format": "date-time"},
                                            "updated_at": {"type": "string", "format": "date-time"}
                                        }
                                    },
                                    "total_questions": {"type": "integer"}
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@ require_token
async def add_questions(request: Request, mock_interview_id: str):
    """Add questions to a specific mock interview by their IDs."""
    try:
        question_ids = request.json.get("questions", [])
        if not question_ids:
            raise BadRequest("No question IDs provided")

        # Add the questions by their IDs
        mock_interview, total_questions = await MockInterviewRepository.add_questions(mock_interview_id, question_ids)

        if not mock_interview:
            raise NotFound("Mock interview not found")

        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data={
                "mock_interview": dict(mock_interview),
                "total_questions": total_questions
            }
        )
        return json_response(body, 200)
    except Exception as e:
        error_logger.exception("Failed to add questions to mock interview: %s", e)
        raise BadRequest("Failed to add questions to mock interview")


@bp.route("/<mock_interview_id:str>/questions", methods=["DELETE"], strict_slashes=False)
@ openapi.definition(
    summary="Remove questions from a mock interview",
    description="Remove one or more questions from an existing mock interview by their IDs",
    tag=["Mock Interview"],
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "mock_interview_id", str, location="path", required=True, description="Mock Interview ID"),
    ],
    # body=openapi.definitions.RequestBody(
    #     content={
    #         "application/json": {
    #             "schema": {
    #                 "type": "object",
    #                 "required": ["questions"],
    #                 "properties": {
    #                     "questions": {
    #                         "type": "array",
    #                         "items": {"type": "string"},
    #                         "description": "Array of question IDs to remove from the mock interview"
    #                     }
    #                 }
    #             }
    #         }
    #     }
    # ),
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully removed questions",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "mock_interview": {
                                        "type": "object",
                                        "properties": {
                                            "id": {"type": "string"},
                                            "user_id": {"type": "string"},
                                            "position": {"type": "string"},
                                            "job_description": {"type": "string"},
                                            "job_description_id": {"type": "string"},
                                            "status": {"type": "integer"},
                                            "created_at": {"type": "string", "format": "date-time"},
                                            "updated_at": {"type": "string", "format": "date-time"}
                                        }
                                    },
                                    "total_questions": {"type": "integer"}
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@ require_token
async def remove_questions(request: Request, mock_interview_id: str):
    """Remove questions from a specific mock interview by their IDs."""
    try:
        question_ids = request.json.get("questions", [])
        if not question_ids:
            raise BadRequest("No question IDs provided")

        # Remove the questions by their IDs
        record, no_questions = await MockInterviewRepository.remove_questions(mock_interview_id, question_ids)

        if not record:
            raise NotFound("Mock interview not found")

        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data={
                "mock_interview": dict(record),
                "total_questions": no_questions
            }
        )
        return json_response(body, 200)
    except Exception as e:
        error_logger.exception(
            "Failed to remove questions from mock interview: %s", e)
        raise BadRequest("Failed to remove questions from mock interview")


@bp.route("/<mock_interview_id:str>/questions", methods=["GET"], strict_slashes=False)
@ openapi.definition(
    summary="Get questions in a mock interview",
    description="Get a paginated list of questions in a mock interview",
    tag=["Mock Interview"],
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "mock_interview_id", str, location="path", required=True, description="Mock Interview ID"),
        openapi.definitions.Parameter(
            "page", int, location="query", description="Page number", required=False, default=1),
        openapi.definitions.Parameter(
            "page_size", int, location="query", description="Number of records per page", required=False, default=10),
    ],
    response=[
        openapi.definitions.Response(
            status=200,
            description="Successfully retrieved questions",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {"type": "integer", "description": "Total number of records"},
                                    "page": {"type": "integer", "description": "Current page number"},
                                    "page_size": {"type": "integer", "description": "Number of records per page"},
                                    "records": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "id": {
                                                    "type": "string",
                                                    "description": "UUID of question",
                                                },
                                                "title": {
                                                    "type": "string",
                                                    "description": "Question content",
                                                },
                                                "content": {
                                                    "type": "string",
                                                    "description": "Question content",
                                                },
                                                "difficulty": {
                                                    "type": "integer",
                                                    "description": "Difficulty level (0: Easy, 1: Medium, 2: Hard)",
                                                },
                                                "created_at": {
                                                    "type": "string", "format": "date-time",
                                                    "description": "Creation time",
                                                },
                                                "updated_at": {
                                                    "type": "string", "format": "date-time",
                                                    "description": "Update time",
                                                },
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@ require_token
async def get_questions_by_mock_interview(request: Request, mock_interview_id: str):
    """Get a paginated list of questions in a mock interview."""
    try:
        pagination = Pagination(
            page=int(request.args.get('page', 1)),
            page_size=int(request.args.get('page_size', 10))
        )

        # Get the questions (as dictionaries for JSON response)
        questions, total = await MockInterviewRepository.get_questions(
            mock_interview_id,
            pagination,
            as_dict=True  # Convert to dict for JSON response
        )

        body = Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=ListResponse(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                records=questions
            )
        )
        return json_response(body, 200)
    except NotFound:
        raise
    except Exception as e:
        error_logger.exception(
            "Failed to get questions from mock interview: %s", e)
        raise BadRequest("Failed to get questions from mock interview")
