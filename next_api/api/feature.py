from datetime import datetime
from typing import List
from pydantic import BaseModel, Field
from tortoise.exceptions import IntegrityError
from sanic import Blueprint, Request
from sanic.log import error_logger
from sanic.exceptions import BadRequest
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, ResponseCode, SUCCESS_MESSAGE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.feature import FeatureRepository, FeatureQuery
from models.feature import Feature

bp = Blueprint("feature")  # pylint: disable=invalid-name


@openapi.component
class RequestFeatureSchema(BaseModel):
    code: str = Field(..., description="Code of feature")
    name: str = Field(..., description="Name of feature")
    description: str = Field("", description="Description of feature")
    default_value: dict = Field(None, description="Default value of feature")
    visibility: int = Field(
        Feature.Visibility.HIDDEN,
        description="Visibility of feature (0: Hidden, 1: Internal, 2: Public)"
    )


@openapi.component
class ResponseFeatureSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of feature")
    code: str = Field(..., description="Code of feature")
    name: str = Field(..., description="Name of feature")
    description: str = Field("", description="Description of feature")
    default_value: dict = Field(None, description="Default value of feature")
    visibility: int = Field(..., description="Visibility of feature")
    created_at: datetime = Field(..., description="Created at")
    updated_at: datetime = Field(..., description="Updated at")


@openapi.component
class DetailFeatureSchema(ResponseFeatureSchema):
    plans: List[dict] = Field([], description="Associated subscription plans")


@openapi.tag("Feature")
@bp.route("/", methods=["GET"], strict_slashes=False)
@require_token
async def get_list(request: Request):
    """Get list of all features

    openapi:
    ---
    summary: Get list of all features
    description: This endpoint is used to get list of all features.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Page size
      example: 10
    - name: name
      in: query
      schema:
        type: string
      description: Filter by exact name match
    - name: name__contains
      in: query
      schema:
        type: string
      description: Filter by name containing string (case sensitive)
    - name: name__icontains
      in: query
      schema:
        type: string
      description: Filter by name containing string (case insensitive)
    - name: name__startswith
      in: query
      schema:
        type: string
      description: Filter by name starting with string
    - name: name__endswith
      in: query
      schema:
        type: string
      description: Filter by name ending with string
    - name: code
      in: query
      schema:
        type: string
      description: Filter by exact code match
    - name: code__contains
      in: query
      schema:
        type: string
      description: Filter by code containing string (case sensitive)  
    - name: code__icontains
      in: query
      schema:
        type: string
      description: Filter by code containing string (case insensitive)
    - name: code__startswith
      in: query
      schema:
        type: string
      description: Filter by code starting with string
    - name: code__endswith
      in: query
      schema:
        type: string
      description: Filter by code ending with string
    - name: visibility
      in: query
      schema:
        type: integer
      description: Filter by exact visibility match
    - name: visibility__in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Filter by visibility in list (comma separated)
    - name: visibility__not_in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: Filter by visibility not in list (comma separated)
    - name: order_by
      in: query
      schema:
        type: array
        items:
          type: string
          enum: ["id", "-id", "name", "-name", "visibility", "-visibility",
                 "created_at", "-created_at", "updated_at", "-updated_at"]
      description: Order by fields (prefix with `-` for descending)
    responses:
      200:
        description: Successfully retrieved features
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array      
                      items:
                        ref: "#/components/schemas/ResponseFeatureSchema"
                  required: ["total", "page", "page_size", "records"]
              required: ["error", "message", "data"]
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = FeatureQuery(**request.args)
    records, total = await FeatureRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@openapi.tag("Feature")
@bp.route("/<feature_id:str>", methods=["GET"], strict_slashes=False)
@require_token
async def get_detail(request: Request, feature_id: str):
    """Get a feature by ID

    openapi:
    ---
    summary: Get a feature by ID
    description: This endpoint is used to get a feature by ID with its associated plans.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: feature_id
      in: path
      schema:
        type: string
        format: uuid
      required: true
      description: ID of feature
    - name: prefetch_plans
      in: query
      schema:
        type: boolean
        default: false
      description: Whether to fetch associated plans
    responses:
      200:
        description: Successfully retrieved a feature
        content:
          application/json:
            schema:
              ref: "#/components/schemas/DetailFeatureSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    prefetch_plans = request.args.get('prefetch_plans', ['false'])[0].lower() == 'true'
    record = await FeatureRepository.get_one(feature_id, prefetch_plans=prefetch_plans)
    if not record:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="feature not found"
        ), 404)

    body = Response(error=0, message=SUCCESS_MESSAGE, data=record)
    return json_response(body, 200)


@openapi.tag("Feature")
@bp.route("/", methods=["POST"], strict_slashes=False)
@require_token
async def create(request):
    """Create a new feature

    openapi:
    ---
    summary: Create a new feature
    description: This endpoint is used to create a new feature.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestFeatureSchema"
    responses:
      201:
        description: Successfully created feature
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ResponseFeatureSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        record = await FeatureRepository.create_one(request.json)
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.CREATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot create feature",
            context={"error": ResponseCode.CREATE_FAILED}
        )
    result = await FeatureRepository.get_one(record.id)
    body = Response(error=0, message=SUCCESS_MESSAGE, data=result)
    return json_response(body, 201)


@openapi.tag("Feature")
@bp.route("/<feature_id:str>", methods=["PATCH"], strict_slashes=False)
@require_token
async def update(request, feature_id: str):
    """Update a feature

    openapi:
    ---
    summary: Update a feature
    description: This endpoint is used to update an existing feature.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: id
      in: path
      schema:
        type: string
      required: true
      description: ID of feature
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestFeatureSchema"
    responses:
      200:
        description: Successfully updated feature
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ResponseFeatureSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        affected_rows, record = await FeatureRepository.update_one(feature_id, request.json)
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.UPDATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot update feature",
            context={"error": ResponseCode.UPDATE_FAILED}
        )

    if affected_rows > 0:
        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=record),
            200
        )
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="cannot update feature"),
        404
    )
