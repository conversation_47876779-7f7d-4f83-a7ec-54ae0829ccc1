import jwt
import requests
from datetime import datetime, timedelta
from decimal import DecimalException, InvalidOperation
from pydantic import BaseModel, Field
from sanic import Blueprint, Request, response
from sanic_ext import openapi
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from tortoise.exceptions import IntegrityError
from uuid_extensions import uuid7

from models.user import User
from repositories.token import AccessTokenRepository
from repositories.user import UserRepository
from repositories.user_subscription import UserSubscriptionRepository
from settings import JWT_EXPIRE_TIME, JWT_REFRESH_EXPIRE_TIME, AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_CALLBACK_URL, AUTH0_CLIENT_SECRET, LOOPS_API_KEY
from utils.email import LoopsClient
from api.base import json_response, Response, SUCCESS_MESSAGE, OPENAPI_BAD_REQUEST_RESPONSE, ResponseCode
from api.exceptions import Conflict
from api.middlewares import require_token, check_jwt_token

bp = Blueprint("auth", url_prefix="/api/v1/auth")


@openapi.component
class ResponseTokenSchema(BaseModel):
    access_token: str = Field(description="Access token")
    refresh_token: str = Field(description="Refresh token")
    expired_at: str = Field(description="Expired at")


@openapi.component
class ActivationRequestSchema(BaseModel):
    user_id: str = Field(description="User ID", example="123e4567-e89b-12d3-a456-************")
    secret: str = Field(description="Verification secret")


@openapi.component
class ActivationResponseSchema(BaseModel):
    error: int = Field(description="Error code", example=0)
    message: str = Field(description="Response message", example="Account activated successfully")
    data: None = Field(description="Response data", example=None)


@bp.route("/register", methods=["POST"], strict_slashes=False)
@openapi.tag("Auth")
@openapi.definition(
    summary="Register a new user",
    description="This endpoint is used to register a new user.",
    body=openapi.definitions.RequestBody(
        description="User data",
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "email": {
                            "type": "string", "format": "email", "description": "Email address, must be unique",
                            "example": "<EMAIL>"
                        },
                        "fullname": {
                            "type": "string", "description": "Full name", "example": "fullname"
                        },
                        "password": {
                            "type": "string", "description": "Password, should be hashed from client first",
                            "example": "password"
                        },
                        "confirm_password": {
                            "type": "string", "description": "Confirm password, should be the same as password",
                            "example": "password"
                        },
                        "min_salary": {
                            "type": "number", "description": "Minimum salary", "example": 1000
                        },
                        "expected_salary": {
                            "type": "number", "description": "Expected salary", "example": 1000
                        },
                        "exp_level": {
                            "type": "integer", "description": "Experience level", "example": 1
                        },
                        "linked_in": {
                            "type": "string", "description": "LinkedIn URL",
                            "example": "https://www.linkedin.com/in/username"
                        },
                    },
                    "required": ["email", "password", "confirm_password"]
                },
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=201, description="Successfully registered a new user",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code", "example": 0},
                            "message": {"type": "string", "description": "Success message", "example": SUCCESS_MESSAGE},
                            "data": {"type": "object", "description": "User data", "example": {}}
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
    ]
)
async def register(request: Request):
    data = request.json
    if data.get("password", "") == "":
        raise BadRequest(message="password is required", context={
            "error": ResponseCode.CREATE_FAILED,
        })
    if data["password"] != data.pop("confirm_password"):
        raise BadRequest(message="password and confirm password do not match", context={
            "error": ResponseCode.CREATE_FAILED,
        })
    try:
        data['status'] = User.Status.IN_VERIFICATION.value
        record = await UserRepository.create_one(data)
        await UserSubscriptionRepository.subscribe_plan(
            user_id=record['id'],
            plan_id=uuid7(0),
            partner=None,
            partner_id=None
        )
    except (DecimalException, InvalidOperation) as e:
        error_logger.exception(e)
        raise BadRequest(message="cannot create user")
    except IntegrityError:
        raise Conflict(message="email already exists", context={
            "error": ResponseCode.CREATE_FAILED,
        })


@bp.route("/login", methods=["POST"], strict_slashes=False)
@openapi.tag("Auth")
@openapi.definition(
    summary="Login",
    description="This endpoint is used to login",
    body=openapi.definitions.RequestBody(
        description="User data",
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "email": {"type": "string", "format": "email", "description": "email", "example": "email"},
                        "password": {"type": "string", "description": "Password", "example": "password"},
                    },
                    "required": ["email", "password"]
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully logged in",
            content={
                "application/json": {
                    "schema": {
                        "$ref": "#/components/schemas/ResponseTokenSchema",
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
    ]
)
async def login(request: Request):
    data = request.json
    if data.get("password", "") == "":
        raise BadRequest(message="password is required", context={
            "error": ResponseCode.GET_ONE_FAILED,
        })
    user = await UserRepository.login(data["email"], data["password"])

    at_exp = datetime.now() + timedelta(seconds=JWT_EXPIRE_TIME)
    rt_exp = datetime.now() + timedelta(seconds=JWT_REFRESH_EXPIRE_TIME)

    generator = AccessTokenRepository()
    at, rt = await generator.generate_user_tokens(user, options={
        "at_exp": at_exp,
        "rt_exp": rt_exp,
    })
    data = {
        "access_token": at,
        "refresh_token": rt,
        "expired_at": at_exp,
    }
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=data
    )
    return json_response(body, 200)


@bp.route("/refresh-token", methods=["POST"], strict_slashes=False)
@openapi.tag("Auth")
@require_token
async def refresh(request: Request):
    """
    Refresh token

    openapi:
    ---
    summary: Refresh token
    description: This endpoint is used to refresh token.
    parameters:
    - name: Authorization
      in: header
      description: Refresh token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    responses:
      200:
        description: Successfully refreshed token
        content:
          application/json:
            schema:
              ref: "#/components/schemas/ResponseTokenSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
    """
    rt_claims = check_jwt_token(request)
    if not rt_claims:
        raise BadRequest(message="Invalid token", context={
            "error": ResponseCode.UNAUTHORIZED,
        })

    at_exp = datetime.now() + timedelta(seconds=JWT_EXPIRE_TIME)
    rt_exp = datetime.now() + timedelta(seconds=JWT_REFRESH_EXPIRE_TIME)

    generator = AccessTokenRepository()
    at, rt = await generator.refresh_token(request.ctx.user, rt_claims, options={
        "at_exp": at_exp,
        "rt_exp": rt_exp,
    })
    data = {
        "access_token": at,
        "refresh_token": rt,
        "expired_at": at_exp,
    }
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=data
    )
    return json_response(body, 200)


@openapi.tag("Auth")
@bp.route("/logout", methods=["POST"], strict_slashes=False)
@require_token
async def logout(request: Request):
    """
    Logout

    openapi:
    ---
    summary: Logout
    description: This endpoint is used to logout.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    responses:
      200:
        description: Successfully logged out
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: SUCCESS_MESSAGE
                data:
                  type: object
                  example: {}
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
    """
    repo = AccessTokenRepository()
    at_model = await repo.get_one(request.ctx.at_claims["jti"])
    repo.revoke_token(at_model)
    return json_response(Response(error=0, message=SUCCESS_MESSAGE), 200)


@bp.route("/auth0/login", methods=["POST", "GET"], strict_slashes=False)
@openapi.tag("Auth")
async def auth0_login(request: Request):  # pylint: disable=unused-argument
    """
    Initiate Auth0 login flow

    openapi:
    ---
    summary: Initiate Auth0 login
    description: Start the Auth0 authentication flow by redirecting to Auth0 authorization URL
    responses:
      302:
        description: Redirect to Auth0 authorization URL
        headers:
          Location:
            schema:
              type: string
              description: The URL to redirect the user to Auth0
              example: "https://your-tenant.auth0.com/authorize?response_type=code&..."
    """
    auth_url = (
        f"https://{AUTH0_DOMAIN}/authorize?"
        f"response_type=code&"
        f"client_id={AUTH0_CLIENT_ID}&"
        f"redirect_uri={AUTH0_CALLBACK_URL}&"
        f"scope=openid email profile"
    )
    return response.redirect(auth_url, status=302)


@openapi.tag("Auth")
@bp.route("/auth0/callback", methods=["GET"], strict_slashes=False)
async def auth0_callback(request: Request):
    """
    Auth0 callback handler

    openapi:
    ---
    summary: Auth0 callback handler
    description: Handles the callback from Auth0 authentication, creates/updates user and returns tokens
    parameters:
      - name: code
        in: query
        description: Authorization code from Auth0
        required: true
        schema:
          type: string
    responses:
      200:
        description: Successfully authenticated
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                data:
                  type: object
                  properties:
                    access_token:
                      type: string
                      description: JWT access token
                    refresh_token:
                      type: string
                      description: JWT refresh token
                    expired_at:
                      type: string
                      format: date-time
                      description: Token expiration time
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
    """
    code = request.args.get('code')
    if not code:
        raise BadRequest(message="Authorization code is required", context={
            "error": ResponseCode.GET_ONE_FAILED,
        })

    headers = {
        "Content-Type": "application/json",
    }

    # Exchange the authorization code for tokens
    token_url = f"https://{AUTH0_DOMAIN}/oauth/token"
    token_payload = {
        "grant_type": "authorization_code",
        "client_id": AUTH0_CLIENT_ID,
        "client_secret": AUTH0_CLIENT_SECRET,
        "code": code,
        "redirect_uri": AUTH0_CALLBACK_URL
    }

    resp = requests.post(token_url, json=token_payload, headers=headers, timeout=10)
    if resp.status_code != 200:
        error_logger.exception("Failed to get tokens from Auth0: %s", resp.text)
        raise BadRequest(message="Failed to authenticate", context={
            "error": ResponseCode.GET_ONE_FAILED,
        })
    tokens = resp.json()
    if "id_token" not in tokens:
        error_logger.exception("Failed to get tokens from Auth0: %s", tokens)
        raise BadRequest(message="Failed to authenticate", context={
            "error": ResponseCode.GET_ONE_FAILED,
        })

    # Decode ID Token
    id_token = tokens["id_token"]
    user_info = jwt.decode(id_token, options={"verify_signature": False})

    try:
        # Prepare user data from Auth0 user info
        user_data = {
            "email": user_info.get("email"),
            "fullname": user_info.get("name", ""),
            "picture": user_info.get("picture", ""),
            "auth0_id": user_info.get("sub"),  # Auth0 unique identifier
            "provider": User.Provider.EMAIL,  # Or determine from auth0 connection
            "status": User.Status.ACTIVE,  # New users from Auth0 are typically pre-verified
            "locale": user_info.get("locale", "en-US"),
        }

        # Upsert user in database
        user, is_created = await UserRepository.upsert_by_email(user_data)

        if is_created:
            lc = LoopsClient(LOOPS_API_KEY)
            lc.send_register_email({
                "to": user["email"],
                "fullname": user["fullname"]
            })

        # Generate tokens
        at_exp = datetime.now() + timedelta(seconds=JWT_EXPIRE_TIME)
        rt_exp = datetime.now() + timedelta(seconds=JWT_REFRESH_EXPIRE_TIME)

        generator = AccessTokenRepository()
        at, rt = await generator.generate_user_tokens(user, options={
            "at_exp": at_exp,
            "rt_exp": rt_exp,
        })

        response_data = {
            "access_token": at,
            "refresh_token": rt,
            "expired_at": at_exp,
            "is_new_user": is_created
        }

        body = Response(
            error=0,
            message=SUCCESS_MESSAGE,
            data=response_data
        )
        return json_response(body, 200)

    except Exception as e:
        error_logger.exception("Failed to process Auth0 callback: %s", str(e))
        raise BadRequest(message="Failed to process authentication", context={
            "error": ResponseCode.CREATE_FAILED,
        })
