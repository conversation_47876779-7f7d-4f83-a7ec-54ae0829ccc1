from datetime import datetime
from typing import List
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, ResponseCode, SUCCESS_MESSAGE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.role import RoleRepository, RoleQuery

bp = Blueprint("role")  # pylint: disable=invalid-name


@openapi.component
class RoleSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of role",
                    example="123e4567-e89b-12d3-a456-************")
    name: str = Field(..., description="Name of role",
                      example="User Admin", unique=True)
    description: str = Field(
        "", description="Description of role", example="Admin can create user")
    created_at: datetime = Field(..., description="Created at")
    updated_at: datetime = Field(..., description="Updated at")


@openapi.component
class ShortPermissionSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of permission",
                    example="06751509-0721-72d7-8000-af687824111e")
    name: str = Field(..., description="Code name of permission",
                      example="admin:user:create", unique=True)


@openapi.component
class DetailRoleSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of role",
                    example="123e4567-e89b-12d3-a456-************")
    name: str = Field(..., description="Name of role",
                      example="User Admin", unique=True)
    description: str = Field(
        "", description="Description of role", example="Admin can create user")
    created_at: datetime = Field(..., description="Created at")
    updated_at: datetime = Field(..., description="Updated at")
    permissions: List["ShortPermissionSchema"] = Field(
        [], description="Permissions list")


@bp.route("/", methods=["GET"], strict_slashes=False)
@openapi.tag("Role")
@require_token
async def get_list(request: Request):
    """Get list of all roles

    openapi:
    ---
    summary: Get list of all roles
    description: This endpoint is used to get list of all roles.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Page size
      example: 10
    responses:
      200:
        description: Successfully retrieved roles
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array      
                      items:
                        ref: "#/components/schemas/RoleSchema"
                  required: ["total", "page", "page_size", "records"]
              required: ["error", "message", "data"]
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = RoleQuery(**request.args)
    records, total = await RoleRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("/<role_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Role")
@require_token
async def get_one(_: Request, role_id: str):
    """Get a role by ID

    openapi:
    ---
    summary: Get a role by ID
    description: This endpoint is used to get a role by ID.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: role_id
      in: path
      schema:
        type: string
      required: true
      description: ID of role
    responses:
      200:
        description: Successfully retrieved a role
        content:
          application/json:
            schema:
              ref: "#/components/schemas/DetailRoleSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Role not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    record = await RoleRepository.get_one(role_id, prefetch_permission=True)
    if record is None:
        return json_response(Response(error=ResponseCode.GET_ONE_FAILED, message="role not found"), 404)
    body = Response(error=0, message=SUCCESS_MESSAGE, data=record)
    return json_response(body, 200)
