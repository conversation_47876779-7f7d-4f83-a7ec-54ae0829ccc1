from datetime import datetime
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.permissions import PermissionRepository, PermissionQuery

bp = Blueprint("permission")


@openapi.component
class PermissionSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of permission",
                    example="123e4567-e89b-12d3-a456-************")
    name: str = Field(..., description="Name of permission",
                      example="admin:user:create", unique=True)
    description: str = Field("", description="Description of permission",
                             example="Admin can create user")
    created_at: datetime = Field(..., description="Created at")
    updated_at: datetime = Field(..., description="Updated at")


@bp.route("/", methods=["GET"], strict_slashes=False)
@openapi.tag("Permission")
@openapi.definition(
    summary="Get list of permissions",
    description="This endpoint is used to get list of permissions.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            name="page", location="query", type="integer", description="Page number", default=1),
        openapi.definitions.Parameter(
            name="page_size", location="query", type="integer", description="Page size", default=10)
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved list of permissions",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {
                                "type": "integer",
                                "description": "Error code",
                                "example": 0
                            },
                            "message": {
                                "type": "string",
                                "description": "Success message",
                                "example": "Successfully retrieved list of permissions"
                            },
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {
                                        "type": "integer",
                                        "description": "Total number of records",
                                        "example": 1
                                    },
                                    "page": {
                                        "type": "integer",
                                        "description": "Current page number",
                                        "example": 1
                                    },
                                    "page_size": {
                                        "type": "integer",
                                        "description": "Number of records per page",
                                        "example": 10
                                    },
                                    "records": {
                                        "type": "array",
                                        "description": "List of permissions",
                                        "items": {
                                            "$ref": '#/components/schemas/PermissionSchema'
                                        }
                                    }
                                },
                                "required": ["total", "page", "page_size", "records"]
                            }
                        },
                        "required": ["error", "message", "data"]
                    }
                }
            }
        ),
        openapi.definitions.Response(
            status=401,
            description="Unauthorized",
            content={
                "application/json": {
                    "schema": {
                        "$ref": "#/components/schemas/UnauthorizedResponse"
                    }
                }
            }
        )
    ]
)
@require_token
async def get_list(request: Request):
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = PermissionQuery(**request.args)
    records, total = await PermissionRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)
