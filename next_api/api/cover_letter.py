from typing import List
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from models.cover_letter import CoverLetter
from repositories.base import Pagination
from repositories.cover_letter import CoverLetterRepository, CoverLetterQuery
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode, \
    OPENAPI_NOT_FOUND_RESPONSE, OPENAPI_BAD_REQUEST_RESPONSE, OPENAPI_UNAUTHORIZED_RESPONSE

bp = Blueprint("cover_letter")


@openapi.component
class RequestCoverLetterSchema(BaseModel):
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    resume_id: str = Field(..., description="UUID of resume",
                           example="123e4567-e89b-12d3-a456-************")
    title: str = Field(
        None, description="Title of cover letter", example="title")
    content: str = Field(
        None, description="Content of cover letter", example="content")
    status: int = Field(CoverLetter.Status.ACTIVE,
                        description="Status of cover letter", example=1)
    target_position: List[str] = Field(
        None, description="Target position", example="Software Engineer")


@openapi.component
class ResponseCoverLetterSchema(BaseModel):
    id: str = Field(..., description="UUID of cover letter",
                    example="123e4567-e89b-12d3-a456-************")
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    resume_id: str = Field(..., description="UUID of resume",
                           example="123e4567-e89b-12d3-a456-************")
    title: str = Field(
        None, description="Title of cover letter", example="title")
    content: str = Field(
        None, description="Content of cover letter", example="content")
    status: int = Field(None, description="Status of cover letter", example=1)
    target_position: List[str] = Field(
        [], description="Target position", example="Software Engineer")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Cover Letter")
@openapi.definition(
    summary="Get a paginated list of cover letters",
    description="This endpoint is used to get a paginated list of cover letters.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            name="page", location="query", type="integer", description="Page number, default is 1"),
        openapi.definitions.Parameter(name="page_size", location="query", type="integer",
                                      description="Number of records per page, default is 10"),
        openapi.definitions.Parameter(
            name="id", location="query", type="string", description="UUID of cover letter"),
        openapi.definitions.Parameter(
            name="id__in", location="query", type="array", description="List of UUIDs of cover letters"),
        openapi.definitions.Parameter(name="id__not_in", location="query", type="array",
                                      description="List of UUIDs of cover letters not to include"),
        openapi.definitions.Parameter(
            name="user_id", location="query", type="string", description="UUID of user"),
        openapi.definitions.Parameter(
            name="user_id__in", location="query", type="array", description="List of UUIDs of users"),
        openapi.definitions.Parameter(name="user_id__not_in", location="query",
                                      type="array", description="List of UUIDs of users not to include"),
        openapi.definitions.Parameter(
            name="resume_id", location="query", type="string", description="UUID of resume"),
        openapi.definitions.Parameter(
            name="resume_id__in", location="query", type="array", description="List of UUIDs of resumes"),
        openapi.definitions.Parameter(name="resume_id__not_in", location="query",
                                      type="array", description="List of UUIDs of resumes not to include"),
        openapi.definitions.Parameter(
            name="title", location="query", type="string", description="Title of cover letter"),
        openapi.definitions.Parameter(
            name="title__contains", location="query", type="string", description="Title contains"),
        openapi.definitions.Parameter(name="title__icontains", location="query",
                                      type="string", description="Title contains, search case insensitive"),
        openapi.definitions.Parameter(
            name="title__startswith", location="query", type="string", description="Title starts with"),
        openapi.definitions.Parameter(
            name="title__endswith", location="query", type="string", description="Title ends with"),
        openapi.definitions.Parameter(
            name="content", location="query", type="string", description="Content of cover letter"),
        openapi.definitions.Parameter(
            name="content__contains", location="query", type="string", description="Content contains"),
        openapi.definitions.Parameter(name="content__icontains", location="query",
                                      type="string", description="Content contains, search case insensitive"),
        openapi.definitions.Parameter(
            "target_position", str, location="query", description="Target position", required=False),
        openapi.definitions.Parameter(
            "target_position__contains", str, location="query", description="Target position contains", required=False),
        openapi.definitions.Parameter(
            "target_position__icontains", str, location="query",
            description="Target position contains (case insensitive)", required=False),
        openapi.definitions.Parameter(
            "target_position__startswith", str,
            location="query", description="Target position starts with",
            required=False
        ),
        openapi.definitions.Parameter(
            "target_position__endswith", str,
            location="query", description="Target position ends with",
            required=False
        ),
        openapi.definitions.Parameter(
            name="status", location="query", type="integer", description="Status of cover letter"),
        openapi.definitions.Parameter(
            name="status__in", location="query", type="array", description="List of statuses of cover letters"),
        openapi.definitions.Parameter(name="status__not_in", location="query", type="array",
                                      description="List of statuses of cover letters not to include"),
        openapi.definitions.Parameter(
            name="created_at", location="query", type="string", description="Created at, format is ISO 8601"),
        openapi.definitions.Parameter(name="created_at__gte", location="query",
                                      type="string", description="Created at greater than or equal to"),
        openapi.definitions.Parameter(name="created_at__lte", location="query",
                                      type="string", description="Created at less than or equal to"),
        openapi.definitions.Parameter(
            name="updated_at", location="query", type="string", description="Updated at, format is ISO 8601"),
        openapi.definitions.Parameter(name="updated_at__gte", location="query",
                                      type="string", description="Updated at greater than or equal to"),
        openapi.definitions.Parameter(name="updated_at__lte", location="query",
                                      type="string", description="Updated at less than or equal to"),
        openapi.definitions.Parameter(
            name="order_by", location="query", type="string", description="Order by, default is created_at"),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a list of cover letters",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "description": "Response data",
                                "properties": {
                                    "total": {"type": "integer", "description": "Total number of records"},
                                    "page": {"type": "integer", "description": "Current page number"},
                                    "page_size": {"type": "integer", "description": "Number of records per page"},
                                    "records": {
                                        "type": "array",
                                        "description": "List of cover letters",
                                        "items": {
                                            "$ref": "#/components/schemas/ResponseCoverLetterSchema"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
async def get_list(request: Request):
    """Get a paginated list of cover letters."""
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = CoverLetterQuery(**request.args)
    records, total = await CoverLetterRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Cover Letter")
@openapi.definition(
    summary="Create a new cover letter",
    description="This endpoint is used to create a new cover letter.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
    ],
    body=openapi.definitions.RequestBody(
        description="Cover letter data",
        content={
            "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/RequestCoverLetterSchema"
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=201, description="Successfully created a cover letter",
            content={
                "application/json": {
                    "schema": {
                        "$ref": "#/components/schemas/ResponseCoverLetterSchema"
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
async def create(request: Request):
    """Create a new cover letter."""
    try:
        record = await CoverLetterRepository.create_one(request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot create cover letter")

    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<cover_letter_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("Cover Letter")
@openapi.definition(
    summary="Update an existing cover letter by ID",
    description="This endpoint is used to update an existing cover letter by ID.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            name="cover_letter_id", location="path", type="string", description="UUID of cover letter", required=True),
    ],
    body=openapi.definitions.RequestBody(
        description="Cover letter data",
        content={
            "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/RequestCoverLetterSchema"
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully updated a cover letter",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "description": "Response data",
                                "$ref": "#/components/schemas/ResponseCoverLetterSchema"
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
async def update(request: Request, cover_letter_id: str):
    """Update an existing cover letter by ID."""
    try:
        affected_rows, record = await CoverLetterRepository.update(cover_letter_id, request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot update cover letter")

    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=dict(record)), 200)
    return json_response(Response(error=ResponseCode.UPDATE_FAILED, message="Cannot update cover letter"), 404)


@bp.route("/<cover_letter_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Cover Letter")
@openapi.definition(
    summary="Get a single cover letter by ID",
    description="This endpoint is used to get a single cover letter by ID.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            name="cover_letter_id", location="path", type="string", description="UUID of cover letter", required=True),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a cover letter",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "description": "Response data",
                                "$ref": "#/components/schemas/ResponseCoverLetterSchema"
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
async def get_one(_: Request, cover_letter_id: str):
    """Get a single cover letter by ID."""
    record = await CoverLetterRepository.get_one(CoverLetterQuery(id=cover_letter_id))
    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=dict(record)), 200)
    return json_response(Response(error=ResponseCode.GET_ONE_FAILED, message="Cover letter not found"), 404)
