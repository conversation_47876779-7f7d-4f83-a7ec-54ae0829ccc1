import time
import os
import datetime
import json
from typing import List, Optional
from sanic import BadRequest, Blueprint, Request
from pydantic import BaseModel, Field
from sanic.log import logger, error_logger
from sanic_ext import openapi
from repositories.cover_letter import CoverLetterRepository, CoverLetterQuery
from api.base import json_response, Response, ResponseCode
from api.middlewares import require_token, check_permission
from models.feature import Feature
from resume_parser.parser_engine import Parser<PERSON><PERSON><PERSON>
from resume_parser.analyze_resume import analyze_resume
from repositories.base import Pagination
from repositories.question import QuestionRepository, QuestionQuery
from repositories.mock_interview import MockInterviewRepository, MockInterviewQuery
from repositories.user_feature import UserFeatureRepository
from uuid_extensions import uuid7
from groq import Groq
from langchain_community.utilities import GoogleSerperAPIWrapper
from utils import llm_prompts, llm_helpers, subscription
from repositories.job_insights import JobInsightsRepository, JobInsightsQuery
from repositories.application_kit import ApplicationKitRepository, ApplicationKitQuery
from repositories.follow_up_letter import FollowUpLetterRepository, FollowUpLetterQuery
from repositories.resume import ResumeQuery, ResumeRepository
from repositories.user import UserRepository
from google.cloud import storage
from google.oauth2 import service_account
import base64
from datetime import timedelta
from pathlib import Path
from openai import OpenAI

bp = Blueprint("ai")

@openapi.component
class AudioFileSchema(BaseModel):
    """Schema for audio file request"""
    audio_key: str = Field(..., description="The key/identifier for the audio file")
    audio_content: str = Field(..., description="The audio content to store")

@openapi.tag("AI")
@openapi.tag("Audio")
@bp.route("/audio/convert-from-text", methods=["POST"], strict_slashes=False)
@require_token
@check_permission([Feature.Code.MOCK_INTERVIEW_AI])
async def convert_from_text(request: Request):
    """
    Convert text to audio and return a signed URL for access.

    openapi:
    ---
    summary: Convert text to audio and get access URL
    description: This endpoint converts text to audio and returns a signed URL for access.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/AudioFileSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  type: object
                  properties:
                    url:
                      type: string
                      description: Signed URL for accessing the audio file
                    time_taken:
                      type: number
                      description: Processing time in seconds
      400:
        description: Bad Request
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 400
                message:
                  type: string
                  description: Error message
                data:
                  type: object
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      500:
        description: Server Error
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 500
                message:
                  type: string
                  description: Error message
                data:
                  type: object
    """
    time_start = time.time()
    voice_mapping ={
        # "aai": "Aaliyah-PlayAI",
        # "adi": "Adelaide-PlayAI",
        # "ani":"Angelo-PlayAI",
        # "ari":"Arista-PlayAI",
        "ati":"Atlas-PlayAI", # man-1
        # "bai":"Basil-PlayAI",
        # "bri": "Briggs-PlayAI",
        # "cai": "Calum-PlayAI",
        "cei":"Celeste-PlayAI", #woman-1
        "dei": "Deedee-PlayAI", #woman-2
        "thi": "Thunder-PlayAI" # man-2
    }
    # OpenAI voice mapping
    openai_voice_mapping = {
        "all": "alloy",
        "ash": "ash",
        "bal": "ballad",
        "cor": "coral",
        "ech": "echo",
        "fab": "fable",
        "ony": "onyx",
        "nov": "nova",
        "sag": "sage",
        "shi": "shimmer",
        "ver": "verse"
    }

    front_end_voice = {
        "man-1": "ati",
        "woman-1": "cei",
        "woman-2": "dei",
        "man-2": "thi"
    }

    init_time = time.time()
    logger.info(f"Initialization took {round(init_time - time_start, 2)} seconds")

    # Configuration variables
    BUCKET_NAME = "next_career_tts"
    MODEL = "playai-tts"
    OPENAI_MODEL = "gpt-4o-mini-tts"

    # Initialize clients
    client_init_start = time.time()
    tts_client = Groq(api_key=os.getenv("GROQ_API_KEY"))
    openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    # Load the base64-encoded key from env
    encoded_key = os.getenv("GCP_SERVICE_ACCOUNT_KEY")
    if not encoded_key:
        raise RuntimeError("Missing GCP_SERVICE_ACCOUNT_KEY")
    service_account_info = json.loads(base64.b64decode(encoded_key))
    credentials = service_account.Credentials.from_service_account_info(service_account_info)
    storage_client = storage.Client(credentials=credentials)
    logger.info(f"Client initialization took {round(time.time() - client_init_start, 2)} seconds")

    try:
        # Get request data
        request_processing_start = time.time()
        audio_key = request.json.get("audio_key")
        audio_content_raw = request.json.get("audio_content")
        audio_content = llm_helpers.replace_punctuations(audio_content_raw)
        logger.info(f"Original audio content: {audio_content_raw} \nProcessed audio content: {audio_content}")
        client_type = request.json.get("client", "playai")
        if client_type == "openai":
          VOICE = request.json.get("character_voice", "fab")
          VOICE = openai_voice_mapping.get(VOICE, "fable")
        else:
          VOICE = request.json.get("character_voice", "ati")
          VOICE = voice_mapping.get(VOICE, "Atlas-PlayAI")
        logger.info(f"Request processing took {round(time.time() - request_processing_start, 2)} seconds")

        if not audio_key or not audio_content:
            return json_response(Response(
                error=ResponseCode.CLIENT_BAD_REQUEST,
                message="Both audio_key and audio_content are required",
                data={}
            ), status_code=400)

        # Check if bucket exists, create if it doesn't
        bucket_check_start = time.time()
        bucket = storage_client.bucket(BUCKET_NAME)
        if not bucket.exists():
            logger.info(f"Bucket {BUCKET_NAME} does not exist. Creating...")
            bucket = storage_client.create_bucket(BUCKET_NAME)
        logger.info(f"Bucket check took {round(time.time() - bucket_check_start, 2)} seconds")

        # Create blob name using audio_key
        file_extension = "ogg" if client_type == "playai" else "mp3"
        blob_name = f"audio_cache/{audio_key}.{file_extension}"
        blob = bucket.blob(blob_name)

        # Check if already cached
        cache_check_start = time.time()
        try:
            if blob.exists():
                logger.info("Audio already cached → generating signed URL")
                signed_url = blob.generate_signed_url(
                    version="v4",
                    expiration=timedelta(hours=1),
                    method="GET"
                )

                time_end = time.time()
                logger.info(f"Cache retrieval took {round(time_end - cache_check_start, 2)} seconds")
                return json_response(Response(
                    error=ResponseCode.SUCCESS,
                    message="Successfully retrieved cached audio URL",
                    data={
                        "url": signed_url,
                        "time_taken": round(time_end - time_start, 2)
                    }
                ), status_code=200)
        except Exception as e:
            logger.error(f"Error checking if blob exists: {e}")

        # Not cached → generate audio
        audio_gen_start = time.time()
        logger.info(f"Generating audio using {client_type}...")
        local_path = Path(f"/tmp/{audio_key}.{file_extension}")

        if client_type == "openai":
            # Use OpenAI TTS
            with openai_client.audio.speech.with_streaming_response.create(
                model=OPENAI_MODEL,
                voice=VOICE,  # OpenAI voice
                input=audio_content,
                instructions="""Voice: Clear, authoritative, and composed, projecting confidence and professionalism.
Tone: Neutral and informative, keep formality like a real interviewer.
Punctuation: Structured with commas and pauses for clarity, ensuring information is digestible and well-paced.
Delivery: Steady and measured, with slight emphasis on key figures and keywords."""
            ) as response:
                response.stream_to_file(str(local_path))
        else:
            # Use PlayAI TTS (Groq)
            response = tts_client.audio.speech.create(
                model=MODEL,
                voice=VOICE,
                response_format="ogg",
                input=audio_content,
            )
            response.write_to_file(local_path)

        logger.info(f"Audio generation took {round(time.time() - audio_gen_start, 2)} seconds")

        # Upload to GCS
        upload_start = time.time()
        logger.info("Uploading to GCS...")
        content_type = "audio/ogg" if client_type == "playai" else "audio/mpeg"
        try:
            blob.upload_from_filename(str(local_path), content_type=content_type)
            logger.info(f"GCS upload took {round(time.time() - upload_start, 2)} seconds")
        except Exception as e:
            logger.error(f"Error uploading to GCS: {e}")
            return json_response(Response(
                error=ResponseCode.SERVER_ERROR,
                message="Failed to upload audio file",
                data={"error": str(e)}
            ), status_code=500)

        # Generate signed URL
        url_gen_start = time.time()
        try:
            signed_url = blob.generate_signed_url(
                version="v4",
                expiration=timedelta(hours=1),
                method="GET"
            )
            logger.info(f"URL generation took {round(time.time() - url_gen_start, 2)} seconds")
        except Exception as e:
            logger.error(f"Error generating signed URL: {e}")
            return json_response(Response(
                error=ResponseCode.SERVER_ERROR,
                message="Failed to generate signed URL",
                data={"error": str(e)}
            ), status_code=500)

        # Clean up temporary file
        cleanup_start = time.time()
        try:
            local_path.unlink()
        except Exception as e:
            logger.warning(f"Failed to delete temporary file: {e}")
        logger.info(f"Cleanup took {round(time.time() - cleanup_start, 2)} seconds")

        time_end = time.time()
        logger.info(f"Total processing time: {round(time_end - time_start, 2)} seconds")
        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Successfully stored audio and generated URL",
            data={
                "url": signed_url,
                "time_taken": round(time_end - time_start, 2)
            }
        ), status_code=200)

    except Exception as e:
        logger.error(f"Unexpected error in store_audio: {e}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="An unexpected error occurred",
            data={"error": str(e)}
        ), status_code=500)

@openapi.component
class FunctionCallSchema(BaseModel):
    name: str = Field(..., description="The name of the tool call")
    arguments: str = Field(..., description="The arguments of the tool call")


@openapi.component
class AIResponseSchema(BaseModel):
    content: str = Field(..., description="The content of the AI response")
    time_taken: float = Field(...,
                              description="The time taken to generate the response")
    role: str = Field(..., description="The role of the AI response")
    function_call: Optional[FunctionCallSchema] = Field(
        None, description="The function calls of the AI response")


@openapi.component
class RequestCoverLetterGenSchema(BaseModel):
    resume_id: str = Field(...,
                           description="The resume ID of the job seeker")
    job_description_id: str = Field(...,
                                    description="The job description ID of the job seeker")
    tone: str = Field(..., description="The tone of the cover letter")

    language: str = Field(..., description="The language of the cover letter")


@openapi.tag("AI")
@openapi.tag("Cover Letter")
@bp.route("/cover-letters/generate", methods=["POST"], strict_slashes=False)
@require_token
@check_permission([Feature.Code.COVER_LETTER_AI_BUILDER])
async def generate_cover_letter(request: Request):
    """
    Generate a cover letter for a job seeker

    openapi:
    ---
    summary: Generate a cover letter for a job seeker
    description: This endpoint is used to generate a cover letter for a job seeker.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestCoverLetterGenSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Role not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    time_start = time.time()
    title: str = request.json.get("title", "New cover letter")
    tone: str = request.json.get("tone", "Unknown")
    language: str = request.json.get("language", "English")
    resume_id: str = request.json.get("resume_id", "")
    job_description_id: str = request.json.get("job_description_id", "")
    force_regenerate: bool = request.json.get("force_regenerate", False)
    user_id = request.ctx.user['id']

    # Validate job description ID
    if not job_description_id:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Job description ID is required",
            data={}
        ), status_code=422)

    # Check if there's an existing application kit with a cover letter
    if not force_regenerate:
        # Check if there's an existing application kit for this job description and resume
        query_params = {
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "user_id": user_id
        }
        app_kit_query = ApplicationKitQuery(
            **query_params,
            order_by=["-created_at"]
        )
        pagination = Pagination(page=1, page_size=1)
        app_kits, total = await ApplicationKitRepository.get_list(app_kit_query, pagination)

        # If application kit exists and has cover_letter_id, return existing cover letter
        if app_kits and total > 0 and app_kits[0].get("cover_letter_id"):
            cover_letter_id = app_kits[0]["cover_letter_id"]
            cover_letter = await CoverLetterRepository.get_one(CoverLetterQuery(id=cover_letter_id))
            if cover_letter:
                logger.info(f"Returning existing cover letter ID {cover_letter_id} from application kit")
                return json_response(Response(
                    error=ResponseCode.SUCCESS,
                    message="Success",
                    data=dict(cover_letter)
                ), status_code=200)

    # Get resume data - consider adding error handling for missing resume
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)
    if resume_details == "Resume not found":
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Resume not found",
            data={"error": "Resume not found"}
        ), status_code=404)

    job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)
    if job_description == "Job description not found":
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Job description not found",
            data={"error": "Job description not found"}
        ), status_code=404)

    if tone.lower() == "informal":
        tone_prompt = "use the easy-to-understand language like a teen friend and friendly tone"
    elif tone.lower() == "casual":
        tone_prompt = "casual tone"
    elif tone.lower() == "neutral":
        tone_prompt = "write in the neutral tone, do not use words that show enthusiasm or feelings"
    else:
        tone_prompt = "write in the professional but friendly tone"
    system_prompt_cover_letter = llm_prompts.COVER_LETTER_PROMPT.format(
        tone=tone_prompt,
        length=f"Write maximum 100 words",
        language=f"Please write in {language} language."
    )
    messages = []
    messages.append({
        "role": "system", "content": system_prompt_cover_letter
    })
    messages.append({
        "role": "user",
        "content": f"Here is my information: \n{resume_details}" + f"\nHere is the job description: \n{job_description}"}
    )
    GROQ_MODEL = llm_helpers.get_available_groq_model()
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    completion = client_groq.chat.completions.create(
        model=GROQ_MODEL,
        messages=messages,
        seed=21,
        temperature=0.8,
        presence_penalty=0.3
    )

    time_end = time.time()
    logger.info("Time taken to generate cover letter: %s seconds", time_end - time_start)

    try:
        request.json["user_id"] = user_id
        request.json["content"] = completion.choices[0].message.content
        request.json["title"] = title
        request.json["target_position"] = [completion.choices[0].message.role]

        record = await CoverLetterRepository.create_one(request.json)

        # Update or create application kit with the new cover letter ID
        _ = await llm_helpers.update_or_create_application_kit(
            {
                "user_id": user_id,
                "jd_id": job_description_id,
                "resume_id": resume_id,
                "cover_letter_id": record.id
            }
        )

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Success",
            data=dict(record)
        ), status_code=200)
    except Exception as e:
        error_logger.error(e)
        raise BadRequest(message="Cannot create resume", context={
            "error": ResponseCode.CREATE_FAILED})

# To find question ID by content


async def check_question_id_with_user_id(question_id: str, user_id: str) -> str:
    '''This function is used to check if the question ID belongs to the user.
    If the user does not own this question, return False.
    If the user owns this question, return True.'''
    pagination = Pagination(page=1)  # Get first matching result
    # Get all mock interviews for the user
    mock_interview_query = MockInterviewQuery(
        user_id=user_id
    )
    mock_interviews, _ = await MockInterviewRepository.get_list(mock_interview_query, pagination)
    if not mock_interviews:
        return False

    # Get all questions for these mock interviews
    for mock_interview in mock_interviews:
        questions_for_interview, _ = await MockInterviewRepository.get_questions(mock_interview['id'], pagination)
        for question in questions_for_interview:
            if str(question.id) == question_id:
                return True
    return False


class MockInterviewQAGenerationSchema(BaseModel):
    number_of_questions: int = Field(..., description="The number of questions to generate")
    questions: list[str] = Field(..., description="List of questions", type="array", items={"type": "string"})
    answers: list[str] = Field(..., description="List of answers", type="array", items={"type": "string"})
    categories: list[str] = Field(..., description="List of categories", type="array", items={"type": "string"})
    hint: list[list[str]] = Field(..., description="List of hints", type="array", items={"type": "string"})


class MockInterviewQAGenerationSchemaOnlyQuestionAnswers(BaseModel):
    number_of_questions: int = Field(..., description="The number of questions to generate")
    questions: list[str] = Field(..., description="List of questions", type="array", items={"type": "string"})
    answers: list[str] = Field(..., description="List of answers", type="array", items={"type": "string"})
    categories: list[str] = Field(..., description="List of categories", type="array", items={"type": "string"})


def extract_json_from_response(response_text: str) -> dict:
    # Find JSON content between ```json and ``` tags
    json_start = response_text.find('```json\n') + 8 if '```json\n' in response_text else 0
    json_end = response_text.find('\n```', json_start) if '\n```' in response_text else len(response_text)
    json_str = response_text[json_start:json_end].strip()
    return json.loads(json_str)


@openapi.component
class RequestMockInterviewGenSchema(BaseModel):
    number_of_questions: int = Field(...,
                                     description="The number of questions to generate")
    resume_id: str = Field(...,
                           description="The ID of the resume of the job seeker")
    job_description_id: str = Field(...,
                                    description="The job description ID of the job seeker")
    job_position: str = Field(...,
                              description="The position of the job seeker")


@openapi.tag("AI")
@openapi.tag("Mock Interview")
@bp.route("/mock-interviews/generate", methods=["POST"], strict_slashes=False)
@require_token
async def generate_mock_interview(request: Request):
    """
    Generate mock interview questions and answers for a job seeker

    openapi:
    ---
    summary: Generate mock interview questions and answers for a job seeker
    description: This endpoint is used to generate mock interview questions and answers for a job seeker.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestMockInterviewGenSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    # Track execution time for performance monitoring
    time_start = time.time()

    # Get parameters from request
    no_questions: int = int(request.json.get("number_of_questions", "0"))
    resume_id: str = request.json.get("resume_id", "")
    job_description_id: str = request.json.get("job_description_id", "")
    job_position: str = request.json.get("job_position", "")
    force_regenerate: bool = request.json.get("force_regenerate", False)
    user_id = request.ctx.user['id']

    # Check if user has enough questions
    uf_result, _ = await UserFeatureRepository.use_feature(user_id, Feature.Code.MOCK_INTERVIEW_AI, no_questions)
    if uf_result in [1, 2]:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="User does not have enough questions",
            data={}
        ), status_code=422)

    # Validate job description ID
    if not job_description_id:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Job description ID is required",
            data={}
        ), status_code=422)

    # Check if there's an existing application kit with a mock interview
    if not force_regenerate:
        # Check if there's an existing application kit for this job description and resume
        query_params = {
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "user_id": user_id
        }
        app_kit_query = ApplicationKitQuery(
            **query_params,
            order_by=["-created_at"]
        )
        pagination = Pagination(page=1, page_size=1)
        app_kits, total = await ApplicationKitRepository.get_list(app_kit_query, pagination)

        # If application kit exists and has mock_interview_id, return existing mock interview
        if app_kits and total > 0 and app_kits[0].get("mock_interview_id"):
            mock_interview_id = app_kits[0]["mock_interview_id"]
            mock_interview_query = MockInterviewQuery(id=mock_interview_id)
            mock_interview = await MockInterviewRepository.get_one(mock_interview_query)
            if mock_interview:
                logger.info(f"Returning existing mock interview ID {mock_interview_id} from application kit")
                return json_response(Response(
                    error=ResponseCode.SUCCESS,
                    message="Success",
                    data=dict(mock_interview)
                ), status_code=200)

    # If we need to generate a new mock interview (no existing one or force_regenerate=True)
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))  # create the groq LLM

    # Calculate number of questions for each category
    # Consider adding validation to ensure no_questions > 0
    # The distribution (50% technical, 30% behavioral, 20% motivation) could be configurable
    no_technical_questions = int(no_questions*0.5)  # 50% technical questions
    no_behavioral_questions = int(no_questions*0.3)  # 30% behavioral questions
    # Remaining questions (20%) for motivation to account for rounding
    no_motivation_questions = no_questions - no_technical_questions - no_behavioral_questions

    # Get resume data - consider adding error handling for missing resume
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)
    job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)

    # Prepare messages for AI model
    # Consider adding retry logic for API failures
    request_to_generate = f"Here is my information:\n" + \
        f"{resume_details}\n" + \
        f"Here is the job description:\n{job_description}\n" + \
        f"Please create {no_technical_questions} technical questions and {no_behavioral_questions} behavioral/competency-based questions and {no_motivation_questions} questions about motivation or ethical/situational issues, also generate {no_questions} answers for those questions based on the job description and my resume. "

    questions, answers, hints, categories = [], [], [], []
    while len(questions) < no_questions or len(answers) < no_questions:
        time_start_gen_qa = time.time()
        try:
            completion = client_groq.chat.completions.create(
                model=llm_helpers.get_available_groq_model(),
                messages=[
                    {
                        "role": "system",
                        "content": llm_prompts.SYSTEM_PROMPT_GENERATE_QUESTIONS_ANSWERS.format(no_questions=no_questions)
                    }, {
                        "role": "user",
                        "content": request_to_generate
                    }
                ],
                temperature=0.8,
                stream=False
            )

            # Extract response content
            response_text = completion.choices[0].message.content
            # Parse JSON
            result_json = extract_json_from_response(response_text)
        except Exception as e:
            logger.error(f"Failed to process Question and Answer response: {e}")
            continue
        questions, answers, categories = result_json["questions"], result_json["answers"], result_json["categories"]
        logger.info(f"Time taken generate questions and answers: {time.time() - time_start_gen_qa} seconds")

    # Check if hints or categories are incomplete
    while len(hints) < no_questions or len(categories) < no_questions:
        time_start_gen_hint_category = time.time()
        # Prepare message for generating missing hints/categories
        hint_category_prompt = "Your task is to classify the questions from users into the correct categories and give hints on how to answer the question."
        hint_category_prompt += """\nThose questions belong to one of the following categories:
    - Behavioral/situational/competency-based questions: real-life examples of how candidate have handled certain situations.
      Competency-based interviews are designed with the intention to analyse the candidate's skills and abilities through examples from their past experiences. Example: "Could you tell me about a moment when you had to lead a team through a challenging project?", "Could you share an experience where you effectively managed a conflict or disagreement within a team?", ...
      Behavioural interview questions are used to analyse your perspective and thought process using a real-life problem or a complex situation that arises in a work environment. Behavioural interview questions are designed to assess the problem-solving skill, teamworking skill, ability to handle criticism, leadership skill, stress management skill, etc. Example: "How do you handle tight deadlines or multiple tasks?", ...
      Situational-Judgement interview questions are asked to candidates for analysing their capability to make an appropriate judgement while handling a hypothetical situation. Example: "Your colleague has been recently underperforming in their position, and you have been asked to give feedback on their performance by the team supervisor. What would you do?"
      For this category, you should give a hint of 3 questions on how to answer the question based on the STAR framework (Situation, Task, Action, Result). These hints are the questions asked to the user have the outline of the answer.
    - Technical questions: questions used to assess the specific job-related skills and technical knowledge.
      Technical interview questions are asked to assess your level of knowledge and technical expertise required for the job role and it is rather a customary section for employers recruiting for engineering, science, or software roles. Example: "What is the difference between a stack and a queue?", "How do you optimize database performance?", "What are your technical certifications and how do they contribute to this job role?" ...
      For this category, you should give a hint of 3 questions on how to outline the answer using the GICF method ( Goal of the project, Impact or outcome, Challenges faced, Findings).
    - Motivation/ethical issues: questions to imagine themself in a hypothetical situation and describe how they would respond.
      Motivation interview questions are used to assess your motivation and commitment to the job role. Example: "Why do you want to work for this company?", "How do you handle stress or pressure?", "What motivates you to work hard?", ...
      Ethical interview questions are used to assess your ethical behavior and decision-making skills. Example: "What would you do if you saw a colleague cheating on a test?", "How do you handle conflicts with colleagues or supervisors?", "What is your approach to handling ethical dilemmas in the workplace?", ...
      For this category, you should give a hint of 3 questions on how to outline the answer which can show the greatest strengths and areas of improvement of user, and also show how they contribute to the job role.
Please classify the categories correctly and hints. Only use above knowledge to generate hints, do not generate random hints.
    """
        number_of_hints = no_questions - len(hints)

        hint_category_prompt2 = "\nPlease classify the following questions into the correct categories and give "
        if number_of_hints > 0:
            hint_category_prompt2 += f"{number_of_hints} hints on how to answer the following {number_of_hints} questions:\n"
        for i, q in enumerate(questions):
            hint_category_prompt2 += f"- Question {i}: {q}\n"

        hint_category_prompt2 += """For each question, give only 1 sentence which contains three or more hint questions. These questions bring the user ideas to answer the question.
        For example, the question is "What is your greatest strength?", the hint could be "What are your strengths? What is your best skill? Tell about the time when it greatly helped you to achieve something."
        For example,
        Your response must be in the following JSON format:\n"""
        hint_category_prompt2 += """\n{
            "hint": ["hints for question 1", "hints for question 2", ...], # list of hints for answering all questions
            "categories": ["category for question 1", "category for question 2", ...] # list of categories for all questions
        }"""

        try:
            # Generate completion using Gemini
            completion = client_groq.chat.completions.create(
                model=llm_helpers.get_available_groq_model(),
                messages=[
                    {
                        "role": "system",
                        "content": hint_category_prompt
                    }, {
                        "role": "user",
                        "content": hint_category_prompt2
                    }
                ],
                temperature=0.8,
                stream=False
            )

            # Extract response content
            response_text = completion.choices[0].message.content
            # Parse JSON
            result_hints = extract_json_from_response(response_text)

            # Append new hints if needed
            if len(hints) < no_questions and "hint" in result_hints:
                new_hints = result_hints["hint"]
                hints.extend(new_hints[:number_of_hints])

            # Append new categories if needed
            if len(categories) < no_questions and "categories" in result_hints:
                new_categories = result_hints["categories"]
                categories.extend(new_categories[len(categories):(no_questions+1)])

            logger.info(
                f"Updated hints count: {len(hints)} ({len(new_hints)} added), categories count: {len(categories)}")

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to process Hint response: {e}")
            continue

        logger.info(f"Time taken generate hints and categories: {time.time() - time_start_gen_hint_category} seconds")

    result_json["hint"] = hints
    result_json["categories"] = categories

    # Add data to Database
    mock_interview_data = {
        "user_id": user_id,
        "position": job_position,
        "job_description": job_description,
        "job_description_id": job_description_id
    }
    mock_interview_repo = await MockInterviewRepository.create_one(mock_interview_data)
    mock_interview_id = mock_interview_repo.id  # ID

    question_ids = []

    for category, question, answer, hint in zip(categories, questions, answers, hints):
        question_id = uuid7()
        question_data = {
            "id": question_id,
            "question": question,
            "answer": answer,
            "hint": hint,
            "category": category
        }
        await QuestionRepository.create_one(question_data)
        question_ids.append(question_id)

    mock_interview, question_count = await MockInterviewRepository.add_questions(mock_interview_id, question_ids)
    logger.info(f"There are {question_count} questions been added to the mock interview ID {mock_interview_id}")

    # Update or create application kit with the new mock interview ID
    _ = await llm_helpers.update_or_create_application_kit(
        {
            "user_id": user_id,
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "mock_interview_id": mock_interview_id
        }
    )

    logger.info(f"Time taken /generate_mock_interview: {time.time() - time_start} seconds")

    return json_response(Response(
        error=ResponseCode.SUCCESS,
        message="Success",
        data=dict(mock_interview)
    ), status_code=200)


@openapi.component
class AnalyzeMockInterviewSchema(BaseModel):
    question_id: str = Field(..., description="The question ID to analyze")
    question_id: str = Field(..., description="The question ID to analyze")
    answer: str = Field(..., description="The answer to analyze")


class AnalyzeSchema(BaseModel):
    category: str = Field(..., description="The category of the question")
    analysis: dict = Field(
        ...,
        description="Analysis of STAR components",
        properties={
            'situation': {'type': 'string', 'description': 'Analysis of situation component (for behavioral/competency-based questions)'},
            'task': {'type': 'string', 'description': 'Analysis of task component (for behavioral/competency-based questions)'},
            'action': {'type': 'string', 'description': 'Analysis of action component (for behavioral/competency-based questions)'},
            'result': {'type': 'string', 'description': 'Analysis of result component (for behavioral/competency-based questions)'},
            'skills': {'type': 'string', 'description': 'Analysis of skill section (for background/motivation questions)'},
            'academic': {'type': 'string', 'description': 'Analysis of academic section (for background/motivation questions)'},
            'management': {'type': 'string', 'description': 'Analysis of management section (for background/motivation questions)'},
            'personal': {'type': 'string', 'description': 'Analysis of personal section (for background/motivation questions)'},
            'seek_info': {'type': 'string', 'description': 'Analysis of information gathering approach (for ethical/professional questions)'},
            'patient_safety': {'type': 'string', 'description': 'Analysis of patient safety considerations (for ethical/professional questions)'},
            'initiative': {'type': 'string', 'description': 'Analysis of personal initiative shown (for ethical/professional questions)'},
            'escalate': {'type': 'string', 'description': 'Analysis of escalation approach (for ethical/professional questions)'},
            'support': {'type': 'string', 'description': 'Analysis of support considerations (for ethical/professional questions)'},
            'strategy': {'type': 'string', 'description': 'Analysis of business alignment and goals (for technical questions)'},
            'technology': {'type': 'string', 'description': 'Analysis of technical tools and processes used (for technical questions)'},
            'analytics': {'type': 'string', 'description': 'Analysis of data-driven insights (for technical questions)'},
            'results': {'type': 'string', 'description': 'Analysis of demonstrated impact (for technical questions)'},
            'transformation': {'type': 'string', 'description': 'Analysis of adaptability and innovation (for technical questions)'}
        },
        required=['situation', 'task', 'action', 'result']
    )
    strengths: list[str] = Field(
        ..., description="List of response strengths, also give compliments on their attitude (positive, courteous mindset?)")
    improvements: list[str] = Field(..., description="List of improvement areas")
    rating: int = Field(description="Overall rating 1-10")


class AnalyzeSchemaList(BaseModel):
    analyze_list: list[AnalyzeSchema]


@openapi.component
class RequestResumeAdjustSchema(BaseModel):
    edit_zone: str = Field(..., description="The zone of the resume that need to be edited or added. If it is empty, the AI will generate suggestions for the whole resume.")
    item_id: str = Field(..., description="The id of the item.")
    job_description_id: str = Field(...,
                                    description="The job description ID of the job seeker")
    resume_id: str = Field(...,
                           description="The resume ID of the job seeker")

# @openapi.tag("AI")
# @openapi.tag("Mock Interview")
# @bp.route("/mock-interviews/analyze", methods=["POST"], strict_slashes=False)
# @require_token
# # @check_permission([Feature.Code.MOCK_INTERVIEW_AI]) # Open for free users on Thinh request 2025-03-25
# async def analyze_mock_interview(request: Request):
#     """
#     Analyze mock interview questions and answers for a job seeker, so he/she can prepare the best for the interview.

#     openapi:
#     ---
#     summary: Analyze mock interview questions and answers for a job seeker
#     description: This endpoint is used to analyze mock interview questions and answers for a job seeker.
#     requestBody:
#       content:
#         multipart/form-data:
#           schema:
#             type: object
#             properties:
#               question_id:
#                 type: string
#                 description: The question ID to analyze
#               answer:
#                 type: string
#                 description: The answer to analyze (optional if audio_file is provided)
#               job_description_id:
#                 type: string
#                 description: The job description ID
#               audio_file:
#                 type: string
#                 format: binary
#                 description: Audio file containing the answer (optional if answer is provided)
#             required:
#               - question_id
#               - job_description_id
#     responses:
#       200:
#         description: Success
#         content:
#           application/json:
#             schema:
#               type: object
#               properties:
#                 error:
#                   type: integer
#                   description: Error code
#                   example: 0
#                 message:
#                   type: string
#                   description: Success message
#                   example: success
#                 data:
#                   ref: "#/components/schemas/AIResponseSchema"
#       401:
#         description: Unauthorized
#         content:
#           application/json:
#             schema:
#               ref: "#/components/schemas/UnauthorizedResponse"
#     """
#     time_start = time.time()
#     client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))

#     # Get question_id and job_description_id from form or JSON
#     question_id = request.form.get("question_id") if request.form else request.json.get("question_id", "")
#     job_description_id = request.form.get("job_description_id") if request.form else request.json.get("job_description_id", "")
#     answer = request.form.get("answer") if request.form else request.json.get("answer", "")

#     # Check if we have an audio file and no answer text
#     if not answer and request.files and 'audio_file' in request.files:
#         audio_file = request.files.get('audio_file')
#         success, result = await process_speech_to_text(audio_file)

#         if success:
#             answer = result['transcription']
#             logger.info(f"Converted audio to text: {answer}")
#         else:
#             return json_response(Response(
#                 error=ResponseCode.SERVER_ERROR,
#                 message="Failed to process audio file",
#                 data={"error": result}
#             ), status_code=500)

#     # If we still don't have an answer, return error
#     if not answer:
#         return json_response(Response(
#             error=ResponseCode.CLIENT_BAD_REQUEST,
#             message="Either answer or audio_file must be provided",
#             data={"error": "No answer or audio file provided"}
#         ), status_code=400)

#     # find question id
#     query = QuestionQuery(
#         id=question_id
#     )
#     question_repo = await QuestionRepository.get_one(query)
#     # get question
#     question: str = question_repo.question

#     # get job description
#     job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)

#     # Create chat completion
#     # First, classify the question
#     GROQ_MODEL = llm_helpers.get_available_groq_model()
#     classification_response = client_groq.chat.completions.create(
#         model=GROQ_MODEL,
#         messages=[
#             {
#                 "role": "system",
#                 "content": llm_prompts.CLASSIFICATION_PROMPT
#             }, {
#                 "role": "user",
#                 "content": f"Question: {question}\nJob Description: {job_description}"
#             }
#         ],
#         response_format={"type": "json_object"}
#     )

#     question_category = BuddyQuestionClassificationSchema.model_validate_json(
#         classification_response.choices[0].message.content)
#     question_category = json.loads(question_category.model_dump_json())["category"]
#     logger.info("Question category detected: %s", question_category)
#     if question_category == "invalid":
#         return json_response(Response(
#             error=ResponseCode.CLIENT_BAD_REQUEST,
#             message="Invalid question",
#             data=None
#         ), status_code=400)
#     # Get the suggestion template for the detected category
#     # suggestion_template = llm_prompts.SUGGESTION_METHODS.get(question_category, llm_prompts.SUGGESTION_METHODS["Normal"])

#     # Modified system prompt to handle non-applicable fields
#     system_prompt = llm_prompts.SYSTEM_PROMPT_ANALYZE_INTERVIEW_ANSWER

#     messages = [
#         {"role": "system", "content": system_prompt},
#         {"role": "user", "content": f"I'm answering a question of category {question_category}.\nHere is the question: {question}\nHelp me analyze my answer for improvement: {answer}"}
#     ]
#     completion = client_groq.chat.completions.create(
#         model=llm_helpers.get_available_groq_model("llama-3.3-70b-versatile"),
#         messages=messages,
#         seed=2,
#         temperature=0.7,
#         response_format={"type": "json_object"}
#     )
#     time_end = time.time()
#     logger.info("Time taken to analyze interview answer: %s seconds", time_end - time_start)
#     completion_content = json.loads(completion.choices[0].message.content)
#     logger.info("Original completion content: %s", completion_content)
#     if completion_content.get('category', 'invalid') == 'invalid':
#         return json_response(Response(
#             error=ResponseCode.CREATE_FAILED,
#             message="Please provide a valid answer",
#             data={
#                 "error": "Please provide a valid answer"
#             }
#         ), status_code=422)

#     # Process the analysis fields to ensure non-applicable fields are null
#     analysis_dict = completion_content.get('analysis', {})
#     processed_analysis = {}

#     # List of all possible analysis fields
#     all_fields = [
#         'situation', 'task', 'action', 'result', 'skills', 'academic', 'management',
#         'personal', 'seek_info', 'patient_safety', 'initiative', 'escalate', 'support',
#         'strategy', 'technology', 'analytics', 'results', 'transformation'
#     ]

#     # Process each field to ensure non-applicable fields are null
#     for field in all_fields:
#         value = analysis_dict.get(field, '')
#         # Check if the field contains a non-applicable message
#         if value and any(phrase in value.lower() for phrase in ['not applicable', 'n/a', 'not relevant']):
#             processed_analysis[field] = ''
#         else:
#             processed_analysis[field] = value

#     response = {
#         "category": completion_content.get('category', ''),
#         "analysis": processed_analysis,
#         "strengths": completion_content.get('strengths', []),
#         "improvements": completion_content.get('improvements', []),
#         "rating": completion_content.get('rating', 0)
#     }
#     response = {
#         "role": completion.choices[0].message.role,
#         "question_id": question_id,
#         "content": response,
#         "time_taken": time_end - time_start
#     }
#     logger.info(f"Response: {response}")
#     # Extract fields from AI response
#     rating = response['content']['rating']
#     overall_strengths = response['content']['strengths']
#     overall_improvements = response['content']['improvements']
#     if rating == 0:

#         # ignore updating database and return question

#         question_repo = {}
#         question_repo['point'] = 0
#         question_repo['overall_strengths'] = overall_strengths
#         question_repo['overall_improvements'] = overall_improvements
#         return json_response(Response(
#             error=ResponseCode.SUCCESS,
#             message="Success",
#             data=question_repo
#         ), status_code=200)
#     # Extract fields from response content with default values for missing keys
#     content = response['content']
#     category = content.get('category', '')
#     analysis = content.get('analysis', {})

#     # Extract analysis fields with empty string defaults
#     analysis_situation = analysis.get('situation', '')
#     analysis_task = analysis.get('task', '')
#     analysis_action = analysis.get('action', '')
#     analysis_result = analysis.get('result', '')
#     analysis_skills = analysis.get('skills', '')
#     analysis_academic = analysis.get('academic', '')
#     analysis_management = analysis.get('management', '')
#     analysis_personal = analysis.get('personal', '')
#     analysis_seek_info = analysis.get('seek_info', '')
#     analysis_patient_safety = analysis.get('patient_safety', '')
#     analysis_initiative = analysis.get('initiative', '')
#     analysis_escalate = analysis.get('escalate', '')
#     analysis_support = analysis.get('support', '')
#     analysis_strategy = analysis.get('strategy', '')
#     analysis_technology = analysis.get('technology', '')
#     analysis_analytics = analysis.get('analytics', '')
#     analysis_results = analysis.get('results', '')
#     analysis_transformation = analysis.get('transformation', '')

#     # Prepare base data dictionary with required fields
#     data = {
#         "user_answer": answer,
#         "category": category,
#         "overall_strengths": [{"description": item} for item in overall_strengths],
#         "overall_improvements": [{"description": item} for item in overall_improvements]
#     }

#     # Add non-empty analysis fields to data dictionary
#     analysis_fields = {
#         "analysis_situation": analysis_situation,
#         "analysis_task": analysis_task,
#         "analysis_action": analysis_action,
#         "analysis_result": analysis_result,
#         "analysis_skills": analysis_skills,
#         "analysis_academic": analysis_academic,
#         "analysis_management": analysis_management,
#         "analysis_personal": analysis_personal,
#         "analysis_seek_info": analysis_seek_info,
#         "analysis_patient_safety": analysis_patient_safety,
#         "analysis_initiative": analysis_initiative,
#         "analysis_escalate": analysis_escalate,
#         "analysis_support": analysis_support,
#         "analysis_strategy": analysis_strategy,
#         "analysis_technology": analysis_technology,
#         "analysis_analytics": analysis_analytics,
#         "analysis_results": analysis_results,
#         "analysis_transformation": analysis_transformation
#     }

#     # Only add non-empty analysis fields to data
#     data.update({k: v for k, v in analysis_fields.items() if v})

#     # Add rating if present
#     if rating:
#         data["point"] = int(rating)
#     # after collecting all data, update the question
#     await QuestionRepository.update(pk=question_id, data=data)
#     updated_question = await QuestionRepository.get_one(query)

#     return json_response(Response(
#         error=ResponseCode.SUCCESS,
#         message="Success",
#         data=dict(updated_question),
#     ), status_code=200)


class AdjustResumeSchema(BaseModel):
    job_title: str = Field(...,
                           description="Assessment of whether the resume's role title is appropriate for the target job")
    education: list[str] = Field(
        ..., description="Analysis of education level and suggestions for adding/removing educational information or certifications")
    skills: list[str] = Field(...,
                              description="Assessment of matching skills, advantages, and suggestions for additional relevant skills")
    experience: list[str] = Field(
        ..., description="Analysis of experience level and suggestions for adding relevant or removing irrelevant experiences")
    experience_level: list[str] = Field(
        ..., description="Assessment of years of experience compared to job requirements and recommendations for appropriate level")
    other_info: list[str] = Field(..., description="Additional suggestions for resume improvements")


class ResumesAdjustmentReccommendedFields(BaseModel):
    missing_required_sections: list[str] = Field(default=[], description="List of missing required sections")
    reason_of_adding_required_sections: list[str] = Field(
        default=[], description="List of reasons why these sections are important")
    missing_good_to_have_sections: list[str] = Field(default=[], description="List of missing good-to-have sections")
    reason_of_adding_good_to_have_sections: list[str] = Field(
        default=[], description="List of reasons why these sections are important")


class ResumesAdjustmentReccommendedSummariesList(BaseModel):
    summary: list[str] = Field(default=[], description="List of suggested summaries")


class ResumesAdjustmentReccommendedItemsList(BaseModel):
    edit_zone: str = Field(default="", description="The zone of the resume that need to be edited or added.")
    suggested_items: list[dict] = Field(default=[], description="List of suggested items")


@openapi.tag("AI")
@openapi.tag("Resume")
@bp.route("/resumes/adjust", methods=["POST"], strict_slashes=False)
@require_token
@check_permission([Feature.Code.RESUME_AI_BUILDER])
async def adjust_resume(request: Request):
    """
    Adjust the resume of a job seeker based on the job description

    openapi:
    ---
    summary: Adjust the resume of a job seeker based on the job description
    description: This endpoint is used to adjust the resume of a job seeker based on the job description.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestResumeAdjustSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    time_start = time.time()
    job_description_id: str = request.json.get("job_description_id", "")  # get job description id from request
    if job_description_id == "":
        # if job description id is not provided, get job description from request
        job_description: str = request.json.get("job_description", "")
        if job_description == "":
            # both job description id and job description are not provided
            # return error
            return json_response(Response(
                error=ResponseCode.CLIENT_BAD_REQUEST,
                message="Error",
                data={"error": "Job description is required, but either job_description_id or job_description is not provided"}
            ), status_code=422)
    else:
        # if job description id is provided, get job description from database
        job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)
    # Get resume data - consider adding error handling for missing resume
    resume_id: str = request.json.get("resume_id", "")
    edit_zone: str = request.json.get("edit_zone", "")
    item_id: str = request.json.get("item_id", "")
    user_id = request.ctx.user['id']
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)
    resume_details = json.loads(resume_details)
    logic_flag = 0  # for later handling response
    # LOGIC 1: if edit_zone and item_id are both empty, then generate suggestions on adding the missing sections and good-to-have sections
    # These sections helps them complete and finalize their best tailored resume
    if not edit_zone and not item_id:
        logic_flag = 1
        time.sleep(2)
        # get keys from resume
        resume_keys = resume_details['sections'].keys()
        # Find missing keys using set operations for better performance
        missing_required_keys = list(
            set(['summary', 'experience', 'education', 'projects', 'references']) - set(resume_keys))
        missing_good_to_have_keys = list(
            set(['skills', 'certifications', 'interests', 'languages', 'awards', 'volunteer', 'publications']) - set(resume_keys))
        sections_and_reasons = llm_helpers.get_sections_and_reasons(missing_required_keys, missing_good_to_have_keys)
        included_required_keys = list(
            set(['summary', 'experience', 'education', 'projects', 'references']) - set(missing_required_keys))
        included_good_to_have_keys = list(set(
            ['skills', 'certifications', 'interests', 'languages', 'awards', 'volunteer', 'publications']) - set(missing_good_to_have_keys))
        included_sections = llm_helpers.get_included_recommendations(included_required_keys, included_good_to_have_keys)
        # Prepare response
        time_end = time.time()
        response_data = {
            "content": {
                "missing_required_sections": sections_and_reasons['missing_required_sections'] + included_sections['missing_required_sections'],
                "reason_of_adding_required_sections": sections_and_reasons['reason_of_adding_required_sections'] + included_sections['reason_of_adding_required_sections'],
                "missing_good_to_have_sections": sections_and_reasons['missing_good_to_have_sections'] + included_sections['missing_good_to_have_sections'],
                "reason_of_adding_good_to_have_sections": sections_and_reasons['reason_of_adding_good_to_have_sections'] + included_sections['reason_of_adding_good_to_have_sections']
            },
            "time_taken": time_end - time_start,
            # "adjustment_id": str(record.id)  # Include the created adjustment ID
        }
        # Update or create application kit with the new cover letter ID
        _ = await llm_helpers.update_or_create_application_kit(
            {
                "user_id": user_id,
                "jd_id": job_description_id,
                "resume_id": resume_id
            }
        )

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Success",
            data=response_data
        ), status_code=200)
    # LOGIC 2: if edit_zone and item_id, then generate suggestions for the editting item only for the given item_id
    if edit_zone and item_id:
        logic_flag = 2
        # GROQ_MODEL = llm_helpers.get_available_groq_model()
        is_custom_section = False
        if edit_zone not in resume_details['sections'].keys():
            is_custom_section = True
        search_google_results = llm_helpers.search_information_based_on_jd(
            job_description=job_description, edit_zone=edit_zone, is_custom_section=is_custom_section)
        content_to_be_edited = llm_helpers.find_item(json_data=resume_details, item_id=item_id, edit_zone=edit_zone)
        user_and_system_messages = [{
            "role": "system",
            "content": '''Your task is to help user change their resume based on a job description.
- You receive a part of the resume, and some up-to-date knowledge for the industry.
- Your task is to analyse the content of the given knowledge, then give suggestions on adding more items to the resume which can make it a more suitable resume for the job. Those items must be relevant with the user's knowledge and experience written in the resume given to you.
- Focus on generating so many suggestions as possible for the sections given to you.
- Use different tone, and languages such as informal, formal, professional, and use different points of view about the resume section while generating suggestions.
- Generate at least 5 suggestions for each section, especially at least 20 suggestion summary items. Do not give duplicate suggestions or repeat the existing items.
- Output format: only JSON format.
'''
        },
            {
                "role": "user",
                "content": f"Use the following professional knowledge from reviews of similar professionals working in the same industry as the user: {search_google_results[edit_zone]}"
        },
            {
                "role": "user",
                "content": f"Based on the job description information {job_description}, please suggest additional items that are appropriate for the job application. Here are the parts of the resume that need suggestions for editing or adding:\n{content_to_be_edited}."
        },
            {
                "role": "user",
                "content": """Generate at least 20 suggestion summary items using the given knowledge. Only suggest for the summary field. You will receive a JSON format input, and you should return a JSON format output with the same set of keys as the input.
Ensure your response always in the JSON format below:
{
    "summary": [list of content],  # Suggested items to be added to the given resume section
}"""
        }
        ]
    # LOGIC 3: if item_id is empty, generate suggestions for adding new items to the resume for the given edit_zone
    if edit_zone and not item_id:
        logic_flag = 3
        # GROQ_MODEL = 'llama-3.3-70b-versatile'
        is_custom_zone = False
        # get content to be edited
        try:
            content_to_be_edited = resume_details['sections'][edit_zone]
        except KeyError:
            logger.info("User is requesting to modify a custom section")
            is_custom_zone = True
            content_to_be_edited = ""
        # get information from google search
        search_google_results = llm_helpers.search_information_based_on_jd(
            job_description=job_description, edit_zone=edit_zone, is_custom_section=is_custom_zone)
        # return json_response(Response(
        #     error=ResponseCode.CLIENT_BAD_REQUEST,
        #     message="Error",
        #     data={"error": "This edit_zone is not supported by our service."}
        # ), status_code=409)
        user_and_system_messages = [
            {
                "role": "system",
                "content": '''Your task is to help user change their resume based on a job description.
- You receive a part of the resume, and some up-to-date knowledge for the industry.
- Your task is to analyse the content of the given knowledge, then give suggestions on adding more items to the resume which can make it a more suitable resume for the job. Those items must be relevant with the user's knowledge and experience written in the resume given to you.
- Use different tone, and languages such as informal, formal, professional, and use different points of view about the resume section while generating suggestions.
- Generate at least 5 suggestions for each section. Do not give duplicate suggestions or repeat the existing items.
- Output format: Ensure your response always in the JSON format. Below is an example of the format of the response when the edit_zone is experience:
{
    "edit_zone": "string",
    "suggested_items": [
        {
            "id": "UUID7",
            "visible": boolean,
            "company": "string",
            "position": "string",
            "location": "string",
            "date": "datetime string",
            "summary": "string",
            ... (some other fields)
        }, ...
    ]
}
'''
            }, {
                "role": "user",
                "content": f"Use the following professional knowledge from reviews of similar professionals working in the same industry as the user: {search_google_results[edit_zone]}"
            }
        ]
        if content_to_be_edited:  # not null
            user_and_system_messages.append({
                "role": "user",
                "content": f"Based on the job description information {job_description}, please suggest additional items that are appropriate for the job application. Here are the parts of the resume that need suggestions for editing or adding:\n{content_to_be_edited}."
            })
        else:
            user_and_system_messages.append({
                "role": "user",
                "content": f"Based on the job description information {job_description}, please suggest additional items that are appropriate for the job application."
            })
        user_and_system_messages.append({
            "role": "user",
            "content": """Generate at least 10 new suggestion items for the section using the given knowledge. Ensure the response maintains the original format of the resume, only updating the content for the items.
Focus on suggestings the string items, not the numerical items. Use your reasoning skill to generate the dictionaries of the suggested items.
Ensure your response always in the JSON format below:
{
    "edit_zone": "string",
    "suggested_items": [Suggested item dictionaries, ...]
}"""
        })
    if not edit_zone and item_id:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Error",
            data={"error": "edit_zone can not be empty."}
        ), status_code=409)
    client_groq = Groq(api_key=os.environ.get("GROQ_API_KEY"))
    GROQ_MODEL = llm_helpers.get_available_groq_model()
    groq_response_success = False

    try:
        max_retries = 5
        retry_count = 0
        while not groq_response_success and retry_count < max_retries:
            try:
                completion = client_groq.chat.completions.create(
                    model=GROQ_MODEL,
                    temperature=1,
                    seed=23,
                    top_p=1,
                    response_format={"type": "json_object"},
                    messages=user_and_system_messages
                )
                # Parse the AI response content
                if logic_flag == 1:
                    adjustment_content = ResumesAdjustmentReccommendedFields.model_validate_json(
                        completion.choices[0].message.content)
                    adjustment_content = json.loads(adjustment_content.model_dump_json())
                    groq_response_success = True
                if logic_flag == 2:
                    adjustment_content = ResumesAdjustmentReccommendedSummariesList.model_validate_json(
                        completion.choices[0].message.content)
                    adjustment_content = json.loads(adjustment_content.model_dump_json())
                    adjustment_content['item_id'] = item_id
                    groq_response_success = True
                if logic_flag == 3:
                    adjustment_content = ResumesAdjustmentReccommendedItemsList.model_validate_json(
                        completion.choices[0].message.content)
                    adjustment_content = json.loads(adjustment_content.model_dump_json())
                    suggested_items = adjustment_content['suggested_items']
                    for item in suggested_items:
                        item['id'] = uuid7()
                    adjustment_content['suggested_items'] = suggested_items
                    groq_response_success = True
                    GROQ_MODEL = 'llama-3.1-8b-instant'

            except Exception as e:
                logger.warning(f"Attempt {retry_count + 1} failed: {str(e)}")
                retry_count += 1
                if retry_count == max_retries:
                    logger.error(f"Failed after {max_retries} attempts: {str(e)}")
                    raise BadRequest(message="Invalid AI response format after multiple retries")
                time.sleep(1)  # Wait 1 second before retrying
                continue

        # Update or create application kit with the new cover letter ID
        _ = await llm_helpers.update_or_create_application_kit(
            {
                "user_id": user_id,
                "jd_id": job_description_id,
                "resume_id": resume_id
            }
        )
        # Prepare response
        time_end = time.time()
        response_data = {
            "content": adjustment_content,
            "time_taken": time_end - time_start,
            # "adjustment_id": str(record.id)  # Include the created adjustment ID
        }

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Success",
            data=response_data
        ), status_code=200)

    except Exception as e:
        error_logger.exception("Failed to create resume adjustment: %s", e)
        raise BadRequest(
            message="Cannot create resume adjustment",
            context={"error": ResponseCode.CREATE_FAILED}
        )


@openapi.component
class RequestInterviewAskBuddySchema(BaseModel):
    job_description_id: str = Field(...,
                                    description="The job description id of the job seeker")
    resume_id: str = Field(...,
                           description="The resume id of the job seeker")

    transcription_text: Optional[str] = Field(None,
                                    description="The transcription text of the interview")
    # audio_file parameter will be handled in form-data
    # session_id: str = Field(..., description="The session id of the interview")


class BuddyQuestionClassificationSchema(BaseModel):
    category: str = Field(..., description="The category of the question")


class BuddySuggestionSchema(BaseModel):
    category: str = Field(..., description="The category of the question")
    outline: list[str] = Field(..., description="The outline of the question")
    answer_example: list[str] = Field(
        ..., description="The example answer of the question, number of examples must be equal to the number of outlines")
    leading_questions: list[list[str]] = Field(
        ..., description="The lists of leading questions helping users gain ideas to answer, number of lists must be equal to the number of outlines")


@openapi.tag("AI")
@openapi.tag("Interview")
@bp.route("/interviews/ask-buddy", methods=["POST"], strict_slashes=False)
@check_permission([Feature.Code.MOCK_INTERVIEW_AI])
async def ask_buddy(request: Request):
    """
    Ask buddy for help in the interview

    openapi:
    ---
    summary: Ask buddy for help in the interview
    description: This endpoint is used to ask buddy for help in the interview. It can accept either transcription text directly or an audio file for speech-to-text conversion.
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              job_description_id:
                type: string
                description: The job description id of the job seeker
              resume_id:
                type: string
                description: The resume id of the job seeker
              transcription_text:
                type: string
                description: The transcription text of the interview (optional if audio_file is provided)
              audio_file:
                type: string
                format: binary
                description: Audio file containing interview question (optional if transcription_text is provided)
            required:
              - job_description_id
              - resume_id
    responses:
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    job_description_id = request.form.get("job_description_id") if request.form else request.json.get("job_description_id", "")
    resume_id = request.form.get("resume_id") if request.form else request.json.get("resume_id", "")
    transcription_text = request.form.get("transcription_text") if request.form else request.json.get("transcription_text", "")

    # Check if we have an audio file and no transcription text
    if not transcription_text and request.files and 'audio_file' in request.files:
        audio_file = request.files.get('audio_file')
        success, result = await process_speech_to_text(audio_file)

        if success:
            transcription_text = result['transcription']
            logger.info(f"Converted audio to text: {transcription_text}")
        else:
            return json_response(Response(
                error=ResponseCode.SERVER_ERROR,
                message="Failed to process audio file",
                data={"error": result}
            ), status_code=500)

    # If we still don't have transcription text, return error
    if not transcription_text:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Either transcription_text or audio_file must be provided",
            data={"error": "No transcription text or audio file provided"}
        ), status_code=400)

    job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)
    # job_description_query = JobDescriptionQuery(id=job_description_id)
    # job_description_repo = await JobDescriptionRepository.get_one(job_description_query)
    # job_description = f"Job Title: {job_description_repo.position}\nSkills required: {', '.join(job_description_repo.skills)}\nJob Description: {job_description_repo.description}\nLocation: {job_description_repo.city}, {job_description_repo.country}"
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)

    # Initialize Groq client
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))

    # Create chat completion
    # First, classify the question
    GROQ_MODEL = llm_helpers.get_available_groq_model()
    classification_response = client_groq.chat.completions.create(
        model=GROQ_MODEL,
        messages=[
            {
                "role": "system",
                "content": llm_prompts.CLASSIFICATION_PROMPT
            }, {
                "role": "user",
                "content": f"Job Description: {job_description}\nResume: {resume_details}"
            },
            {
                "role": "user",
                "content": f"Question: {transcription_text}"
            }
        ],
        response_format={"type": "json_object"}
    )

    question_category = BuddyQuestionClassificationSchema.model_validate_json(
        classification_response.choices[0].message.content)
    question_category = json.loads(question_category.model_dump_json())["category"]
    logger.info("Question category detected: %s", question_category)

    # Get the suggestion template for the detected category
    suggestion_template = llm_prompts.SUGGESTION_METHODS.get(
        question_category, llm_prompts.SUGGESTION_METHODS["Normal"])

    # Get the leading questions for the detected category
    leading_questions = llm_prompts.LEADING_QUESTIONS.get(question_category, "No leading questions")
    information_provided = "Please help the user answer the interview question of category 'Normal'. You do not provide the leading questions for this category, return an empty array for leading_questions. Because this is normal question, just find factual information and give the direct answer."
    if question_category.lower() != "normal":
        information_provided = f"Using this framework for this category: {question_category}\nHere is the must-use framework for an excellent answer: {suggestion_template}\nYou should remember the Job and user information to tailor the answer to the user's background.\nJob Description: {job_description}\nResume: {resume_details}\nHere are some ideas which help you generate leading questions for the user: {leading_questions}"

    # Then get personalized answer suggestions based on classification
    response = client_groq.chat.completions.create(
        model=GROQ_MODEL,
        messages=[
            {
                "role": "system",
                "content": llm_prompts.ANSWER_SUGGESTION_PROMPT
            },
            {
                "role": "user",
                "content": information_provided
            },
            {
                "role": "user",
                "content": f"Question: {transcription_text}"
            }
        ],
        response_format={"type": "json_object"},
        max_tokens=1024
    )

    # Check if response is valid JSON
    try:
        # json_response = json.loads(response.choices[0].message.content)
        buddy_suggestions = BuddySuggestionSchema.model_validate_json(response.choices[0].message.content)
        json_buddy_suggestions = json.loads(buddy_suggestions.model_dump_json())
        json_buddy_suggestions["category"] = question_category
        if question_category.lower() == "normal":
            json_buddy_suggestions["leading_questions"] = []
    except json.JSONDecodeError:
        logger.error("Failed to parse JSON response from Groq API")
        raise BadRequest(
            message="Failed to generate valid JSON response from AI model",
            context={"error": ResponseCode.EXTERNAL_SERVICE_ERROR}
        )

    # Store the message queue using session_id as a key
    # redis_client.set(session_id, response.choices[0].message.content)

    return json_response(Response(
        error=ResponseCode.SUCCESS,
        message="Success",
        data=json_buddy_suggestions
    ), status_code=200)


@openapi.component
class RequestResumeParserSchema(BaseModel):
    resume_file: str = Field(
        ..., description="The resume file of the job seeker this parameter, is in form-data {'resume_file': File_data byte}. Now only support PDF file.")


@openapi.tag("AI")
@openapi.tag("Resume")
@bp.route("/resumes/parser", methods=["POST"], strict_slashes=False)
@require_token
async def parser_resume(request):
    """
    Parse the resume of a job seeker
    openapi:
    ---
    summary: Parse the resume of a job seeker
    description: This endpoint is used to parse the resume of a job seeker.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestResumeParserSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    time_start = time.time()
    file = request.files.get('resume_file')
    if not file:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Error",
            data={"error": "No file received"}
        ), status_code=400)
    filename = file.name
    file_content = file.body
    file_type = file.type
    logger.info("Received file: %s of type %s", filename, file_type)
    # import the engine
    parser = ParserEngine(
        openai_api_key=os.environ.get("OPENAI_API_KEY"),
        groq_api_key=os.environ.get("GROQ_API_KEY"),
        org="")
    try:
        if file_type == "application/pdf":
            pdfstr = parser.pdf2string(file_content)
        else:
            # other types are not processed yet
            pdfstr = file_content
        logger.info("PDF content read successfully")

        # Process the resume
        # result = parser.query_resume_langchain(pdfstr)
        result = parser.parse_resume_from_pdf(file_content)
        logger.info("Resume parsed successfully")

        time_end = time.time()
        logger.info("Time taken to parse resume: %s seconds", time_end - time_start)

        # add result to response
        response = {
            "role": "assistant",
            "content": result,
            "time_taken": time_end - time_start
        }
        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Success",
            data=response
        ), status_code=200)
    except ValueError as e:
        # Handle specific ValueError exceptions (like JSON serialization errors)
        logger.error(f"Value error processing resume: {str(e)}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Error",
            data={"error": f"Error processing resume: {str(e)}"}
        ), status_code=500)
    except Exception as e:
        # Handle all other exceptions
        logger.error(f"Unexpected error processing resume: {str(e)}", exc_info=True)
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Error",
            data={"error": f"Error processing resume: {str(e)}"}
        ), status_code=500)


@openapi.component
class RequestJobInsightsSchema(BaseModel):
    resume_id: str = Field(..., description="The ID of the resume to analyze")
    job_description_id: str = Field(..., description="The ID of the job description to analyze")


@openapi.component
class JobRequirementInsightsSchema(BaseModel):
    job_title: str = Field(..., description="The title of the job position")
    overview: str = Field(..., description="Overview of the job role and responsibilities")
    core_skills: List[dict] = Field(..., description="List of core technical skills required for the role")
    trending_skills: List[dict] = Field(..., description="List of trending skills in the industry")
    soft_skills: List[dict] = Field(..., description="List of required soft skills")
    professional_courses: List[dict] = Field(..., description="List of recommended professional courses")
    certifications: List[dict] = Field(..., description="List of relevant certifications")
    projects: List[dict] = Field(..., description="List of sample projects demonstrating required skills")
    expected_salary: dict = Field(..., description="Expected salary range and details")
    advises: List[str] = Field(..., description="Career advice and recommendations")
    other_insights: List[str] = Field(..., description="Additional industry and role insights")


@openapi.tag("AI")
@openapi.tag("Resume")
@bp.route("/resumes/get-advices", methods=["POST"], strict_slashes=False)
@require_token
async def get_job_insights(request: Request):
    """
    Get job market insights for a given job position

    openapi:
    ---
    summary: Get job market insights for a given job position
    description: This endpoint is used to get job market insights for a given job position.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestJobInsightsSchema"
          examples:
            sample:
              value:
                resume_id: "123e4567-e89b-12d3-a456-426614174000"
                job_description_id: "123e4567-e89b-12d3-a456-426614174000"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/JobRequirementInsightsSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    components:
      schemas:
        RequestJobInsightsSchema:
          type: object
          required:
            - resume_id
            - job_description_id
          properties:
            resume_id:
              type: string
              description: The ID of the resume to analyze
              example: "123e4567-e89b-12d3-a456-426614174000"
            job_description_id:
              type: string
              description: The ID of the job description to analyze
              example: "123e4567-e89b-12d3-a456-426614174000"
    """
    user_id = request.ctx.user['id']
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    # get resume details
    resume_id: str = request.json.get("resume_id", "")
    force_regenerate: bool = request.json.get("force_regenerate", False)
    # get resume content, and remove the null fields
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)
    job_description_id: str = request.json.get("job_description_id", "")  # get job description id from request

    update_existing_job_insights = ({}, False)
    if job_description_id == "":
        # if job description id is not provided, get job description from request
        job_description: str = request.json.get("job_description", "")
        if job_description == "":
            # both job description id and job description are not provided
            # return error
            return json_response(Response(
                error=ResponseCode.CLIENT_BAD_REQUEST,
                message="Error",
                data={"error": "Job description is required, but either job_description_id or job_description is not provided"}
            ), status_code=422)
    else:
        # if job description id is provided, get job description from database
        job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)
        if not force_regenerate:
            # Check if there's an existing application kit for this job description and resume
            query_params = {
                "jd_id": job_description_id,
                "resume_id": resume_id,
                "user_id": user_id
            }
            app_kit_query = ApplicationKitQuery(
                **query_params,
                order_by=["-created_at"]
            )
            pagination = Pagination(page=1, page_size=1)
            app_kits, total = await ApplicationKitRepository.get_list(app_kit_query, pagination)

            # If application kit exists and has job_insight_id, return existing job insights
            if app_kits and total > 0 and app_kits[0].get("job_insight_id"):
                job_insight_id = app_kits[0]["job_insight_id"]
                job_insight_query = JobInsightsQuery(id=job_insight_id)
                job_insight = await JobInsightsRepository.get_one(job_insight_query)

                if job_insight:
                    # Check if the job description has been updated recently
                    if (datetime.datetime.fromisoformat(str(job_description_repo.updated_at)).astimezone(datetime.UTC) <
                        datetime.datetime.now(datetime.UTC) - datetime.timedelta(hours=1) or
                        datetime.datetime.fromisoformat(str(job_description_repo.updated_at)) ==
                            datetime.datetime.fromisoformat(str(job_description_repo.created_at))):

                        return json_response(Response(
                            error=ResponseCode.SUCCESS,
                            message="Retrieved existing job insights",
                            data=job_insight
                        ), status_code=200)

            # If no existing job insights found or job description was updated, continue with generating new insights
            db_data = {
                "jd_id": job_description_id,
                "resume_id": resume_id,
                "user_id": user_id
            }
            job_insights_query = JobInsightsQuery(**db_data)
            pagination = Pagination(page=1)
            job_insights_list = await JobInsightsRepository.get_list(job_insights_query, pagination)
            if job_insights_list[0]:
                latest_job_insights = job_insights_list[0][-1]
                update_existing_job_insights = (latest_job_insights, True)
                if (datetime.datetime.fromisoformat(str(job_description_repo.updated_at)).astimezone(datetime.UTC) <
                    datetime.datetime.now(datetime.UTC) - datetime.timedelta(hours=1) or
                    datetime.datetime.fromisoformat(str(job_description_repo.updated_at)) ==
                        datetime.datetime.fromisoformat(str(job_description_repo.created_at))):

                    updated_application_kit = await llm_helpers.update_or_create_application_kit(
                        {
                            "jd_id": job_description_id,
                            "resume_id": resume_id,
                            "job_insight_id": str(latest_job_insights["id"]),
                            "user_id": user_id
                        }
                    )
                    return json_response(Response(
                        error=ResponseCode.SUCCESS,
                        message="Success. Job insights retrieved and added to Application Kit successfully.",
                        data=latest_job_insights
                    ), status_code=200)
    job_details = llm_helpers.extract_job_details(job_description)

    job_desire = f"Jobs {job_details['job_title']} for level {job_details['job_level']} in {job_details['job_location']['city']} {job_details['job_location']['country']}"
    if job_details['job_level'] == "":
        job_desire = f"Jobs {job_details['job_title']} in {job_details['job_location']['city']} {job_details['job_location']['country']}"
    search_job = f"100 jobs for {job_desire}"
    search_job_trends = f"{job_desire}, What are neccessary skills, latest trends, professional courses or certifications or projects"
    # search job market insights
    search = GoogleSerperAPIWrapper(serper_api_key=os.environ.get('SERPER_API_KEY'))
    result1 = search.run(search_job)
    result2 = search.run(search_job_trends)

    # Data model for LLM to generate

    class CoreSkill(BaseModel):
        name: str = ""
        description: str = ""

    class TrendingSkill(BaseModel):
        name: str = ""
        description: str = ""

    class SoftSkill(BaseModel):
        name: str = ""
        description: str = ""

    class ProfessionalCourse(BaseModel):
        name: str = ""
        description: str = ""
        provider: str = ""

    class Certification(BaseModel):
        name: str = ""
        description: str = ""
        provider: str = ""

    class Project(BaseModel):
        name: str = ""
        description: str = ""
        required_skills: List[str] = []

    class ExpectedSalary(BaseModel):
        min_salary: float = 0.0
        max_salary: float = 0.0
        currency: str = "USD"
        frequency: str = "yearly"
        level: str = "entry"

    class JobRequirementInsights(BaseModel):
        job_title: str = ""
        overview: Optional[str]
        core_skills: List[CoreSkill]
        trending_skills: List[TrendingSkill]
        soft_skills: Optional[List[SoftSkill]]
        professional_courses: List[ProfessionalCourse]
        certifications: List[Certification]
        projects: List[Project]
        expected_salary: Optional[ExpectedSalary]
        advises: Optional[List[str]]
        other_insights: Optional[List[str]]

    completion = client_groq.chat.completions.create(
        model=llm_helpers.get_available_groq_model("mistral-saba-24b"),
        temperature=0.8,
        seed=13,
        response_format={"type": "json_object"},
        stream=False,
        messages=[
            {
                "role": "system",
                "content": f'For Job Position of Desire: {job_desire} research to identify the current market requirements for a person at the job including the relevant skills, some unique research projects or common projects along with what experience would be required. You will also receive a resume so you can give the tailored requirements, insights and neccessary advices based on the user experience and knowledge.',
            }, {
                "role": "user",
                "content": f"Here is the Resume: {resume_details}. Please generate the job insights based on this resume and the job market information."
            },
            {
                "role": "user",
                "content": """With the given resume and information below, please provide a report on what are the skills required, latest trends of that industry and some unique real time projects, with some professional courses, certifications that can be there which enhances the chance of a person to get a job. Please provide the response in the following JSON format:

{
    "job_title": "string",
    "overview": "string",
    "core_skills": [
        {
            "name": "string",
            "description": "string"
        }
    ],
    "trending_skills": [
        {
            "name": "string",
            "description": "string"
        }
    ],
    "soft_skills": [
        {
            "name": "string",
            "description": "string"
        }
    ],
    "professional_courses": [
        {
            "name": "string",
            "description": "string",
            "provider": "string"
        }
    ],
    "certifications": [
        {
            "name": "string",
            "description": "string",
            "provider": "string"
        }
    ],
    "projects": [
        {
            "name": "string",
            "description": "string",
            "required_skills": ["string"]
        }
    ],
    "expected_salary": {
        "min_salary": "float. Default is 0.",
        "max_salary": "float. Default is 0.",
        "currency": "string. Default is USD.",
        "frequency": "string. Default is yearly.",
        "level": "string. Default is Intern."
    },
    "advises": [
        "string"
    ],
    "other_insights": [
        "string"
    ]
}

The data: """ + result1+result2
            }
        ]
    )

    # Extract response content
    response_text = completion.choices[0].message.content
    insight = JobRequirementInsights.model_validate_json(response_text)
    response = json.loads(insight.model_dump_json())

    # Store the job insights in the database
    try:
        # Prepare data for database insertion
        db_data = {
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "job_title": response.get("job_title", ""),
            "overview": response.get("overview", ""),
            "core_skills": response.get("core_skills", []),
            "trending_skills": response.get("trending_skills", []),
            "soft_skills": response.get("soft_skills", []),
            "professional_courses": response.get("professional_courses", []),
            "certifications": response.get("certifications", []),
            "projects": response.get("projects", []),
            "expected_salary": response.get("expected_salary", {}),
            "advises": response.get("advises", []),
            "other_insights": response.get("other_insights", [])
        }

        # Check if we need to update existing job insight or create a new one
        if update_existing_job_insights[1] and update_existing_job_insights[0]:
            # Update existing job insight
            insight_id = update_existing_job_insights[0]["id"]
            _, stored_insight = await JobInsightsRepository.update(insight_id, db_data)
        else:
            # Create new job insight
            stored_insight = await JobInsightsRepository.create_one(db_data)

        # Add database ID to response
        response["id"] = str(stored_insight["id"])

        _ = await llm_helpers.update_or_create_application_kit(
            {
                "jd_id": job_description_id,
                "resume_id": resume_id,
                "job_insight_id": str(stored_insight["id"]),
                "user_id": user_id
            }
        )
    except Exception as e:
        error_logger.exception("Failed to store job insights: %s", str(e))
        # Continue with response even if storage fails
        # We don't want to fail the API response just because storage failed
        pass

    return json_response(Response(
        error=ResponseCode.SUCCESS,
        message="Success",
        data=stored_insight
    ), status_code=200)


class RewriteSummary(BaseModel):
    formal: str = Field(default="", description="The professional summary")
    informal: str = Field(default="", description="The casual summary")
    neutral: str = Field(default="", description="The neutral summary")


@openapi.component
class RequestRewriteSummarySchema(BaseModel):
    content: str = Field(..., description="The content to rewrite")
    length: int = Field(..., description="The length of the summary")


@openapi.tag("AI")
@openapi.tag("Resume")
@bp.route("/resumes/rewrite-summary", methods=["POST"], strict_slashes=False)
@require_token
@check_permission([Feature.Code.RESUME_BUILDER])
async def rewrite_summary(request: Request):
    """
    This endpoint is used to rewrite the summary of the resume.
    openapi:
    ---
    summary: Rewrite the summary of the resume
    description: This endpoint is used to rewrite the summary of the resume.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestRewriteSummarySchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    time_start = time.time()
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    # get content
    content: str = request.json.get("content", "")
    assert content != "", "Content is required"
    length: int = request.json.get("length", 300)
    assert length > 0, "Length must be greater than 0"
    # Get available GROQ model
    GROQ_MODEL = llm_helpers.get_available_groq_model()
    completion = client_groq.chat.completions.create(
        model=GROQ_MODEL,
        temperature=0.8,
        seed=13,
        response_format={"type": "json_object"},
        stream=False,
        messages=[
            {
                "role": "system",
                "content": llm_prompts.SUMMARY_REWRITE_PROMPT
            },
            {
                "role": "user",
                "content": f"Please rewrite the summary into 3 versions in length of {length} words or more. Here is the content: {content}"
            }
        ]
    )
    response_json = RewriteSummary.model_validate_json(completion.choices[0].message.content)
    total_time = round(time.time() - time_start, 2)
    logger.info(f"Total time for API {request.path} is {total_time} seconds")
    return json_response(Response(
        error=ResponseCode.SUCCESS,
        message=f"Success. Took {total_time} seconds.",
        data=json.loads(response_json.model_dump_json()),
    ), status_code=200)


class RequestGenerateFollowUpsSchema(BaseModel):
    job_description_id: str = Field(..., description="The job description id of the job seeker")
    resume_id: str = Field(..., description="The resume id of the job seeker")


@openapi.tag("AI")
@openapi.tag("Cover Letter")
@bp.route("/follow-ups/generate", methods=["POST"], strict_slashes=False)
@require_token
@check_permission([Feature.Code.COVER_LETTER_AI_BUILDER])
async def generate_follow_ups(request: Request):
    """
    This endpoint is used to generate the follow-ups for the interview.
    openapi:
    ---
    summary: Generate the follow-ups for the interview
    description: This endpoint is used to generate the follow-ups for the interview.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestGenerateFollowUpsSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    time_start = time.time()
    user_id = request.ctx.user['id']

    # get content
    job_description_id: str = request.json.get("job_description_id", "")
    resume_id: str = request.json.get("resume_id", "")
    if not job_description_id:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Job description id is required",
            data={"error": "Job description id is required"}
        ), status_code=403)

    if not resume_id:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Resume id is required",
            data={"error": "Resume id is required"}
        ), status_code=403)

    force_regenerate: bool = request.json.get("force_regenerate", False)
    if not force_regenerate:
        # Check if there's an existing application kit for this job description and resume
        query_params = {
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "user_id": user_id
        }
        app_kit_query = ApplicationKitQuery(
            **query_params,
            order_by=["-created_at"]
        )
        pagination = Pagination(page=1, page_size=1)
        app_kits, total = await ApplicationKitRepository.get_list(app_kit_query, pagination)

        # If application kit exists and has follow_up_id, return existing follow-up
        if app_kits and total > 0 and app_kits[0].get("follow_up_id"):
            follow_up_query = FollowUpLetterQuery(id=app_kits[0]["follow_up_id"])
            follow_up = await FollowUpLetterRepository.get_one(follow_up_query)

            if follow_up:
                time_end = time.time()
                total_time = round(time_end - time_start, 2)

                return json_response(Response(
                    error=0,
                    message=f"Retrieved existing follow-up letter in {total_time} seconds",
                    data={
                        "follow_up_letters": follow_up,
                        "total_time": total_time,
                        "is_cached": True
                    }
                ), status_code=200)

    # If no existing follow-up found, generate a new one
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)
    if job_description == "Job description not found":
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Job description not found",
            data={"error": "Job description not found"}
        ), status_code=404)
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)
    if resume_details == "Resume not found":
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Resume not found",
            data={"error": "Resume not found"}
        ), status_code=404)

    # Prepare system and user messages for generating follow-up letters
    user_and_system_messages = [
        {
            "role": "system",
            "content": llm_prompts.PROMPT_GENERATE_FOLLOWUP
        },
        {
            "role": "user",
            "content": f"Here is the job description:\n{job_description}\n\nHere is my resume information:\n{resume_details}\n\nPlease generate a professional follow-up letter based on the requirements (at least 2 paragraphs and 150 words), including guidance on timing and customization."
        }
    ]

    # Get available GROQ model
    GROQ_MODEL = llm_helpers.get_available_groq_model()

    try:
        # Generate the follow-up letters using GROQ
        completion = client_groq.chat.completions.create(
            model=GROQ_MODEL,
            temperature=0.8,
            seed=10,
            response_format={"type": "json_object"},
            messages=user_and_system_messages
        )

        # Extract the generated letters from response
        response_content = json.loads(completion.choices[0].message.content)
        follow_up_letter = response_content.get("followup", "")
        customization_note = response_content.get("customization_note", "")

        # Save the follow-up to the database
        follow_up_db_data = {
            "user_id": user_id,
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "follow_up": follow_up_letter,
            "customization_note": customization_note
        }
        new_follow_up = await FollowUpLetterRepository.create_one(follow_up_db_data)
        app_kit_data = {
            "jd_id": job_description_id,
            "resume_id": resume_id,
            "user_id": user_id,
            "follow_up_id": new_follow_up["id"]
        }
        _ = await llm_helpers.update_or_create_application_kit(
            app_kit_data
        )

        time_end = time.time()
        total_time = round(time_end - time_start, 2)

        return json_response(Response(
            error=0,
            message=f"Successfully generated follow-up letter in {total_time} seconds",
            data={
                "follow_up_letter": new_follow_up,
                "total_time": total_time,
                "is_cached": False
            }
        ), status_code=200)

    except Exception as e:
        logger.error(f"Error generating follow-up letter: {str(e)}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Failed to generate follow-up letter",
            data={"error": str(e)}
        ), status_code=500)


@openapi.tag("AI")
@openapi.tag("Job")
@bp.route("/job/parser", methods=["POST"], strict_slashes=False)
@require_token
async def parse_job_description(request: Request):
    """
    Parse job description text into structured data.

    openapi:
    ---
    summary: Parse job description text into structured data
    description: This endpoint extracts key information from a job description including position, company, location, and skills.
    requestBody:
      content:
        application/json:
          schema:
            type: object
            properties:
              job_description:
                type: string
                description: The job description text to parse
            required:
              - job_description
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  type: object
                  properties:
                    position:
                      type: string
                      description: Job position/title
                    company:
                      type: string
                      description: Company name
                    city:
                      type: string
                      description: City location
                    country:
                      type: string
                      description: Country location
                    skills:
                      type: array
                      items:
                        type: string
                      description: List of required skills
                    total_time:
                      type: number
                      description: Processing time in seconds
      400:
        description: Bad Request
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 400
                message:
                  type: string
                  description: Error message
                data:
                  type: object
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      500:
        description: Server Error
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 500
                message:
                  type: string
                  description: Error message
                data:
                  type: object
    """
    time_start = time.time()

    # Get job description from request
    job_description = request.json.get('job_description', '')

    # Validate input
    if not job_description or not isinstance(job_description, str):
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Invalid or empty job description",
            data={}
        ), status_code=429)

    # Prepare system and user messages for parsing job description
    user_and_system_messages = [
        {
            "role": "system",
            "content": "You are an expert job description parser. Extract key information from job descriptions into structured data."
        },
        {
            "role": "user",
            "content": llm_prompts.PROMPT_JOB_DESCRIPTION_PARSER + f"""
Here is the job description:
{job_description}"""
        }
    ]

    # Get available GROQ model
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
    GROQ_MODEL = llm_helpers.get_available_groq_model()

    try:
        # Parse the job description using GROQ
        completion = client_groq.chat.completions.create(
            model=GROQ_MODEL,
            temperature=0.2,
            seed=42,
            response_format={"type": "json_object"},
            messages=user_and_system_messages
        )

        # Extract the parsed data from response
        parsed_data = json.loads(completion.choices[0].message.content)
        logger.info(f"Parsed data: {parsed_data}")
        if parsed_data.get("position", "") == "Invalid job description":
            logger.error("Invalid job description, returning error")
            return json_response(Response(
                error=ResponseCode.CLIENT_BAD_REQUEST,
                message="Invalid job description",
                data={"error": "The provided job description is invalid or insufficient"}
            ), status_code=422)

        # Ensure all required fields exist with default values if missing
        result = {
            "position": parsed_data.get("position", ""),
            "company": parsed_data.get("company", ""),
            "city": parsed_data.get("city", ""),
            "country": parsed_data.get("country", ""),
            "skills": parsed_data.get("skills", [])
        }

        # Validate skills is a list
        if not isinstance(result["skills"], list):
            result["skills"] = []

        time_end = time.time()
        total_time = round(time_end - time_start, 2)

        # Add total time to result
        result["total_time"] = total_time

        return json_response(Response(
            error=0,
            message="Successfully parsed job description",
            data=result
        ), status_code=200)

    except json.JSONDecodeError:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="AI model response is not valid JSON",
            data={}
        ), status_code=400)
    except Exception as e:
        logger.error(f"Error parsing job description: {str(e)}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Failed to parse job description",
            data={"error": str(e)}
        ), status_code=500)


@openapi.component
class ResponseLatestApplicationKitSchema(BaseModel):
    """Schema for latest application kit response"""
    id: str = Field(..., description="UUID of application kit")
    user_id: str = Field(..., description="UUID of user")
    jd_id: str = Field(..., description="UUID of job description")
    resume_id: Optional[str] = Field(None, description="UUID of resume")
    cover_letter_id: Optional[str] = Field(None, description="UUID of cover letter")
    follow_up_id: Optional[str] = Field(None, description="UUID of follow-up letter")
    mock_interview_id: Optional[str] = Field(None, description="UUID of mock interview")
    job_insight_id: Optional[str] = Field(None, description="UUID of job insights")
    created_at: datetime.datetime = Field(..., description="Created at")
    updated_at: datetime.datetime = Field(..., description="Updated at")


@openapi.component
class ProfessionSchema(BaseModel):
    """Schema for profession response"""
    profession: str = Field(..., description="User's profession category")
    other: Optional[str] = Field(None, description="Specific profession if category is Other")


@openapi.component
class ResponseLatestApplicationKitWithProfessionSchema(ResponseLatestApplicationKitSchema):
    """Schema for latest application kit response with profession"""
    profession_info: Optional[ProfessionSchema] = Field(None, description="Professional category information")


@openapi.component
class RequestResumeAnalysisSchema(BaseModel):
    resume_id: str = Field(..., description="The ID of the resume to analyze")
    job_description_id: str = Field(..., description="The ID of the job description to analyze")

@openapi.tag("AI")
@openapi.tag("Resume")
@bp.route("/resumes/analyze", methods=["POST"], strict_slashes=False)
@require_token
# @check_permission([Feature.Code.RESUME_AI_BUILDER])
async def analyze_resume_api(request: Request):
    """
    Analyze a resume against a job description to get detailed insights and ATS score.

    openapi:
    ---
    summary: Analyze resume against job description
    description: This endpoint analyzes a resume against a job description to provide detailed insights and ATS compatibility score.
    requestBody:
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestResumeAnalysisSchema"
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  type: object
                  properties:
                    ATS_score:
                      type: integer
                      description: ATS compatibility score (0-100)
                    matching_jd_resume:
                      type: number
                      description: Score indicating how well resume matches job description
                    jd_keywords:
                      type: array
                      items:
                        type: string
                      description: Keywords extracted from job description
                    missing_keywords:
                      type: array
                      items:
                        type: string
                      description: Keywords from job description missing in resume
                    professional_score:
                      type: number
                      description: Score indicating professional quality of resume
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Resume or job description not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    time_start = time.time()

    # Get resume and job description IDs from request
    resume_id: str = request.json.get("resume_id", "")
    job_description_id: str = request.json.get("job_description_id", "")

    # Validate inputs
    if not resume_id:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Resume ID is required",
            data={}
        ), status_code=422)

    # Get resume content
    resume_details: str = await llm_helpers.get_filtered_resume_content(resume_id)
    if resume_details == "Resume not found":
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="Resume not found",
            data={"error": "Resume not found"}
        ), status_code=404)

    # Get job description
    if job_description_id:
        job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)
    else:
        job_description = ""
    # if job_description == "Job description not found":
    #     return json_response(Response(
    #         error=ResponseCode.CLIENT_BAD_REQUEST,
    #         message="Job description not found",
    #         data={"error": "Job description not found"}
    #     ), status_code=404)
    # a flag to notify if this is a free user -> return only the ATS score without other details
    is_free_user = True
    user_id = request.ctx.user['id']
    # Get user subscription information

    try:
        is_premium_user = await subscription.is_premium_user(user_id)
        is_free_user = not is_premium_user
    except Exception as e:
        logger.error(f"Error checking if user is premium: {str(e)}")


    try:
        # Parse resume content from string to JSON
        resume_json = json.loads(resume_details)

        # Analyze resume
        analysis_result = analyze_resume(resume_json, job_description, is_free_user)

        # Calculate time taken
        time_end = time.time()
        total_time = round(time_end - time_start, 2)
        # Extract scores from analysis result
        ats_score = analysis_result.get("ATS_score", 0)
        keyword_score = analysis_result.get("keyword_score", 0)
        structure_score = analysis_result.get("structure_score", 0)
        content_quality_score = analysis_result.get("content_quality_score", 0)

        # Get resume_id from the request
        # resume_id = request.json.get("resume_id")

        if resume_id:
            try:
                # Create update data dictionary
                update_data = {
                    "ats_score": ats_score,
                    "keyword_score": keyword_score,
                    "structure_score": structure_score,
                    "content_quality_score": content_quality_score,
                    "updated_at": datetime.datetime.now()
                }

                # Update the resume with the analysis scores
                affected_rows, updated_resume = await ResumeRepository.update(
                    pk=resume_id,
                    data=update_data
                )

                if affected_rows == 0:
                    logger.warning(f"Resume with ID {resume_id} not found or not updated")
            except Exception as e:
                logger.error(f"Error updating resume with analysis scores: {str(e)}")

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message=f"Successfully analyzed resume in {total_time} seconds",
            data=analysis_result
        ), status_code=200)

    except json.JSONDecodeError:
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Failed to parse resume JSON",
            data={"error": "Invalid resume format"}
        ), status_code=500)
    except Exception as e:
        logger.error(f"Error analyzing resume: {str(e)}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Failed to analyze resume",
            data={"error": str(e)}
        ), status_code=500)

@openapi.tag("AI")
@openapi.tag("Resume")
@bp.route("/job/profession_of_resume", methods=["POST"], strict_slashes=False)
@require_token
async def profession_of_resume(request: Request):
    """
    Detects professions based on a resume and updates the user's profession field.

    Args:
        resume_id (str): The ID of the resume to analyze

    Returns:
        list[str]: List of detected professions

    Raises:
        Exception: If there's an error during profession detection
    """
    try:
        # Get resume object to extract user_id
        resume_id = request.json.get("resume_id")
        resume_query = ResumeQuery(id=resume_id)
        resume = await ResumeRepository.get_one(resume_query)
        if not resume:
            logger.error(f"Resume with ID {resume_id} not found")
            return []

        # Extract user_id from the resume
        user_id = resume.get("user_id")
        if not user_id:
            logger.error(f"User ID not found in resume {resume_id}")
            return []

        # Get resume content
        resume_details = await llm_helpers.get_filtered_resume_content(resume_id)

        # Initialize Groq client
        client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))
        GROQ_MODEL = llm_helpers.get_available_groq_model()

        # Prepare system message for profession analysis
        messages = [
            {
                "role": "system",
                "content": """You are an expert at analyzing resumes and categorizing professions.
Use your reasoning to analyze the resume and you are responsible to determine the user's profession. Focus on less than 3 professions highest correlated with the resume.
E.g. the profession 'Marketing Lead' if it shows in the resume headline, 'Backend Developer' if user experience reflects the tech stack related to Backend.
If the profession doesn't clearly fit into the main categories, use ['Other']. Returning array of multiple professions is allowed.
Return the response in JSON format.
{
    "profession": ["List of the Profession, default: Other"]
}"""
            },
            {
                "role": "user",
                "content": f"Based on this resume content, determine the professional positions that users are applying to: {resume_details}"
            }
        ]

        # Get profession analysis from Groq
        completion = client_groq.chat.completions.create(
            model=GROQ_MODEL,
            messages=messages,
            temperature=0.2,
            seed=4,
            response_format={"type": "json_object"}
        )

        profession_info = json.loads(completion.choices[0].message.content)

        profession = profession_info.get("profession", [])
        if profession == [] or profession == ["Other"]:
            profession = []

        # Handle if profession is a string instead of a list
        if isinstance(profession, str):
            profession = [profession]

        logger.info(f"Detected professions for resume {resume_id}: {profession}")

        # Update user's profession field
        if isinstance(profession, list) and len(profession) > 0:
            await UserRepository.update(user_id, {"profession": profession})
            logger.info(f"Updated user {user_id} with professions: {profession}")
        else:
            logger.info(f"No profession detected for user {user_id}")

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Profession detection successful",
            data={"professions": profession}
        ), status_code=200)

    except Exception as e:
        logger.error(f"Error detecting professions from resume {resume_id}: {str(e)}")
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Error detecting professions",
            data={"error": str(e)}
        ), status_code=500)


@openapi.component
class SpeechToTextRequestSchema(BaseModel):
    """Schema for speech-to-text request"""
    audio_file: str = Field(..., description="The audio file to transcribe (in form-data)")


# Create a reusable function for speech-to-text conversion
async def process_speech_to_text(audio_file):
    """
    Process an audio file for speech-to-text conversion.

    Args:
        audio_file: The audio file object to transcribe

    Returns:
        tuple: (success, result) where success is a boolean and result is either the transcription or error message
    """
    try:
        time_start = time.time()

        # Get file details
        filename = audio_file.name
        file_content = audio_file.body
        file_type = audio_file.type

        logger.info(f"Processing audio file: {filename} of type {file_type} for transcription")

        # Initialize Groq client
        client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))

        # Call Groq's Whisper model for transcription
        response = client_groq.audio.transcriptions.create(
            file=(filename, file_content),
            model="whisper-large-v3-turbo",
            response_format="text"
        )

        # Get transcription result
        transcription = response

        time_end = time.time()
        total_time = round(time_end - time_start, 2)

        logger.info(f"Transcription completed in {total_time} seconds")

        return True, {
            "transcription": transcription,
            "time_taken": total_time
        }

    except Exception as e:
        logger.error(f"Error during audio transcription: {str(e)}", exc_info=True)
        return False, str(e)


@openapi.tag("AI")
@openapi.tag("Interview")
@bp.route("/interviews/speech2text", methods=["POST"], strict_slashes=False)
# @require_token
async def speech_to_text(request: Request):
    """
    Convert speech audio to text

    openapi:
    ---
    summary: Convert speech audio to text
    description: This endpoint transcribes English audio to text with proper formatting, spelling, and punctuation.
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              audio_file:
                type: string
                format: binary
                description: Audio file to transcribe
            required:
              - audio_file
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  type: object
                  properties:
                    transcription:
                      type: string
                      description: The transcribed text from the audio
                    time_taken:
                      type: number
                      description: Processing time in seconds
      400:
        description: Bad Request
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 400
                message:
                  type: string
                  description: Error message
                data:
                  type: object
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      500:
        description: Server Error
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 500
                message:
                  type: string
                  description: Error message
                data:
                  type: object
    """
    # Get audio file from request
    audio_file = request.files.get('audio_file')
    if not audio_file:
        return json_response(Response(
            error=ResponseCode.CLIENT_BAD_REQUEST,
            message="No audio file received",
            data={"error": "No audio file received"}
        ), status_code=400)

    # Process the audio file
    success, result = await process_speech_to_text(audio_file)

    if success:
        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message="Transcription successful",
            data=result
        ), status_code=200)
    else:
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="Failed to transcribe audio",
            data={"error": result}
        ), status_code=500)


async def analyze_mock_interview_core(question_id, job_description_id, answer, audio_file=None):
    """
    Core function to analyze a mock interview answer.

    Args:
        question_id (str): The ID of the question to analyze
        job_description_id (str): The job description ID
        answer (str): The answer text to analyze
        audio_file (file, optional): Audio file containing the answer

    Returns:
        dict: Analysis results and question data
    """
    time_start = time.time()
    client_groq = Groq(api_key=os.getenv("GROQ_API_KEY"))

    # Process audio file if provided and no answer text
    if not answer and audio_file:
        success, result = await process_speech_to_text(audio_file)

        if success:
            answer = result['transcription']
            logger.info(f"Converted audio to text: {answer}")
        else:
            return {
                "error": ResponseCode.SERVER_ERROR,
                "message": "Failed to process audio file",
                "data": {"error": result},
                "status_code": 500
            }

    # If we still don't have an answer, return error
    if not answer:
        return {
            "error": ResponseCode.CLIENT_BAD_REQUEST,
            "message": "Either answer or audio_file must be provided",
            "data": {"error": "No answer or audio file provided"},
            "status_code": 400
        }

    # find question id
    query = QuestionQuery(
        id=question_id
    )
    question_repo = await QuestionRepository.get_one(query)
    # get question
    question: str = question_repo.question

    # get job description
    job_description, job_description_repo = await llm_helpers.get_job_description(job_description_id)

    # Create chat completion
    # First, classify the question
    GROQ_MODEL = llm_helpers.get_available_groq_model()
    classification_response = client_groq.chat.completions.create(
        model=GROQ_MODEL,
        messages=[
            {
                "role": "system",
                "content": llm_prompts.CLASSIFICATION_PROMPT
            }, {
                "role": "user",
                "content": f"Question: {question}\nJob Description: {job_description}"
            }
        ],
        response_format={"type": "json_object"}
    )

    question_category = BuddyQuestionClassificationSchema.model_validate_json(
        classification_response.choices[0].message.content)
    question_category = json.loads(question_category.model_dump_json())["category"]
    logger.info("Question category detected: %s", question_category)
    if question_category == "invalid":
        return {
            "error": ResponseCode.CLIENT_BAD_REQUEST,
            "message": "Invalid question",
            "data": None,
            "status_code": 400
        }

    # Modified system prompt to handle non-applicable fields
    system_prompt = llm_prompts.SYSTEM_PROMPT_ANALYZE_INTERVIEW_ANSWER

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": f"I'm answering a question of category {question_category}.\nHere is the question: {question}\nHelp me analyze my answer for improvement: {answer}"}
    ]
    try:
        completion = client_groq.chat.completions.create(
            model=llm_helpers.get_available_groq_model("llama-3.3-70b-versatile"),
            messages=messages,
            seed=2,
            temperature=0.7,
            response_format={"type": "json_object"}
        )
        time_end = time.time()
        logger.info("Time taken to analyze interview answer: %s seconds", time_end - time_start)
        completion_content = json.loads(completion.choices[0].message.content)
        logger.info("Original completion content: %s", completion_content)
        if completion_content.get('category', 'invalid') == 'invalid':
            return {
                "error": ResponseCode.CREATE_FAILED,
                "message": "Please provide a valid answer",
                "data": {
                    "error": "Please provide a valid answer"
                },
                "status_code": 999 # for later processing in the caller
            }
    except Exception as e:
        logger.error(f"Error with Groq API: {str(e)}")
        if "temporarily unavailable" in str(e).lower():
            return {
                "error": ResponseCode.SERVER_ERROR,
                "message": "Service temporarily unavailable",
                "data": {"error": "Service temporarily unavailable"},
                "status_code": 400
            }
        return {
          "error": ResponseCode.CREATE_FAILED,
          "message": str(e),
          "data": {
              "error": "Please provide a valid answer"
          },
          "status_code": 999 # for later processing in the caller
        }

    # Process the analysis fields to ensure non-applicable fields are null
    analysis_dict = completion_content.get('analysis', {})
    processed_analysis = {}

    # List of all possible analysis fields
    all_fields = [
        'situation', 'task', 'action', 'result', 'skills', 'academic', 'management',
        'personal', 'seek_info', 'patient_safety', 'initiative', 'escalate', 'support',
        'strategy', 'technology', 'analytics', 'results', 'transformation'
    ]

    # Process each field to ensure non-applicable fields are null
    for field in all_fields:
        value = analysis_dict.get(field, '')
        # Check if the field contains a non-applicable message
        if value and any(phrase in value.lower() for phrase in ['not applicable', 'n/a', 'not relevant']):
            processed_analysis[field] = ''
        else:
            processed_analysis[field] = value

    response = {
        "category": completion_content.get('category', ''),
        "analysis": processed_analysis,
        "strengths": completion_content.get('strengths', []),
        "improvements": completion_content.get('improvements', []),
        "rating": completion_content.get('rating', 0)
    }
    response = {
        "role": completion.choices[0].message.role,
        "question_id": question_id,
        "content": response,
        "time_taken": time_end - time_start
    }
    logger.info(f"Response: {response}")
    # Extract fields from AI response
    rating = response['content']['rating']
    overall_strengths = response['content']['strengths']
    overall_improvements = response['content']['improvements']
    if rating == 0:
        # ignore updating database and return question
        question_repo = {}
        question_repo['point'] = 0
        question_repo['overall_strengths'] = overall_strengths
        question_repo['overall_improvements'] = overall_improvements
        return {
            "error": ResponseCode.SUCCESS,
            "message": "Success",
            "data": question_repo,
            "status_code": 200
        }

    # Extract fields from response content with default values for missing keys
    content = response['content']
    category = content.get('category', '')
    analysis = content.get('analysis', {})

    # Extract analysis fields with empty string defaults
    analysis_situation = analysis.get('situation', '')
    analysis_task = analysis.get('task', '')
    analysis_action = analysis.get('action', '')
    analysis_result = analysis.get('result', '')
    analysis_skills = analysis.get('skills', '')
    analysis_academic = analysis.get('academic', '')
    analysis_management = analysis.get('management', '')
    analysis_personal = analysis.get('personal', '')
    analysis_seek_info = analysis.get('seek_info', '')
    analysis_patient_safety = analysis.get('patient_safety', '')
    analysis_initiative = analysis.get('initiative', '')
    analysis_escalate = analysis.get('escalate', '')
    analysis_support = analysis.get('support', '')
    analysis_strategy = analysis.get('strategy', '')
    analysis_technology = analysis.get('technology', '')
    analysis_analytics = analysis.get('analytics', '')
    analysis_results = analysis.get('results', '')
    analysis_transformation = analysis.get('transformation', '')

    # Prepare base data dictionary with required fields
    data = {
        "user_answer": answer,
        "category": category,
        "overall_strengths": [{"description": item} for item in overall_strengths],
        "overall_improvements": [{"description": item} for item in overall_improvements]
    }

    # Add non-empty analysis fields to data dictionary
    analysis_fields = {
        "analysis_situation": analysis_situation,
        "analysis_task": analysis_task,
        "analysis_action": analysis_action,
        "analysis_result": analysis_result,
        "analysis_skills": analysis_skills,
        "analysis_academic": analysis_academic,
        "analysis_management": analysis_management,
        "analysis_personal": analysis_personal,
        "analysis_seek_info": analysis_seek_info,
        "analysis_patient_safety": analysis_patient_safety,
        "analysis_initiative": analysis_initiative,
        "analysis_escalate": analysis_escalate,
        "analysis_support": analysis_support,
        "analysis_strategy": analysis_strategy,
        "analysis_technology": analysis_technology,
        "analysis_analytics": analysis_analytics,
        "analysis_results": analysis_results,
        "analysis_transformation": analysis_transformation
    }

    # Only add non-empty analysis fields to data
    data.update({k: v for k, v in analysis_fields.items() if v})

    # Add rating if present
    if rating:
        data["point"] = int(rating)
    # after collecting all data, update the question
    await QuestionRepository.update(pk=question_id, data=data)
    updated_question = await QuestionRepository.get_one(query)

    return {
        "error": ResponseCode.SUCCESS,
        "message": "Success",
        "data": dict(updated_question),
        "status_code": 200
    }


@openapi.tag("AI")
@openapi.tag("Mock Interview")
@bp.route("/mock-interviews/analyze", methods=["POST"], strict_slashes=False)
@require_token
# Open for free users on Thinh request 2025-03-25
async def analyze_mock_interview_text(request: Request):
    """
    Analyze mock interview questions and answers for a job seeker, so he/she can prepare the best for the interview.

    openapi:
    ---
    summary: Analyze mock interview questions and answers for a job seeker
    description: This endpoint is used to analyze mock interview questions and answers for a job seeker.
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              question_id:
                type: string
                description: The question ID to analyze
              answer:
                type: string
                description: The answer to analyze (optional if audio_file is provided)
              job_description_id:
                type: string
                description: The job description ID
              audio_file:
                type: string
                format: binary
                description: Audio file containing the answer (optional if answer is provided)
            required:
              - question_id
              - job_description_id
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    # Get question_id and job_description_id from form or JSON
    question_id = request.form.get("question_id") if request.form else request.json.get("question_id", "")
    job_description_id = request.form.get("job_description_id") if request.form else request.json.get("job_description_id", "")
    answer = request.form.get("answer") if request.form else request.json.get("answer", "")

    # Get audio file if provided
    audio_file = request.files.get('audio_file') if request.files else None

    # Call the core analysis function
    result = await analyze_mock_interview_core(question_id, job_description_id, answer, audio_file)
    if result.get('status_code') == 999:
        return json_response(Response(
            error=result["error"],
            message=result["message"],
            data=result["data"]
        ), status_code=422)

    # Return response based on the result
    return json_response(Response(
        error=result["error"],
        message=result["message"],
        data=result["data"]
    ), status_code=result["status_code"])


@openapi.tag("AI")
@openapi.tag("Mock Interview")
@bp.route("/mock-interviews/analyze-with-permission", methods=["POST"], strict_slashes=False)
@require_token
@check_permission([Feature.Code.MOCK_INTERVIEW_AI])
async def analyze_mock_interview_with_permission(request: Request):
    """
    Analyze mock interview questions and answers for a job seeker with permission check.

    openapi:
    ---
    summary: Analyze mock interview questions and answers for a job seeker (requires MOCK_INTERVIEW_AI permission)
    description: This endpoint is used to analyze mock interview questions and answers for a job seeker. It requires the user to have the MOCK_INTERVIEW_AI permission.
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              question_id:
                type: string
                description: The question ID to analyze
              answer:
                type: string
                description: The answer to analyze (optional if audio_file is provided)
              job_description_id:
                type: string
                description: The job description ID
              audio_file:
                type: string
                format: binary
                description: Audio file containing the answer (optional if answer is provided)
            required:
              - question_id
              - job_description_id
    responses:
      200:
        description: Success
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 0
                message:
                  type: string
                  description: Success message
                  example: success
                data:
                  ref: "#/components/schemas/AIResponseSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      403:
        description: Forbidden
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code
                  example: 403
                message:
                  type: string
                  description: Access denied message
                data:
                  type: object
    """
    # Get question_id and job_description_id from form or JSON
    question_id = request.form.get("question_id") if request.form else request.json.get("question_id", "")
    job_description_id = request.form.get("job_description_id") if request.form else request.json.get("job_description_id", "")
    answer = request.form.get("answer") if request.form else request.json.get("answer", "")

    # Get audio file if provided
    audio_file = request.files.get('audio_file') if request.files else None

    # Call the core analysis function
    result = await analyze_mock_interview_core(question_id, job_description_id, answer, audio_file)
    if result.get('status_code') == 999:
        return json_response(Response(
            error=result["error"],
            message=result["message"],
            data={}
        ), status_code=204)

    # Return response based on the result
    return json_response(Response(
        error=result["error"],
        message=result["message"],
        data=result["data"]
    ), status_code=result["status_code"])

