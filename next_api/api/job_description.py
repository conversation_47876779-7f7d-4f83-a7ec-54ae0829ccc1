from uuid import UUID
from datetime import datetime
from typing import List, Optional
from sanic import Blueprint, Request
from pydantic import BaseModel, Field, ConfigDict
from tortoise.exceptions import IntegrityError
from sanic.log import error_logger
from sanic.exceptions import BadRequest
from sanic_ext import openapi
from models.job_description import JobDescription
from repositories.job_description import JobDescriptionRepository, JobDescriptionQuery
from repositories.base import Pagination
from api.base import json_response, Response, ListResponse, ResponseCode, SUCCESS_MESSAGE
from api.middlewares import require_token

bp = Blueprint('job_description')


@openapi.component
class JobDescriptionCreate(BaseModel):
    description: str = Field(..., description="Job description content")
    position: Optional[str] = Field(None, description="Job position")
    company: Optional[str] = Field(None, description="Company name")
    city: Optional[str] = Field(None, description="City location")
    country: Optional[str] = Field(None, description="Country location")
    posted_at: Optional[datetime] = Field(None, description="Date when job was posted")
    salary: Optional[float] = Field(None, description="Job salary")
    link: Optional[str] = Field(None, description="URL to the job posting")
    is_remote: bool = Field(default=False, description="Whether the job is remote")
    source: Optional[str] = Field(None, description="Source of the job posting")
    category: Optional[str] = Field(None, description="Job category")
    keywords: Optional[List[str]] = Field(default=[], description="Keywords related to the job")
    skills: Optional[List[str]] = Field(default=[], description="Required skills")
    status: str = Field(
        default=JobDescription.Status.PRIVATE,
        description="Job status"
    )
    contract_type: Optional[str] = Field(
        None,
        description="Type of contract"
    )
    is_favorite: bool = Field(
        default=False,
        description="Whether job is marked as favorite"
    )
    promotion: str = Field(
        default=JobDescription.PromotionType.NORMAL,
        description="Promotion level"
    )
    mock_interview_id: Optional[UUID] = Field(None, description="Related mock interview ID")
    resume_id: Optional[UUID] = Field(None, description="Related resume ID")
    follow_up_id: Optional[UUID] = Field(None, description="Related follow-up ID")


@openapi.component
class JobDescriptionUpdate(BaseModel):
    description: Optional[str] = Field(None, description="Job description content")
    position: Optional[str] = Field(None, description="Job position")
    company: Optional[str] = Field(None, description="Company name")
    city: Optional[str] = Field(None, description="City location")
    country: Optional[str] = Field(None, description="Country location")
    posted_at: Optional[datetime] = Field(None, description="Date when job was posted")
    salary: Optional[float] = Field(None, description="Job salary")
    link: Optional[str] = Field(None, description="URL to the job posting")
    is_remote: Optional[bool] = Field(None, description="Whether the job is remote")
    source: Optional[str] = Field(None, description="Source of the job posting")
    category: Optional[str] = Field(None, description="Job category")
    keywords: Optional[List[str]] = Field(None, description="Keywords related to the job")
    skills: Optional[List[str]] = Field(None, description="Required skills")
    status: Optional[str] = Field(
        None,
        description="Job status"
    )
    contract_type: Optional[str] = Field(
        None,
        description="Type of contract"
    )
    is_favorite: Optional[bool] = Field(
        None,
        description="Whether job is marked as favorite"
    )
    promotion: Optional[str] = Field(
        None,
        description="Promotion level"
    )
    mock_interview_id: Optional[UUID] = Field(None, description="Related mock interview ID")
    resume_id: Optional[UUID] = Field(None, description="Related resume ID")
    follow_up_id: Optional[UUID] = Field(None, description="Related follow-up ID")


@openapi.component
class JobDescriptionResponse(BaseModel):
    id: UUID
    user_id: UUID
    description: str
    position: Optional[str]
    company: Optional[str]
    city: Optional[str]
    country: Optional[str]
    posted_at: Optional[datetime]
    salary: Optional[float]
    link: Optional[str]
    is_remote: bool
    source: Optional[str]
    category: Optional[str]
    keywords: List[str]
    skills: List[str]
    status: str
    contract_type: Optional[str]
    is_favorite: bool
    promotion: str
    mock_interview_id: Optional[UUID]
    resume_id: Optional[UUID]
    follow_up_id: Optional[UUID]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


@openapi.tag("Job Description")
@bp.route("/", methods=["GET"], strict_slashes=False)
@require_token
async def get_list(request: Request):
    """Get list of job descriptions for the authenticated user

    openapi:
    ---
    parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
        description: Bearer token for authentication
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: page_size
        in: query
        schema:
          type: integer
          default: 10
      - name: position
        in: query
        schema:
          type: string
      - name: company
        in: query
        schema:
          type: string
      - name: status
        in: query
        schema:
          type: string
          enum: [private, posted, denied, in_queue]
      - name: contract_type
        in: query
        schema:
          type: string
          enum: [full_time, part_time, contract, freelance, internship]
      - name: is_favorite
        in: query
        schema:
          type: boolean
      - name: order_by
        in: query
        schema:
          type: array
          items:
            type: string
        description: Fields to sort by (prefix with - for descending)
    responses:
      200:
        description: List of job descriptions
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        $ref: "#/components/schemas/JobDescriptionResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )

    # Add user_id from request context to query parameters
    query_params = {
        **request.args,
        'user_id': request.ctx.user['id']  # Filter by authenticated user
    }
    query = JobDescriptionQuery(**query_params)

    records, total = await JobDescriptionRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@openapi.tag("Job Description")
@bp.route("/<job_id:uuid>", methods=["GET"], strict_slashes=False)
@require_token
async def get_detail(request: Request, job_id: UUID):
    """Get job description details

    openapi:
    ---
    parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
        description: Bearer token for authentication
      - name: job_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      200:
        description: Job description details
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  $ref: "#/components/schemas/JobDescriptionResponse"
      404:
        description: Job description not found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NotFoundResponse"
      403:
        description: Not authorized to access this job description
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
    """
    result = await JobDescriptionRepository.get_one(JobDescriptionQuery(id=job_id))
    if not result:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="job description not found"
        ), 404)

    if str(result.user_id) != request.ctx.user['id']:
        return json_response(Response(
            error=ResponseCode.UNAUTHORIZED,
            message="not authorized to access this job description"
        ), 403)

    body = Response(error=0, message=SUCCESS_MESSAGE, data=dict(result))
    return json_response(body, 200)


@openapi.tag("Job Description")
@bp.route("/", methods=["POST"], strict_slashes=False)
@require_token
async def create(request: Request):
    """Create a new job description

    openapi:
    ---
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/JobDescriptionCreate"
    responses:
      201:
        description: Created job description
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  $ref: "#/components/schemas/JobDescriptionResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        data = JobDescriptionCreate(**request.json)
        result = await JobDescriptionRepository.create_one({
            **data.model_dump(exclude_none=True),
            "user_id": request.ctx.user['id']
        })
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.CREATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot create job description",
            context={"error": ResponseCode.CREATE_FAILED}
        )

    body = Response(error=0, message=SUCCESS_MESSAGE, data=dict(result))
    return json_response(body, 201)


@openapi.tag("Job Description")
@bp.route("/<job_id:uuid>", methods=["PUT"], strict_slashes=False)
@require_token
async def update(request: Request, job_id: UUID):
    """Update a job description

    openapi:
    ---
    parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
        description: Bearer token for authentication
      - name: job_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/JobDescriptionUpdate"
    responses:
      200:
        description: Updated job description
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  $ref: "#/components/schemas/JobDescriptionResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
      403:
        description: Not authorized to update this job description
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Job description not found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NotFoundResponse"
    """
    # Check if job exists and user has permission
    job = await JobDescriptionRepository.get_one(JobDescriptionQuery(id=job_id))
    if not job:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="job description not found"
        ), 404)

    if str(job.user_id) != request.ctx.user['id']:
        return json_response(Response(
            error=ResponseCode.UNAUTHORIZED,
            message="not authorized to update this job description"
        ), 403)

    try:
        data = JobDescriptionUpdate(**request.json)
        affected_rows, record = await JobDescriptionRepository.update(
            job_id,
            data.model_dump(exclude_none=True)
        )
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.UPDATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot update job description",
            context={"error": ResponseCode.UPDATE_FAILED}
        )

    if affected_rows > 0:
        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=dict(record)),
            200
        )
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="cannot update job description"),
        404
    )


@openapi.tag("Job Description")
@bp.route("/<job_id:uuid>", methods=["DELETE"], strict_slashes=False)
@require_token
async def delete(request: Request, job_id: UUID):
    """Delete a job description

    openapi:
    ---
    parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
        description: Bearer token for authentication
      - name: job_id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      200:
        description: Job description deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
      403:
        description: Not authorized to delete this job description
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Job description not found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NotFoundResponse"
    """
    job = await JobDescriptionRepository.get_one(JobDescriptionQuery(id=job_id))
    if not job:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="job description not found"
        ), 404)

    if str(job.user_id) != request.ctx.user['id']:
        return json_response(Response(
            error=ResponseCode.UNAUTHORIZED,
            message="not authorized to delete this job description"
        ), 403)

    try:
        await job.delete()
        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE),
            200
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot delete job description",
            context={"error": ResponseCode.DELETE_FAILED}
        )
