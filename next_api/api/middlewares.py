from functools import wraps
from sanic import Request
from sanic.log import access_logger
from api.base import json_response, Response, ResponseCode
from utils.security.token import JWT
from repositories.user import UserRepository
from repositories.user_feature import UserFeatureRepository
from repositories.token import AccessTokenRepository
from clients.google_auth import GooglePeople
from clients.lemonsqueezy import check_signature


def check_jwt_token(request: Request):
    claims = {}
    if request.token:
        jwt_generator = JWT()
        claims = jwt_generator.decode(request.token)
        access_logger.info("JWT claims: %s", claims)
    return claims


def require_token(wrapped):
    def decorator(f):
        @wraps(f)
        async def decorated_function(request: Request, *args, **kwargs):
            gp: GooglePeople = AccessTokenRepository.get_info_from_google(request.token)
            if gp:
                user = await UserRepository.get_by_email(gp.primary_email)
                if user:
                    request.ctx.user = user
                    request.ctx.at_claims = {}
                    response = await f(request, *args, **kwargs)
                    return response
            else:
                claims = check_jwt_token(request)
                if claims != {}:
                    # Check if token is revoked
                    cached_token = AccessTokenRepository().get_token_from_cache(claims['jti'])
                    if cached_token:
                        return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Invalid token"), 401)
                    user = await UserRepository.get_one(claims['user_id'])
                    if not user:
                        return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Invalid token"), 401)
                    request.ctx.user = user
                    request.ctx.at_claims = claims
                    response = await f(request, *args, **kwargs)
                    return response
            return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Missing or invalid token"), 401)

        return decorated_function

    return decorator(wrapped)


def check_permission(permissions: list):
    """
    Check if the user has the required permissions

    Args:
        permissions (list): List of permissions

    Returns:
        decorator: Decorator for the function

    Example:
        @check_permission([Feature.Code.RESUME_BUILDER])
        async def my_function(request: Request, *args, **kwargs):
            return json_response(Response(message="Hello, world!"), 200)
    """
    def decorator(f):
        @wraps(f)
        async def decorated_function(request: Request, *args, **kwargs):
            gp: GooglePeople = AccessTokenRepository.get_info_from_google(request.token)
            if gp:
                user = await UserRepository.get_by_email(gp.primary_email)
                if user:
                    request.ctx.user = user
                    request.ctx.at_claims = {}
                    response = await f(request, *args, **kwargs)
                    return response
            else:
                claims = check_jwt_token(request)
                if claims != {}:
                    # Check if token is revoked
                    cached_token = AccessTokenRepository().get_token_from_cache(claims['jti'])
                    if cached_token:
                        return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Invalid token"), 401)
                    user = await UserRepository.get_one(claims['user_id'])
                    if not user:
                        return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Invalid token"), 401)
                    features = await UserFeatureRepository.get_all_features(user)
                    if not features:
                        return json_response(Response(error=ResponseCode.PERMISSION_DENIED, message="Permission denied"), 401)
                    if not all(feature in features for feature in permissions):
                        return json_response(Response(error=ResponseCode.PERMISSION_DENIED, message="Permission denied"), 401)
                    request.ctx.user = user
                    request.ctx.at_claims = claims
                    response = await f(request, *args, **kwargs)
                    return response
            return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Missing or invalid token"), 401)
        return decorated_function
    return decorator


def check_lemon_sign(wrapped):
    def decorator(f):
        @wraps(f)
        async def decorated_function(request: Request, *args, **kwargs):
            check_result = check_signature(request.headers.get("X-Signature"), request.body)
            if not check_result:
                return json_response(Response(error=ResponseCode.UNAUTHORIZED, message="Invalid signature"), 401)
            return await f(request, *args, **kwargs)
        return decorated_function
    return decorator(wrapped)
