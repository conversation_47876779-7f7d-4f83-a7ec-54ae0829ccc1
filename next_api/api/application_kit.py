from typing import Optional
from datetime import datetime
from pydantic import BaseModel, <PERSON>
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.application_kit import ApplicationKitRepository, ApplicationKitQuery

bp = Blueprint("application_kit")


@openapi.component
class RequestApplicationKitSchema(BaseModel):
    """Schema for application kit creation/update requests"""
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    jd_id: str = Field(..., description="UUID of job description",
                       example="123e4567-e89b-12d3-a456-************")
    resume_id: Optional[str] = Field(None, description="UUID of resume",
                                     example="123e4567-e89b-12d3-a456-************")
    cover_letter_id: Optional[str] = Field(None, description="UUID of cover letter",
                                           example="123e4567-e89b-12d3-a456-************")
    follow_up_id: Optional[str] = Field(None, description="UUID of follow-up letter",
                                        example="123e4567-e89b-12d3-a456-************")
    mock_interview_id: Optional[str] = Field(None, description="UUID of mock interview",
                                             example="123e4567-e89b-12d3-a456-************")
    job_insight_id: Optional[str] = Field(None, description="UUID of job insights",
                                          example="123e4567-e89b-12d3-a456-************")
    status: Optional[int] = Field(0, description="Status of the application kit",
                                  example=0)


@openapi.component
class ResponseApplicationKitSchema(BaseModel):
    """Schema for application kit responses"""
    id: str = Field(..., description="UUID of application kit",
                    example="123e4567-e89b-12d3-a456-************")
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    jd_id: str = Field(..., description="UUID of job description",
                       example="123e4567-e89b-12d3-a456-************")
    resume_id: Optional[str] = Field(None, description="UUID of resume",
                                     example="123e4567-e89b-12d3-a456-************")
    cover_letter_id: Optional[str] = Field(None, description="UUID of cover letter",
                                           example="123e4567-e89b-12d3-a456-************")
    follow_up_id: Optional[str] = Field(None, description="UUID of follow-up letter",
                                        example="123e4567-e89b-12d3-a456-************")
    mock_interview_id: Optional[str] = Field(None, description="UUID of mock interview",
                                             example="123e4567-e89b-12d3-a456-************")
    job_insight_id: Optional[str] = Field(None, description="UUID of job insights",
                                          example="123e4567-e89b-12d3-a456-************")
    status: int = Field(..., description="Status of the application kit",
                        example=0)
    created_at: datetime = Field(..., description="Created at",
                                 example="2024-01-01T00:00:00")
    updated_at: datetime = Field(..., description="Updated at",
                                 example="2024-01-01T00:00:00")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Application Kit")
@require_token
async def get_list(request: Request):
    """Get a paginated list of application kits.

    openapi:
    ---
    summary: Get a list of application kits
    description: This endpoint is used to get application kit list.
    parameters:
    - name: Authorization
      in: header
      schema:
        type: string
        format: Bearer <JWT token>
      description: Access token
      required: true
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Number of records per page
      example: 10
    - name: user_id
      in: query
      schema:
        type: string
        format: uuid
      description: User ID
    - name: jd_id
      in: query
      schema:
        type: string
        format: uuid
      description: Job Description ID
    - name: resume_id
      in: query
      schema:
        type: string
        format: uuid
      description: Resume ID
    - name: cover_letter_id
      in: query
      schema:
        type: string
        format: uuid
      description: Cover Letter ID
    - name: follow_up_id
      in: query
      schema:
        type: string
        format: uuid
      description: Follow-up Letter ID
    - name: mock_interview_id
      in: query
      schema:
        type: string
        format: uuid
      description: Mock Interview ID
    - name: job_insight_id
      in: query
      schema:
        type: string
        format: uuid
      description: Job Insights ID
    - name: status
      in: query
      schema:
        type: integer
      description: Status of the application kit
    - name: status__in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: List of statuses to filter by
    - name: status__not_in
      in: query
      schema:
        type: array
        items:
          type: integer
      description: List of statuses to exclude
    - name: status__gte
      in: query
      schema:
        type: integer
      description: Status greater than or equal to
    - name: status__lte
      in: query
      schema:
        type: integer
      description: Status less than or equal to
    - name: order_by
      in: query
      schema:
        type: array
        items:
          type: string
          enum: ["id", "-id", "created_at", "-created_at", "updated_at", "-updated_at", "status", "-status"]
      description: Order by
    responses:
      200:
        description: Successfully retrieved application kits
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        ref: "#/components/schemas/ResponseApplicationKitSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = ApplicationKitQuery(**request.args)
    records, total = await ApplicationKitRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Application Kit")
@require_token
async def create(request: Request):
    """Create a new application kit.

    openapi:
    ---
    summary: Create a new application kit
    description: This endpoint is used to create a new application kit.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestApplicationKitSchema"
    responses:
      201:
        description: Successfully created application kit
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseApplicationKitSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        record = await ApplicationKitRepository.create_one(request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot create application kit",
                         context={"error": ResponseCode.CREATE_FAILED})

    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<kit_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("Application Kit")
@require_token
async def update(request: Request, kit_id: str):
    """Update an existing application kit.

    openapi:
    ---
    summary: Update an existing application kit
    description: This endpoint is used to update an existing application kit.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: kit_id
      in: path
      type: string
      required: true
      description: Application Kit ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestApplicationKitSchema"
    responses:
      200:
        description: Successfully updated application kit
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseApplicationKitSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Application kit not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    try:
        affected_rows, record = await ApplicationKitRepository.update(kit_id, request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot update application kit")

    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="Cannot update application kit"),
        404
    )


@bp.route("/<kit_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Application Kit")
@require_token
async def get_one(_: Request, kit_id: str):
    """Get a single application kit by ID.

    openapi:
    ---
    summary: Get a single application kit
    description: This endpoint is used to get a single application kit by ID.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: kit_id
      in: path
      type: string
      required: true
      description: Application Kit ID
    responses:
      200:
        description: Successfully retrieved application kit
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseApplicationKitSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Application kit not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    record = await ApplicationKitRepository.get_one(ApplicationKitQuery(id=kit_id))

    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.GET_ONE_FAILED,
                 message="Application kit not found"),
        404
    )
