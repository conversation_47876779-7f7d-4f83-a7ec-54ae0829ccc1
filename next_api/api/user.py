from decimal import DecimalException, InvalidOperation
from datetime import datetime
from uuid import UUID
from typing import Union, Optional
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode, \
    OPENAPI_BAD_REQUEST_RESPONSE, OPENAPI_NOT_FOUND_RESPONSE, OPENAPI_UNAUTHORIZED_RESPONSE
from repositories.base import Pagination
from repositories.user import UserRepository, UserQuery
from repositories.user_subscription import UserSubscriptionRepository, UserSubscriptionQuery
from api.middlewares import require_token
from repositories.user_feature import UserFeatureRepository
from utils.subscription import is_premium_user

bp = Blueprint("user")


@openapi.component
class ResponseUserSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of user",
                    example="123e4567-e89b-12d3-a456-************")
    email: str = Field(..., format="email",
                       description="Email address, must be unique", example="<EMAIL>")
    fullname: str = Field("", description="Full name", example="John Doe")
    picture: str = Field("", description="Picture URL",
                         example="https://www.example.com/picture.jpg")
    min_salary: float = Field(
        None, description="Minimum salary", example=1000.0)
    expected_salary: float = Field(
        None, description="Expected salary", example=1200.0)
    exp_level: int = Field(...,
                           description="Experience level of a user", example=3)
    linked_in: str = Field(..., description="LinkedIn URL",
                           example="https://www.linkedin.com/in/username")
    locale: str = Field(..., description="Locale", example="en-US")
    two_factor_enabled: bool = Field(...,
                                     description="Two factor enabled", example=False)
    provider: str = Field(..., description="Provider", example="email")
    status: int = Field(..., description="User status", example=1)
    created_at: datetime = Field(..., description="Created at")
    updated_at: datetime = Field(..., description="Updated at")
    auth0_id: Optional[str] = Field(None, description="Auth0 ID", example="auth0|123456789")


@openapi.component
class RequestUserSchema(BaseModel):
    email: str = Field(None, format="email",
                       description="Email address, must be unique", example="<EMAIL>")
    fullname: str = Field("", description="Full name", example="John Doe")
    picture: str = Field(None, description="Picture URL",
                         example="https://www.example.com/picture.jpg")
    min_salary: float = Field(
        None, description="Minimum salary", example=1000.0)
    expected_salary: float = Field(
        None, description="Expected salary", example=1200.0)
    exp_level: int = Field(
        None, description="Experience level of a user", example=3)
    linked_in: str = Field(None, description="LinkedIn URL",
                           example="https://www.linkedin.com/in/username")
    locale: str = Field(None, description="Locale", example="en-US")
    two_factor_enabled: bool = Field(
        None, description="Two factor enabled", example=False)
    provider: str = Field(None, description="Provider", example="email")
    status: int = Field(None, description="User status", example=1)
    auth0_id: Optional[str] = Field(None, description="Auth0 ID", example="auth0|123456789")


@openapi.component
class RequestSubscriptionSchema(BaseModel):
    plan_id: UUID = Field(
        ...,
        description="ID of the subscription plan to subscribe to",
        example="123e4567-e89b-12d3-a456-************"
    )
    start_date: Optional[datetime] = Field(
        None,
        description="Start date of subscription. If not provided, current time will be used",
        example="2024-03-15T00:00:00Z"
    )


@openapi.component
class ResponseSubscriptionFeatureSchema(BaseModel):
    id: UUID = Field(..., description="ID of the user feature")
    user_id: UUID = Field(..., description="ID of the user")
    feature_id: UUID = Field(..., description="ID of the feature")
    feature_value: Optional[dict] = Field(None, description="Value configuration for the feature")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


@openapi.component
class ResponseSubscriptionPlanSchema(BaseModel):
    """Schema for subscription plan data in subscription response"""
    id: UUID = Field(..., description="ID of the subscription plan")
    name: str = Field(..., description="Name of the subscription plan")
    description: str = Field(..., description="Description of the subscription plan")
    billing_cycle: int = Field(..., description="Billing cycle of the plan (1=Monthly, 2=Quarterly, etc)")
    is_auto_renewable: bool = Field(..., description="Whether the plan auto-renews")


@openapi.component
class ResponseSubscriptionsOfUserSchema(BaseModel):
    """Schema for user subscription response"""
    id: UUID = Field(
        ...,
        description="ID of the subscription",
        example="06786845-5c51-7143-8000-9558b5fc7f98"
    )
    user_id: UUID = Field(
        ...,
        description="ID of the user",
        example="067789f9-d0ff-70d7-8000-cc471a4b4d8c"
    )
    subscription_plan_id: UUID = Field(
        ...,
        description="ID of the subscription plan",
        example="06783db7-f64d-79e8-8000-23f1f24906fd"
    )
    status: int = Field(
        ...,
        description="Subscription status",
        example=1
    )
    start_date: datetime = Field(
        ...,
        description="Start date of subscription",
        example="2025-01-14T22:35:49.767105+00:00"
    )
    end_date: Optional[datetime] = Field(
        None,
        description="End date of subscription",
        example="2025-02-14T22:35:49.767105+00:00"
    )
    last_billing_date: Optional[datetime] = Field(
        None,
        description="Last billing date",
        example=None
    )
    next_billing_date: Optional[datetime] = Field(
        None,
        description="Next billing date",
        example="2025-02-14T22:35:49.767105+00:00"
    )
    created_at: datetime = Field(
        ...,
        description="Creation timestamp",
        example="2025-01-14T15:35:49.769864+00:00"
    )
    updated_at: datetime = Field(
        ...,
        description="Last update timestamp",
        example="2025-01-14T15:35:49.769869+00:00"
    )
    subscription_plan__id: UUID = Field(
        ...,
        description="ID of the subscription plan (denormalized)",
        example="06783db7-f64d-79e8-8000-23f1f24906fd"
    )
    subscription_plan__name: str = Field(
        ...,
        description="Name of the subscription plan",
        example="Monthly Plan"
    )
    subscription_plan__description: str = Field(
        ...,
        description="Description of the subscription plan",
        example="Access to all features with monthly billing"
    )
    subscription_plan__billing_cycle: int = Field(
        ...,
        description="Billing cycle of the plan",
        example=1
    )
    subscription_plan__is_auto_renewable: bool = Field(
        ...,
        description="Whether the plan auto-renews",
        example=True
    )


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("User")
@openapi.definition(
    summary="Get a list of users",
    description="This endpoint is used to get user list.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string",
            description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "fullname__icontains", str, location="query",
            description="Full name contains, search case insensitive", required=False),
        openapi.definitions.Parameter(
            "expected_salary__gte", float, location="query",
            description="Expected salary greater than or equal", required=False),
        openapi.definitions.Parameter(
            "min_salary__gt", float, location="query",
            description="Minimum salary greater than", required=False),
        openapi.definitions.Parameter(
            "min_salary__gte", float, location="query",
            description="Minimum salary greater than or equal", required=False),
        openapi.definitions.Parameter(
            "min_salary__lt", float, location="query",
            description="Minimum salary less than", required=False),
        openapi.definitions.Parameter(
            "min_salary__lte", float, location="query",
            description="Minimum salary less than or equal", required=False),
        openapi.definitions.Parameter(
            "expected_salary__gt", float, location="query",
            description="Expected salary greater than", required=False),
        openapi.definitions.Parameter(
            "expected_salary__lt", float, location="query",
            description="Expected salary less than", required=False),
        openapi.definitions.Parameter(
            "expected_salary__lte", float, location="query",
            description="Expected salary less than or equal", required=False),
        openapi.definitions.Parameter(
            "exp_level__in", Union[list[int], int], location="query",
            description="Experience level list", required=False),
        openapi.definitions.Parameter(
            "exp_level__not_in", Union[list[int], int], location="query",
            description="Experience level list", required=False),
        openapi.definitions.Parameter(
            "linked_in", str, location="query",
            description="LinkedIn URL", required=False),
        openapi.definitions.Parameter(
            "locale", str, location="query",
            description="Locale", required=False),
        openapi.definitions.Parameter(
            "two_factor_enabled", bool, location="query",
            description="Two factor enabled", required=False),
        openapi.definitions.Parameter(
            "provider", str, location="query",
            description="Provider", required=False),
        openapi.definitions.Parameter(
            "email__contains", str, location="query",
            description="Email contains", required=False),
        openapi.definitions.Parameter(
            "email__icontains", str, location="query",
            description="Email contains (case insensitive)", required=False),
        openapi.definitions.Parameter(
            "email__startswith", str, location="query",
            description="Email starts with", required=False),
        openapi.definitions.Parameter(
            "email__endswith", str, location="query",
            description="Email ends with", required=False),
        openapi.definitions.Parameter(
            "status__in", Union[list[int], int], location="query",
            description="Status of user", required=False),
        openapi.definitions.Parameter(
            "status__not_in", Union[list[int], int], location="query",
            description="Status of user", required=False),
        openapi.definitions.Parameter(
            "created_at__gt", datetime, location="query",
            description="Created at greater than", required=False),
        openapi.definitions.Parameter(
            "created_at__gte", datetime, location="query",
            description="Created at greater than or equal", required=False),
        openapi.definitions.Parameter(
            "created_at__lt", datetime, location="query",
            description="Created at less than", required=False),
        openapi.definitions.Parameter(
            "created_at__lte", datetime, location="query",
            description="Created at less than or equal", required=False),
        openapi.definitions.Parameter(
            "updated_at__gt", datetime, location="query",
            description="Updated at greater than", required=False),
        openapi.definitions.Parameter(
            "updated_at__gte", datetime, location="query",
            description="Updated at greater than or equal", required=False),
        openapi.definitions.Parameter(
            "updated_at__lt", datetime, location="query",
            description="Updated at less than", required=False),
        openapi.definitions.Parameter(
            "updated_at__lte", datetime, location="query",
            description="Updated at less than or equal", required=False),
        openapi.definitions.Parameter(
            "order_by", str, location="query",
            description="Order by. Example: `created_at,-updated_at`", required=False),
        openapi.definitions.Parameter(
            "auth0_id", str, location="query",
            description="Auth0 ID", required=False),
        openapi.definitions.Parameter(
            "auth0_id__contains", str, location="query",
            description="Auth0 ID contains", required=False),
        openapi.definitions.Parameter(
            "auth0_id__icontains", str, location="query",
            description="Auth0 ID contains (case insensitive)", required=False),
        openapi.definitions.Parameter(
            "auth0_id__startswith", str, location="query",
            description="Auth0 ID starts with", required=False),
        openapi.definitions.Parameter(
            "auth0_id__endswith", str, location="query",
            description="Auth0 ID ends with", required=False),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved list of users",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {
                                "type": "integer",
                                "description": "Error code, 0 means success"
                            },
                            "message": {
                                "type": "string",
                                "description": "Response message"
                            },
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {
                                        "type": "integer",
                                        "description": "Total number of records"
                                    },
                                    "page": {
                                        "type": "integer",
                                        "description": "Current page number"
                                    },
                                    "page_size": {
                                        "type": "integer",
                                        "description": "Number of records per page"
                                    },
                                    "records": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "$ref": "#/components/schemas/ResponseUserSchema"
                                        }
                                    }
                                }
                            }
                        },
                        "required": ["error", "message", "data"]
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def get_list(request):
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = UserQuery(**request.args)
    records, total = await UserRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("User")
@openapi.definition(
    summary="Create a new user",
    description="This endpoint is used to create a new user.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
    ],
    body=openapi.definitions.RequestBody(
        description="User data",
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "$ref": "#/components/schemas/RequestUserSchema",
                    "required": ["email"]
                },
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=201, description="Successfully created a new user",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseUserSchema",
                            }
                        }
                    }
                }
            },
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@require_token
async def create(request):
    try:
        record = await UserRepository.create_one(request.json)
    except (DecimalException, InvalidOperation) as e:
        error_logger.exception(e)
        raise BadRequest(message="cannot create user")
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<user_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("User")
@openapi.definition(
    summary="Update a user",
    description="This endpoint is used to update a user.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "user_id", str, location="path", description="User ID", required=True),
    ],
    body=openapi.definitions.RequestBody(
        description="User data",
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "$ref": "#/components/schemas/RequestUserSchema",
                },
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully updated a user",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseUserSchema",
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@require_token
async def update(request: Request, user_id: str):
    try:
        affected_rows, record = await UserRepository.update(user_id, request.json)
    except (DecimalException, InvalidOperation) as e:
        error_logger.exception(e)
        raise BadRequest(message="cannot update user")
    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=dict(record)), 200)
    return json_response(Response(error=ResponseCode.UPDATE_FAILED, message="cannot update user"), 404)


@bp.route("/<user_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("User")
@openapi.definition(
    summary="Get a user",
    description="This endpoint is used to get a user.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "user_id", str, location="path", description="User ID", required=True),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a user",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseUserSchema",
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@require_token
async def get_one(_: Request, user_id: str):
    record = await UserRepository.get_one(user_id)
    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=dict(record)), 200)
    return json_response(Response(error=ResponseCode.GET_ONE_FAILED, message="user not found"), 404)


@openapi.tag("User")
@openapi.tag("Feature")
@bp.get('/<user_id:str>/features')
@require_token
async def get_user_features(request: Request, user_id: str):  # pylint: disable=unused-argument
    """Get all features available for a specific user.

    openapi:
    ---
    summary: Get user features
    description: Get all features and their values available for a specific user based on their subscription plan.
    parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
          format: Bearer <JWT token>
        description: Authorization header
      - name: user_id
        in: path
        required: true
        description: ID of the user to get features for
        schema:
          type: string
    responses:
      200:
        description: Successfully retrieved user features
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code, 0 means success
                  example: 0
                message:
                  type: string
                  description: Response message
                  example: "Success"
                data:
                  type: object
                  additionalProperties:
                    type: object
                  example:
                    RESUME_BUILDER:
                      max_resumes: 10
                      allow_download: true
                      allow_share: true
                    RESUME_AI_BUILDER:
                      max_ai_generations: 10
                      max_words_per_generation: 500
                      allow_custom_prompts: true
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: User not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
      500:
        description: Internal server error
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 1
                message:
                  type: string
                  example: "Failed to get user features"
    """
    try:
        # Get user first to ensure they exist
        user = await UserRepository.get_one(user_id)
        if not user:
            return json_response(Response(
                error=ResponseCode.GET_ONE_FAILED,
                message="User not found"
            ), 404)

        features = await UserFeatureRepository.get_all_features(user)
        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=features
        ), 200)
    except Exception as e:
        error_logger.exception("Failed to get user features: %s", str(e))
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="Failed to get user features"
        ), 500)


@bp.route("/<user_id:str>/subscriptions", methods=["GET"], strict_slashes=False)
@openapi.tag("User")
@openapi.tag("Subscription")
@require_token
async def get_user_subscriptions(request: Request, user_id: str):
    """Get all subscriptions of a user with optional status filtering and pagination

    openapi:
    ---
    summary: Get list of user subscriptions
    description: This endpoint is used to get list of subscriptions for a specific user.
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: user_id
      in: path
      required: true
      description: ID of user
      schema:
        type: string
    - name: status
      in: query
      description: Filter by subscription status (0=Unknown, 1=Active, 2=Inactive, 3=Cancelled, 4=Expired)
      schema:
        type: integer
        enum: [0, 1, 2, 3, 4]
    - name: status__in
      in: query
      description: Filter by multiple subscription statuses (comma-separated)
      schema:
        type: string
        example: "1,2,3"
    - name: page
      in: query
      description: Page number
      schema:
        type: integer
        default: 1
        example: 1
    - name: page_size
      in: query
      description: Page size
      schema:
        type: integer
        default: 10
        example: 10
    - name: order_by
      in: query
      description: Order by field. Prefix with - for descending order
      schema:
        type: string
        example: "-created_at"
    responses:
      200:
        description: Successfully retrieved subscriptions
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array      
                      items:
                        ref: "#/components/schemas/ResponseSubscriptionsOfUserSchema"
                  required: ["total", "page", "page_size", "records"]
              required: ["error", "message", "data"]
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: User not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
    """
    try:
        # Verify user exists
        user = await UserRepository.get_one(user_id)
        if not user:
            return json_response(Response(
                error=ResponseCode.GET_ONE_FAILED,
                message="User not found"
            ), 404)

        # Parse query parameters
        pagination = Pagination(
            page=int(request.args.get('page', 1)),
            page_size=int(request.args.get('page_size', 10))
        )

        # Create query object
        query = UserSubscriptionQuery(
            user_id=user_id,
            status=request.args.get('status'),
            status__in=request.args.get('status__in'),
            order_by=request.args.get(
                'order_by', '-created_at').split(',') if request.args.get('order_by') else ['-created_at']
        )

        # Get subscriptions with pagination
        subscriptions, total = await UserSubscriptionRepository.get_list(
            q=query,
            p=pagination,
            prefetch=True
        )

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data=ListResponse(
                total=total,
                page=pagination.page,
                page_size=pagination.page_size,
                records=subscriptions
            )
        ), 200)
    except ValueError as e:
        error_logger.exception("Invalid parameter: %s", str(e))
        raise BadRequest(
            message=f"Invalid parameter: {str(e)}",
            context={"error": ResponseCode.CLIENT_BAD_REQUEST}
        )
    except Exception as e:
        error_logger.exception("Failed to get user subscriptions: %s", str(e))
        raise BadRequest(
            message="Failed to get user subscriptions",
            context={"error": ResponseCode.GET_LIST_FAILED}
        )


@openapi.tag("User")
@openapi.tag("Subscription")
@bp.get('/<user_id:str>/is-premium')
@require_token
async def check_user_premium_status(request: Request, user_id: str):
    """Check if a user has premium status based on their subscriptions.

    openapi:
    ---
    summary: Check premium status
    description: |
      Determines if a user has an active premium subscription.
      Premium plans include premium_monthly, premium_yearly_59, education_monthly, and education_yearly.
    parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
          format: Bearer <JWT token>
        description: Authorization header
      - name: user_id
        in: path
        required: true
        description: ID of the user to check premium status for
        schema:
          type: string
    responses:
      200:
        description: Successfully checked premium status
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  description: Error code, 0 means success
                  example: 0
                message:
                  type: string
                  description: Response message
                  example: "Success"
                data:
                  type: object
                  properties:
                    is_premium:
                      type: boolean
                      description: Whether the user has premium status
                      example: true
                    user_id:
                      type: string
                      description: ID of the checked user
                      example: "123e4567-e89b-12d3-a456-************"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: User not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
      500:
        description: Internal server error
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 1
                message:
                  type: string
                  example: "Failed to check premium status"
    """
    try:
        # Verify the user exists
        user = await UserRepository.get_one(user_id)
        if not user:
            return json_response(Response(
                error=ResponseCode.GET_ONE_FAILED,
                message="User not found"
            ), 404)

        # Check if the user has premium status
        is_premium = await is_premium_user(user_id)

        return json_response(Response(
            error=ResponseCode.SUCCESS,
            message=SUCCESS_MESSAGE,
            data={
                "is_premium": is_premium,
                "user_id": user_id
            }
        ), 200)
    except Exception as e:
        error_logger.exception("Failed to check premium status: %s", str(e))
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="Failed to check premium status"
        ), 500)
