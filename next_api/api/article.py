from typing import List, Optional
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from tortoise.exceptions import IntegrityError
from sanic import Blueprint, Request
from sanic.log import error_logger
from sanic.exceptions import BadRequest
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, ResponseCode, SUCCESS_MESSAGE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.article import ArticleRepository, ArticleQuery, ArticleLikeRepository

bp = Blueprint("article")


@openapi.component
class ArticleCreate(BaseModel):
    title: str = Field(..., description="Title of the article")
    content: str = Field(..., description="Content of the article")
    banner_image_url: Optional[str] = Field(None, description="URL of the banner image")
    author: str = Field(..., description="Author of the article")
    published_at: Optional[datetime] = Field(None, description="Publication date")
    tags: List[str] = Field(default=[], description="List of tags")


@openapi.component
class ArticleUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    banner_image_url: Optional[str] = None
    author: Optional[str] = None
    published_at: Optional[datetime] = None
    tags: Optional[List[str]] = None


@openapi.component
class ArticleResponse(BaseModel):
    id: UUID
    title: str
    content: str
    banner_image_url: Optional[str]
    author: str
    published_at: Optional[datetime]
    likes_count: int
    tags: List[str]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


@openapi.tag("Article")
@bp.route("/", methods=["GET"], strict_slashes=False)
@require_token
async def get_list(request: Request):
    """Get list of articles

    openapi:
    ---
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: page_size
        in: query
        schema:
          type: integer
          default: 10
      - name: title
        in: query
        schema:
          type: string
      - name: title__icontains
        in: query
        schema:
          type: string
      - name: author
        in: query
        schema:
          type: string
      - name: author__icontains
        in: query
        schema:
          type: string
      - name: published_at__gte
        in: query
        schema:
          type: string
          format: date-time
      - name: published_at__lte
        in: query
        schema:
          type: string
          format: date-time
      - name: tags__contains
        in: query
        schema:
          type: array
          items:
            type: string
      - name: order_by
        in: query
        schema:
          type: array
          items:
            type: string
        description: Fields to sort by (prefix with - for descending)
    responses:
      200:
        description: List of articles
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        ref: "#/components/schemas/ArticleResponse"
      400:
        description: Bad request
      401:
        description: Unauthorized
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = ArticleQuery(**request.args)
    records, total = await ArticleRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@openapi.tag("Article")
@bp.route("/<article_id:str>", methods=["GET"], strict_slashes=False)
@require_token
async def get_detail(request: Request, article_id: str):
    """Get article details

    openapi:
    ---
    parameters:
      - name: article_id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: Article details
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/ArticleResponse"
      400:
        description: Bad request
      401:
        description: Unauthorized
      404:
        description: Not found
    """
    query = ArticleQuery(id=article_id)
    result = await ArticleRepository.get_one(query)
    if not result:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="article not found"
        ), 404)

    body = Response(error=0, message=SUCCESS_MESSAGE, data=result)
    return json_response(body, 200)


@openapi.tag("Article")
@bp.route("/", methods=["POST"], strict_slashes=False)
@require_token
async def create(request: Request):
    """Create a new article

    openapi:
    ---
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/ArticleCreate"
    responses:
      201:
        description: Created article
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/ArticleResponse"
      400:
        description: Bad request
      401:
        description: Unauthorized
    """
    try:
        result = await ArticleRepository.create_one(request.json)
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.CREATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot create article",
            context={"error": ResponseCode.CREATE_FAILED}
        )

    body = Response(error=0, message=SUCCESS_MESSAGE, data=result)
    return json_response(body, 201)


@openapi.tag("Article")
@bp.route("/<article_id:str>", methods=["PATCH"], strict_slashes=False)
@require_token
async def update(request: Request, article_id: str):
    """Update an article

    openapi:
    ---
    parameters:
      - name: article_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/ArticleUpdate"
    responses:
      200:
        description: Updated article
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/ArticleResponse"
      400:
        description: Bad request
      401:
        description: Unauthorized
      404:
        description: Not found
    """
    try:
        affected_rows, record = await ArticleRepository.update(article_id, request.json)
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.UPDATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot update article",
            context={"error": ResponseCode.UPDATE_FAILED}
        )

    if affected_rows > 0:
        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=record),
            200
        )
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="cannot update article"),
        404
    )


@openapi.tag("Article")
@bp.route("/<article_id:str>/like", methods=["POST"], strict_slashes=False)
@require_token
async def like_article(request: Request, article_id: str):
    """Like an article

    openapi:
    ---
    parameters:
      - name: article_id
        in: path
        required: true
        schema:
          type: string
    responses:
      201:
        description: Article liked successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
      400:
        description: Bad request
      401:
        description: Unauthorized
      404:
        description: Article not found
    """
    try:
        # Check if article exists
        query = ArticleQuery(id=article_id)
        article = await ArticleRepository.get_one(query)
        if not article:
            return json_response(Response(
                error=ResponseCode.GET_ONE_FAILED,
                message="article not found"
            ), 404)

        # Create like record
        like_data = {
            "article_id": article_id,
            "user_id": request.ctx.user.get('id')
        }
        await ArticleLikeRepository.create_one(like_data)

        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE),
            201
        )
    except IntegrityError:
        # User has already liked the article
        return json_response(Response(
            error=ResponseCode.CREATE_FAILED,
            message="article already liked"
        ), 400)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot like article",
            context={"error": ResponseCode.CREATE_FAILED}
        )


@openapi.tag("Article")
@bp.route("/<article_id:str>/unlike", methods=["POST"], strict_slashes=False)
@require_token
async def unlike_article(request: Request, article_id: str):
    """Unlike an article

    openapi:
    ---
    parameters:
      - name: article_id
        in: path
        required: true
        schema:
          type: string
    responses:
      200:
        description: Article unliked successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
      400:
        description: Bad request
      401:
        description: Unauthorized
      404:
        description: Article not found or not liked
    """
    try:
        deleted = await ArticleLikeRepository.delete(
            article_id=article_id,
            user_id=request.ctx.user.get('id')
        )
        if deleted:
            return json_response(
                Response(error=0, message=SUCCESS_MESSAGE),
                200
            )
        return json_response(Response(
            error=ResponseCode.DELETE_FAILED,
            message="article not liked"
        ), 404)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot unlike article",
            context={"error": ResponseCode.DELETE_FAILED}
        )
