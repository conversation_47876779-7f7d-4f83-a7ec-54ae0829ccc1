import os
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict
from tortoise.exceptions import IntegrityError
from sanic import Blueprint, Request
from sanic.log import error_logger
from sanic.exceptions import BadRequest
from sanic_ext import openapi
from polar_sdk import Checkout
from api.base import json_response, Response, ListResponse, ResponseCode, SUCCESS_MESSAGE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.user import UserRepository
from repositories.subscription_plan import SubscriptionPlanRepository, SubscriptionPlanQuery
from models.subscription_plan import SubscriptionPlan
from clients.lemonsqueezy import checkout as lemonsqueezy_checkout
from clients.polar_payment import checkout as polar_checkout

bp = Blueprint("subscription_plan")


@openapi.component
class SubscriptionPlanCreate(BaseModel):
    code: str = Field(..., description="Unique code for the plan")
    name: str = Field(..., description="Name of the plan")
    description: Optional[str] = Field(None, description="Description of the plan")
    price: Decimal = Field(..., description="Price of the plan")
    currency: str = Field("USD", description="Currency code")
    billing_cycle: int = Field(
        SubscriptionPlan.BillingCycle.MONTHLY,
        description="Billing cycle (1: Monthly, 3: Quarterly, etc.)"
    )
    is_auto_renewable: bool = Field(False, description="Whether plan auto-renews")
    trial_period_days: int = Field(0, description="Number of trial days")
    sort_order: int = Field(0, description="Display order of the plan")
    is_public: bool = Field(True, description="Whether plan is publicly visible")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional plan metadata")
    status: int = Field(
        SubscriptionPlan.Status.INACTIVE,
        description="Plan status (0: Inactive, 1: Active, 2: Deprecated)"
    )
    lemon_variant_id: Optional[int] = Field(None, description="Lemon variant ID")
    polar_product_id: Optional[str] = Field(None, description="Polar product ID")


@openapi.component
class SubscriptionPlanUpdate(BaseModel):
    code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    currency: Optional[str] = None
    billing_cycle: Optional[int] = None
    is_auto_renewable: Optional[bool] = None
    trial_period_days: Optional[int] = None
    sort_order: Optional[int] = None
    is_public: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None
    status: Optional[int] = None
    lemon_variant_id: Optional[int] = None
    polar_product_id: Optional[str] = None


@openapi.component
class SubscriptionPlanResponse(BaseModel):
    id: UUID
    code: str
    name: str
    description: Optional[str]
    price: Decimal
    currency: str
    billing_cycle: int
    is_auto_renewable: bool
    trial_period_days: int
    sort_order: int
    is_public: bool
    metadata: Optional[Dict[str, Any]]
    status: int
    lemon_variant_id: Optional[int]
    polar_product_id: Optional[str]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


@openapi.component
class RequestMapFeaturesSchema(BaseModel):
    """Request model for mapping features to a subscription plan."""
    features: Dict[str, Optional[Any]] = Field(
        ...,
        description="Dictionary of feature codes mapped to their values. Use null for default value."
    )


@openapi.component
class ResponseMapFeaturesSchema(BaseModel):
    """Response model for mapped features in a subscription plan."""
    id: UUID = Field(..., description="Feature ID")
    code: str = Field(..., description="Feature code")
    name: str = Field(..., description="Feature name")
    description: Optional[str] = Field(None, description="Feature description")
    value: Optional[Dict[str, Any]] = Field(None, description="Feature value configuration")
    mapping_id: UUID = Field(..., description="ID of the feature mapping")

    model_config = ConfigDict(from_attributes=True)


@openapi.component
class ResponseMapFeaturesPlanSchema(BaseModel):
    """Response model for a subscription plan with mapped features."""
    id: UUID
    code: str
    name: str
    description: Optional[str]
    price: Decimal
    currency: str
    billing_cycle: int
    is_auto_renewable: bool
    trial_period_days: int
    sort_order: int
    is_public: bool
    metadata: Optional[Dict[str, Any]]
    status: int
    lemon_variant_id: Optional[int]
    polar_product_id: Optional[str]
    features: List[ResponseMapFeaturesSchema]
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


@openapi.component
class RequestCheckoutSchema(BaseModel):
    user_id: Optional[str] = None
    redirect_url: Optional[str] = None


@openapi.component
class CheckoutResponse(BaseModel):
    """Response model for subscription plan checkout."""
    variant_id: int = Field(..., description="LemonSqueezy variant ID")
    user_email: str = Field(..., description="User's email")
    user_name: str = Field(..., description="User's name")
    checkout_url: str = Field(..., description="Checkout URL")


@openapi.tag("Subscription Plan")
@bp.route("/", methods=["GET"], strict_slashes=False)
@require_token
async def get_list(request: Request):
    """Get list of subscription plans

    openapi:
    ---
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: page_size
        in: query
        schema:
          type: integer
          default: 10
      - name: code
        in: query
        schema:
          type: string
      - name: name
        in: query
        schema:
          type: string
      - name: is_public
        in: query
        schema:
          type: boolean
      - name: status
        in: query
        schema:
          type: integer
      - name: order_by
        in: query
        schema:
          type: array
          items:
            type: string
        description: Fields to sort by (prefix with - for descending)
    responses:
      200:
        description: List of subscription plans
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        ref: "#/components/schemas/SubscriptionPlanResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = SubscriptionPlanQuery(**request.args)
    records, total = await SubscriptionPlanRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@openapi.tag("Subscription Plan")
@bp.route("/<plan_id:str>", methods=["GET"], strict_slashes=False)
@require_token
async def get_detail(request: Request, plan_id: str):
    """Get subscription plan details

    openapi:
    ---
    parameters:
      - name: plan_id
        in: path
        required: true
        schema:
          type: string
      - name: prefetch_features
        in: query
        schema:
          type: boolean
          default: false
    responses:
      200:
        description: Subscription plan details
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/SubscriptionPlanResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    prefetch_features = request.args.get('prefetch_features', 'false').lower() == 'true'
    result = await SubscriptionPlanRepository.get_one(plan_id, prefetch_features=prefetch_features)
    if not result:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="subscription plan not found"
        ), 404)

    body = Response(error=0, message=SUCCESS_MESSAGE, data=result)
    return json_response(body, 200)


@openapi.tag("Subscription Plan")
@bp.route("/", methods=["POST"], strict_slashes=False)
@require_token
async def create(request: Request):
    """Create a new subscription plan

    openapi:
    ---
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/SubscriptionPlanCreate"
    responses:
      201:
        description: Created subscription plan
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/SubscriptionPlanResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        result = await SubscriptionPlanRepository.create_one(request.json)
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.CREATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot create subscription plan",
            context={"error": ResponseCode.CREATE_FAILED}
        )

    body = Response(error=0, message=SUCCESS_MESSAGE, data=result)
    return json_response(body, 201)


@openapi.tag("Subscription Plan")
@bp.route("/<plan_id:str>", methods=["PATCH"], strict_slashes=False)
@require_token
async def update(request: Request, plan_id: str):
    """Update a subscription plan

    openapi:
    ---
    parameters:
      - name: plan_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/SubscriptionPlanUpdate"
    responses:
      200:
        description: Updated subscription plan
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/SubscriptionPlanResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    try:
        affected_rows, record = await SubscriptionPlanRepository.update_one(plan_id, request.json)
    except IntegrityError as e:
        error_logger.exception(e)
        raise BadRequest(
            message=e.args[0].detail,
            context={"error": ResponseCode.UPDATE_FAILED}
        )
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot update subscription plan",
            context={"error": ResponseCode.UPDATE_FAILED}
        )

    if affected_rows > 0:
        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=record),
            200
        )
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="cannot update subscription plan"),
        404
    )


@openapi.tag("Subscription Plan")
@bp.route("/<plan_id:str>/features", methods=["POST"], strict_slashes=False)
@require_token
async def map_features(request: Request, plan_id: str):
    """Map features to a subscription plan

    openapi:
    ---
    parameters:
      - name: plan_id
        in: path
        required: true
        schema:
          type: string
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestMapFeaturesSchema"
    responses:
      200:
        description: Updated subscription plan with mapped features
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/ResponseMapFeaturesPlanSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    try:
        result = await SubscriptionPlanRepository.map_features(
            plan_id=plan_id,
            feature_mappings=request.json.get('features', {})
        )
        if not result:
            return json_response(Response(
                error=ResponseCode.GET_ONE_FAILED,
                message="subscription plan not found"
            ), 404)

        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=result),
            200
        )

    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(
            message="cannot map features to subscription plan",
            context={"error": ResponseCode.UPDATE_FAILED}
        )


@openapi.tag("Subscription Plan")
@bp.route("/<plan_id:str>/checkout", methods=["POST"], strict_slashes=False)
@require_token
async def checkout_plan(request: Request, plan_id: str):
    plan = await SubscriptionPlanRepository.get_one(plan_id)
    if not plan:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="subscription plan not found"
        ), 404)

    # Validate if plan has lemon_variant_id
    if not plan.get('lemon_variant_id'):
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="subscription plan has no associated variant ID"
        ), 400)

    try:
        # 2. Make checkout request to Polar
        json_data = request.json or {}
        user_id = json_data.get('user_id')
        if user_id:
            user = await UserRepository.get_one(user_id)
            user_email = user.get('email')
            user_name = user.get('name')
        else:
            user = request.ctx.user
            user_email = request.ctx.user.get('email')
            user_name = request.ctx.user.get('name')

        if json_data.get('redirect_url'):
            redirect_url = json_data.get('redirect_url')
        else:
            redirect_url = os.getenv("PUBLIC_FRONTEND_URL", "https://nextcareer.ai")
        checkout_data: Checkout = polar_checkout(
            product_id=plan['polar_product_id'],
            user=user,
            redirect_url=redirect_url
        )

        # 3. Return checkout details
        if not checkout_data.url:
            raise ValueError("No checkout URL in response")

        response_data = {
            "variant_id": plan['polar_product_id'],
            "user_email": user_email,
            "user_name": user_name,
            "checkout_url": checkout_data.url
        }

        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=response_data),
            200
        )
    except Exception as e:
        error_logger.exception(e)
        return json_response(Response(
            error=ResponseCode.EXTERNAL_SERVICE_ERROR,
            message="failed to create checkout session"
        ), 400)


@openapi.tag("Subscription Plan")
@bp.route("/<plan_id:str>/checkout-lemonsqueezy", methods=["POST"], strict_slashes=False)
@require_token
async def checkout_plan_lemonsqueezy(request: Request, plan_id: str):
    """Checkout a subscription plan

    openapi:
    ---
    parameters:
    - name: Authorization
      in: header
      description: Access token
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: plan_id
      in: path
      required: true
      schema:
        type: string
      description: ID of the subscription plan
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestCheckoutSchema"
    responses:
      200:
        description: Checkout details
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                  example: 0
                message:
                  type: string
                  example: "success"
                data:
                  ref: "#/components/schemas/CheckoutResponse"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    # 1. Check if subscription plan exists
    plan = await SubscriptionPlanRepository.get_one(plan_id)
    if not plan:
        return json_response(Response(
            error=ResponseCode.GET_ONE_FAILED,
            message="subscription plan not found"
        ), 404)

    # Validate if plan has lemon_variant_id
    if not plan.get('lemon_variant_id'):
        return json_response(Response(
            error=ResponseCode.SERVER_ERROR,
            message="subscription plan has no associated variant ID"
        ), 400)
    try:
        # 2. Make checkout request to LemonSqueezy
        json_data = request.json or {}
        user_id = json_data.get('user_id')
        if user_id:
            user = await UserRepository.get_one(user_id)
            user_email = user.get('email')
            user_name = user.get('name')
        else:
            user_email = request.ctx.user.get('email')
            user_name = request.ctx.user.get('name')

        if json_data.get('redirect_url'):
            redirect_url = json_data.get('redirect_url')
        else:
            redirect_url = os.getenv("PUBLIC_FRONTEND_URL", "https://nextcareer.ai")
        checkout_data = lemonsqueezy_checkout(
            variant_id=plan['lemon_variant_id'],
            email=user_email,
            name=user_name,
            redirect_url=redirect_url
        )

        # 3. Return checkout details
        checkout_url = checkout_data.get("data", {}).get("attributes", {}).get("url")
        if not checkout_url:
            raise ValueError("No checkout URL in response")

        response_data = {
            "variant_id": plan['lemon_variant_id'],
            "user_email": user_email,
            "user_name": user_name,
            "checkout_url": checkout_url
        }

        return json_response(
            Response(error=0, message=SUCCESS_MESSAGE, data=response_data),
            200
        )

    except Exception as e:
        error_logger.exception(e)
        return json_response(Response(
            error=ResponseCode.EXTERNAL_SERVICE_ERROR,
            message="failed to create checkout session"
        ), 400)
