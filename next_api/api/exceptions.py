from json import dumps
from pydantic import ValidationError
from sanic.exceptions import HTTPException
from sanic.handlers import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from sanic.request import Request
from sanic.log import error_logger
from sanic import json
from utils.helpers import ExtendedJsonEncoder
from api.base import Response, ResponseCode, JSONResponse, INVALID_REQUEST_MESSAGE


class Conflict(HTTPException):
    status_code = 409


class CustomErrorHandler(ErrorHandler):
    def default(self, request: Request, exception: Exception) -> JSONResponse:
        """Default handler

        Args:
            request (Request): user request
            exception (Exception): raised exception

        Returns:
            JSONResponse: Format like Response

        Examples:
            Raise error
            raise SanicException(
                "test error",
                status_code=500,
                context={"error": ResponseCode.UNKNOWN_ERROR},
                extra={"more_data": 1},
            )
        """
        if isinstance(exception, ValidationError):
            error_logger.exception(exception.errors())
            response_body = Response(
                error=ResponseCode.CLIENT_BAD_REQUEST,
                message=INVALID_REQUEST_MESSAGE,
                data={
                    "errors_count": exception.error_count(),
                    "errors": exception.errors(
                        include_url=False,
                        include_context=False,
                        include_input=False
                    )
                }
            )

            return json(response_body.model_dump(), status=400, dumps=dumps, cls=ExtendedJsonEncoder)

        context = getattr(exception, "context", None) or {}
        extra = getattr(exception, "extra", None) or {}

        if isinstance(exception, (ConnectionRefusedError, ConnectionError)):
            status_code = 503
            message = "Service Unavailable"
            err_code = ResponseCode.DATABASE_ERROR
        else:
            status_code = getattr(exception, "status_code", 500)
            message = str(exception)
            err_code = context.get("error", ResponseCode.UNKNOWN_ERROR)

        if status_code >= 500:
            error_logger.exception(exception)

        response_body = Response(
            error=err_code,
            message=message,
            data=extra
        )

        return json(response_body.model_dump(), status=status_code, dumps=dumps, cls=ExtendedJsonEncoder)
