from typing import Optional
from datetime import datetime
from pydantic import BaseModel, <PERSON>
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.follow_up_letter import FollowUpLetterRepository, FollowUpLetterQuery

bp = Blueprint("follow_up_letter")


@openapi.component
class RequestFollowUpLetterSchema(BaseModel):
    """Schema for follow-up letter creation/update requests"""
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    jd_id: str = Field(..., description="UUID of job description",
                       example="123e4567-e89b-12d3-a456-************")
    resume_id: Optional[str] = Field(None, description="UUID of resume",
                                     example="123e4567-e89b-12d3-a456-************")
    follow_up: str = Field(..., description="Follow-up letter content")
    customization_note: Optional[str] = Field(None, description="Customization notes")


@openapi.component
class ResponseFollowUpLetterSchema(BaseModel):
    """Schema for follow-up letter responses"""
    id: str = Field(..., description="UUID of follow-up letter",
                    example="123e4567-e89b-12d3-a456-************")
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    jd_id: str = Field(..., description="UUID of job description",
                       example="123e4567-e89b-12d3-a456-************")
    resume_id: Optional[str] = Field(None, description="UUID of resume",
                                     example="123e4567-e89b-12d3-a456-************")
    follow_up: str = Field(..., description="Follow-up letter content")
    customization_note: Optional[str] = Field(None, description="Customization notes")
    created_at: datetime = Field(..., description="Created at",
                                 example="2024-01-01T00:00:00")
    updated_at: datetime = Field(..., description="Updated at",
                                 example="2024-01-01T00:00:00")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Follow-up Letter")
@require_token
async def get_list(request: Request):
    """Get a paginated list of follow-up letters.

    openapi:
    ---
    summary: Get a list of follow-up letters
    description: This endpoint is used to get follow-up letter list.
    parameters:
    - name: Authorization
      in: header
      schema:
        type: string
        format: Bearer <JWT token>
      description: Access token
      required: true
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Number of records per page
      example: 10
    - name: user_id
      in: query
      schema:
        type: string
        format: uuid
      description: User ID
    - name: jd_id
      in: query
      schema:
        type: string
        format: uuid
      description: Job Description ID
    - name: resume_id
      in: query
      schema:
        type: string
        format: uuid
      description: Resume ID
    - name: follow_up__contains
      in: query
      schema:
        type: string
      description: Follow-up content contains
    - name: follow_up__icontains
      in: query
      schema:
        type: string
      description: Follow-up content contains (case insensitive)
    - name: order_by
      in: query
      schema:
        type: array
        items:
          type: string
          enum: ["id", "-id", "created_at", "-created_at", "updated_at", "-updated_at"]
      description: Order by
    responses:
      200:
        description: Successfully retrieved follow-up letters
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        ref: "#/components/schemas/ResponseFollowUpLetterSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = FollowUpLetterQuery(**request.args)
    records, total = await FollowUpLetterRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Follow-up Letter")
@require_token
async def create(request: Request):
    """Create a new follow-up letter.

    openapi:
    ---
    summary: Create a new follow-up letter
    description: This endpoint is used to create a new follow-up letter.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestFollowUpLetterSchema"
    responses:
      201:
        description: Successfully created follow-up letter
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseFollowUpLetterSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        record = await FollowUpLetterRepository.create_one(request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot create follow-up letter",
                         context={"error": ResponseCode.CREATE_FAILED})

    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<letter_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("Follow-up Letter")
@require_token
async def update(request: Request, letter_id: str):
    """Update an existing follow-up letter.

    openapi:
    ---
    summary: Update an existing follow-up letter
    description: This endpoint is used to update an existing follow-up letter.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: letter_id
      in: path
      type: string
      required: true
      description: Follow-up Letter ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestFollowUpLetterSchema"
    responses:
      200:
        description: Successfully updated follow-up letter
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseFollowUpLetterSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Follow-up letter not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    try:
        affected_rows, record = await FollowUpLetterRepository.update(letter_id, request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot update follow-up letter")

    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="Cannot update follow-up letter"),
        404
    )


@bp.route("/<letter_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Follow-up Letter")
@require_token
async def get_one(_: Request, letter_id: str):
    """Get a single follow-up letter by ID.

    openapi:
    ---
    summary: Get a single follow-up letter
    description: This endpoint is used to get a single follow-up letter by ID.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: letter_id
      in: path
      type: string
      required: true
      description: Follow-up Letter ID
    responses:
      200:
        description: Successfully retrieved follow-up letter
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseFollowUpLetterSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Follow-up letter not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    record = await FollowUpLetterRepository.get_one(FollowUpLetterQuery(id=letter_id))

    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.GET_ONE_FAILED,
                 message="Follow-up letter not found"),
        404
    )
