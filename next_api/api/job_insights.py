from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.job_insights import JobInsightsRepository, JobInsightsQuery

bp = Blueprint("job_insights")


@openapi.component
class RequestJobInsightsSchema(BaseModel):
    """Schema for job insights creation/update requests"""
    jd_id: str = Field(..., description="UUID of job description",
                       example="123e4567-e89b-12d3-a456-************")
    resume_id: Optional[str] = Field(None, description="UUID of resume",
                                     example="123e4567-e89b-12d3-a456-************")
    job_title: str = Field(..., description="Job title")
    overview: Optional[str] = Field(None, description="Job overview")
    core_skills: List[Dict[str, Any]] = Field([], description="Core skills required")
    trending_skills: List[Dict[str, Any]] = Field([], description="Trending skills")
    soft_skills: List[Dict[str, Any]] = Field([], description="Soft skills")
    professional_courses: List[Dict[str, Any]] = Field([], description="Professional courses")
    certifications: List[Dict[str, Any]] = Field([], description="Certifications")
    projects: List[Dict[str, Any]] = Field([], description="Projects")
    expected_salary: Dict[str, Any] = Field({}, description="Expected salary range")
    advises: List[str] = Field([], description="Career advises")
    other_insights: List[str] = Field([], description="Other insights")


@openapi.component
class ResponseJobInsightsSchema(BaseModel):
    """Schema for job insights responses"""
    id: str = Field(..., description="UUID of job insights",
                    example="123e4567-e89b-12d3-a456-************")
    jd_id: str = Field(..., description="UUID of job description",
                       example="123e4567-e89b-12d3-a456-************")
    resume_id: Optional[str] = Field(None, description="UUID of resume",
                                     example="123e4567-e89b-12d3-a456-************")
    job_title: str = Field(..., description="Job title")
    overview: Optional[str] = Field(None, description="Job overview")
    core_skills: List[Dict[str, Any]] = Field(..., description="Core skills required")
    trending_skills: List[Dict[str, Any]] = Field(..., description="Trending skills")
    soft_skills: List[Dict[str, Any]] = Field(..., description="Soft skills")
    professional_courses: List[Dict[str, Any]] = Field(..., description="Professional courses")
    certifications: List[Dict[str, Any]] = Field(..., description="Certifications")
    projects: List[Dict[str, Any]] = Field(..., description="Projects")
    expected_salary: Dict[str, Any] = Field(..., description="Expected salary range")
    advises: List[str] = Field(..., description="Career advises")
    other_insights: List[str] = Field(..., description="Other insights")
    created_at: datetime = Field(..., description="Created at",
                                 example="2024-01-01T00:00:00")
    updated_at: datetime = Field(..., description="Updated at",
                                 example="2024-01-01T00:00:00")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Job Insights")
@require_token
async def get_list(request: Request):
    """Get a paginated list of job insights.

    openapi:
    ---
    summary: Get a list of job insights
    description: This endpoint is used to get job insights list.
    parameters:
    - name: Authorization
      in: header
      schema:
        type: string
        format: Bearer <JWT token>
      description: Access token
      required: true
    - name: page
      in: query
      schema:
        type: integer
        default: 1
      description: Page number
      example: 1
    - name: page_size
      in: query
      schema:
        type: integer
        default: 10
      description: Number of records per page
      example: 10
    - name: jd_id
      in: query
      schema:
        type: string
        format: uuid
      description: Job Description ID
    - name: resume_id
      in: query
      schema:
        type: string
        format: uuid
      description: Resume ID
    - name: job_title
      in: query
      schema:
        type: string
      description: Job title
    - name: job_title__contains
      in: query
      schema:
        type: string
      description: Job title contains
    - name: job_title__icontains
      in: query
      schema:
        type: string
      description: Job title contains (case insensitive)
    - name: order_by
      in: query
      schema:
        type: array
        items:
          type: string
          enum: ["id", "-id", "job_title", "-job_title", "created_at", "-created_at", "updated_at", "-updated_at"]
      description: Order by
    responses:
      200:
        description: Successfully retrieved job insights
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  type: object
                  properties:
                    total:
                      type: integer
                    page:
                      type: integer
                    page_size:
                      type: integer
                    records:
                      type: array
                      items:
                        ref: "#/components/schemas/ResponseJobInsightsSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = JobInsightsQuery(**request.args)
    records, total = await JobInsightsRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Job Insights")
@require_token
async def create(request: Request):
    """Create a new job insights record.

    openapi:
    ---
    summary: Create a new job insights record
    description: This endpoint is used to create a new job insights record.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestJobInsightsSchema"
    responses:
      201:
        description: Successfully created job insights
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseJobInsightsSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
    """
    try:
        record = await JobInsightsRepository.create_one(request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot create job insights",
                         context={"error": ResponseCode.CREATE_FAILED})

    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<insights_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("Job Insights")
@require_token
async def update(request: Request, insights_id: str):
    """Update an existing job insights record.

    openapi:
    ---
    summary: Update an existing job insights record
    description: This endpoint is used to update an existing job insights record.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: insights_id
      in: path
      type: string
      required: true
      description: Job Insights ID
    requestBody:
      required: true
      content:
        application/json:
          schema:
            ref: "#/components/schemas/RequestJobInsightsSchema"
    responses:
      200:
        description: Successfully updated job insights
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseJobInsightsSchema"
      400:
        description: Bad request
        content:
          application/json:
            schema:
              ref: "#/components/schemas/BadRequestResponse"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Job insights not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    try:
        affected_rows, record = await JobInsightsRepository.update(insights_id, request.json)
    except Exception as e:
        error_logger.exception(e)
        raise BadRequest(message="Cannot update job insights")

    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.UPDATE_FAILED,
                 message="Cannot update job insights"),
        404
    )


@bp.route("/<insights_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Job Insights")
@require_token
async def get_one(_: Request, insights_id: str):
    """Get a single job insights record by ID.

    openapi:
    ---
    summary: Get a single job insights record
    description: This endpoint is used to get a single job insights record by ID.
    parameters:
    - name: Authorization
      in: header
      description: Authorization header
      required: true
      schema:
        type: string
        format: Bearer <JWT token>
    - name: insights_id
      in: path
      type: string
      required: true
      description: Job Insights ID
    responses:
      200:
        description: Successfully retrieved job insights
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: integer
                message:
                  type: string
                data:
                  ref: "#/components/schemas/ResponseJobInsightsSchema"
      401:
        description: Unauthorized
        content:
          application/json:
            schema:
              ref: "#/components/schemas/UnauthorizedResponse"
      404:
        description: Job insights not found
        content:
          application/json:
            schema:
              ref: "#/components/schemas/NotFoundResponse"
    """
    record = await JobInsightsRepository.get_one(JobInsightsQuery(id=insights_id))

    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(
        Response(error=ResponseCode.GET_ONE_FAILED,
                 message="Job insights not found"),
        404
    )
