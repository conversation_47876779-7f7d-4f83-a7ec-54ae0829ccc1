from typing import Union, Optional, List
from datetime import datetime
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode, \
    OPENAPI_BAD_REQUEST_RESPONSE, OPENAPI_NOT_FOUND_RESPONSE, OPENAPI_UNAUTHORIZED_RESPONSE
from api.middlewares import require_token
from repositories.base import Pagination
from repositories.resume import ResumeRepository, ResumeQuery

bp = Blueprint("resume")


@openapi.component
class RequestResumeSchema(BaseModel):
    """Schema for resume creation/update requests"""
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    title: Optional[str] = Field(...,
                                 description="Title of resume", example="title")
    slug: Optional[str] = Field(...,
                                description="Slug of resume", example="slug")
    content: Optional[dict] = Field(...,
                                    description="Content of resume", example="content")
    keywords: Optional[List[str]] = Field(..., description="Keywords of resume", example=[
                                          "keyword1", "keyword2"])
    status: Optional[int] = Field(...,
                                  description="Status of resume", example=1)


@openapi.component
class ResponseResumeSchema(BaseModel):
    """Schema for resume responses"""
    id: str = Field(..., description="UUID of resume",
                    example="123e4567-e89b-12d3-a456-************")
    user_id: str = Field(..., description="UUID of user",
                         example="123e4567-e89b-12d3-a456-************")
    title: str = Field(None, description="Title of resume", example="title")
    content: dict = Field(
        None, description="Content of resume", example="content")
    keywords: List[str] = Field(None, description="Keywords of resume", example=[
                                "keyword1", "keyword2"])
    status: int = Field(..., description="Status of resume", example=1)
    created_at: datetime = Field(..., description="Created at",
                                 example="2024-01-01T00:00:00")
    updated_at: datetime = Field(..., description="Updated at",
                                 example="2024-01-01T00:00:00")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("Resume")
@openapi.definition(
    summary="Get a list of resumes",
    description="This endpoint is used to get resume list.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "page", int, location="query", description="Page number", required=False, default=1),
        openapi.definitions.Parameter(
            "page_size", int, location="query", description="Number of records per page", required=False, default=10),
        openapi.definitions.Parameter(
            "id", str, location="query", description="Resume ID", required=False),
        openapi.definitions.Parameter(
            "id__in", Union[list[str], str], location="query", description="Resume ID list", required=False),
        openapi.definitions.Parameter(
            "id__not_in", Union[list[str], str], location="query", description="Resume ID list", required=False),
        openapi.definitions.Parameter(
            "user_id", str, location="query", description="User ID", required=False),
        openapi.definitions.Parameter(
            "user_id__in", Union[list[str], str], location="query", description="User ID list", required=False),
        openapi.definitions.Parameter(
            "user_id__not_in", Union[list[str], str], location="query", description="User ID list", required=False),
        openapi.definitions.Parameter(
            "title", str, location="query", description="Title", required=False),
        openapi.definitions.Parameter(
            "title__contains", str, location="query", description="Title contains", required=False),
        openapi.definitions.Parameter(
            "title__icontains", str, location="query",
            description="Title contains, search case insensitive", required=False),
        openapi.definitions.Parameter(
            "title__startswith", str, location="query", description="Title starts with", required=False),
        openapi.definitions.Parameter(
            "title__endswith", str, location="query", description="Title ends with", required=False),
        openapi.definitions.Parameter(
            "content", str, location="query", description="Content", required=False),
        openapi.definitions.Parameter(
            "content__contains", str, location="query", description="Content contains", required=False),
        openapi.definitions.Parameter(
            "content__icontains", str, location="query",
            description="Content contains, search case insensitive", required=False),
        openapi.definitions.Parameter(
            "keywords", Union[list[str], str], location="query", description="Keywords", required=False),
        openapi.definitions.Parameter(
            "status", int, location="query", description="Status", required=False),
        openapi.definitions.Parameter(
            "status__in", Union[list[int], int], location="query", description="Status list", required=False),
        openapi.definitions.Parameter(
            "status__not_in", Union[list[int], int], location="query", description="Status list", required=False),
        openapi.definitions.Parameter(
            "created_at", str, location="query", description="Created at", required=False),
        openapi.definitions.Parameter(
            "created_at__gte", str, location="query",
            description="Created at greater than or equal to", required=False),
        openapi.definitions.Parameter(
            "created_at__lte", str, location="query", description="Created at less than or equal to", required=False),
        openapi.definitions.Parameter(
            "updated_at", str, location="query", description="Updated at", required=False),
        openapi.definitions.Parameter(
            "updated_at__gte", str, location="query",
            description="Updated at greater than or equal to", required=False),
        openapi.definitions.Parameter(
            "updated_at__lte", str, location="query", description="Updated at less than or equal to", required=False),
        openapi.definitions.Parameter(
            "order_by", str, location="query", description="Order by", required=False),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a resume list",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {
                                "type": "integer",
                                "description": "Error code, 0 means success"
                            },
                            "message": {
                                "type": "string",
                                "description": "Response message"
                            },
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {"type": "integer", "description": "Total number of records"},
                                    "page": {"type": "integer", "description": "Current page number"},
                                    "page_size": {"type": "integer", "description": "Number of records per page"},
                                    "records": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "$ref": "#/components/schemas/ResponseResumeSchema"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def get_list(request: Request):
    """Get a paginated list of resumes."""
    pagination = Pagination(
        page=int(request.args.get('page', 1)),
        page_size=int(request.args.get('page_size', 10))
    )
    query = ResumeQuery(**request.args)
    records, total = await ResumeRepository.get_list(query, pagination)
    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=ListResponse(
            total=total,
            page=pagination.page,
            page_size=pagination.page_size,
            records=records
        )
    )
    return json_response(body, 200)


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("Resume")
@openapi.definition(
    summary="Create a new resume",
    description="This endpoint is used to create a new resume.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
    ],
    body=openapi.definitions.RequestBody(
        description="Resume data",
        content={
            "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/RequestResumeSchema"
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=201, description="Successfully created a resume",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {
                                "type": "integer",
                                "description": "Error code, 0 means success"
                            },
                            "message": {
                                "type": "string",
                                "description": "Response message"
                            },
                            "data": {
                                "$ref": "#/components/schemas/ResponseResumeSchema"
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def create(request: Request):
    """Create a new resume."""
    try:
        record = await ResumeRepository.create_one(request.json)
    except Exception as e:
        error_logger.error(e)
        raise BadRequest(message="Cannot create resume", context={
                         "error": ResponseCode.CREATE_FAILED})

    body = Response(
        error=0,
        message=SUCCESS_MESSAGE,
        data=dict(record)
    )
    return json_response(body, 201)


@bp.route("/<resume_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("Resume")
@openapi.definition(
    summary="Update an existing resume by ID",
    description="This endpoint is used to update an existing resume by ID.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "resume_id", str, location="path", description="Resume ID", required=True),
    ],
    body=openapi.definitions.RequestBody(
        description="Resume data",
        content={
            "application/json": {
                "schema": {
                    "$ref": "#/components/schemas/RequestResumeSchema"
                }
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully updated a resume",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "description": "Response data",
                                "$ref": "#/components/schemas/ResponseResumeSchema"
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_NOT_FOUND_RESPONSE
    ],
)
@require_token
async def update(request: Request, resume_id: str):
    """Update an existing resume by ID."""
    try:
        affected_rows, record = await ResumeRepository.update(resume_id, request.json)
    except Exception as e:
        error_logger.error(e)
        raise BadRequest(message="Cannot update resume")

    if affected_rows > 0:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(Response(error=ResponseCode.UPDATE_FAILED, message="Cannot update resume"), 404)


@bp.route("/<resume_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("Resume")
@openapi.definition(
    summary="Get a single resume by ID",
    description="This endpoint is used to get a single resume by ID.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "resume_id", str, location="path", description="Resume ID", required=True),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a resume",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "$ref": "#/components/schemas/ResponseResumeSchema"
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_NOT_FOUND_RESPONSE,
    ],
)
@require_token
async def get_one(_: Request, resume_id: str):
    """Get a single resume by ID."""
    record = await ResumeRepository.get_one(ResumeQuery(id=resume_id))

    if record:
        return json_response(Response(error=0, message=SUCCESS_MESSAGE, data=record), 200)
    return json_response(Response(error=ResponseCode.GET_ONE_FAILED, message="Resume not found"), 404)
