from sanic import Blueprint, Request, empty
from sanic_ext import openapi

bp = Blueprint("healthcheck")


@bp.route("/", methods=["POST"])
@openapi.tag("Health")
@openapi.definition(
    summary="Health check endpoint",
    description="This endpoint is used to check the health of the API.",
    response=openapi.definitions.Response(
        status=204, description="API is healthy"),
)
async def health_check(_: Request):
    return empty(status=204) 