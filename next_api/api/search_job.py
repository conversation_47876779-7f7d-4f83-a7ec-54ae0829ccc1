from typing import Optional, List
from pydantic import BaseModel, Field
from sanic import Blueprint, Request
from sanic.log import error_logger
from sanic_ext import openapi
from api.base import json_response, Response, ListResponse, SUCCESS_MESSAGE, ResponseCode, \
    OPENAPI_BAD_REQUEST_RESPONSE, OPENAPI_NOT_FOUND_RESPONSE, OPENAPI_UNAUTHORIZED_RESPONSE
from repositories.base import Pagination
from repositories.search_job import SearchJobRepository, SearchJobQuery
from api.middlewares import require_token

bp = Blueprint("search_job")


@openapi.component
class ResponseSearchJobSchema(BaseModel):
    id: str = Field(..., format="UUID", description="UUID of job posting",
                    example="123e4567-e89b-12d3-a456-************")
    title: str = Field(..., description="Job title", example="Software Engineer")
    company: str = Field(..., description="Company name", example="Google")
    location: str = Field(..., description="Job location", example="Mountain View, CA")
    salary: Optional[str] = Field(None, description="Job salary", example="$100,000 - $150,000")
    description: Optional[str] = Field(None, description="Job description")
    link: Optional[str] = Field(None, description="Job posting URL")
    posted_at: Optional[str] = Field(None, description="When the job was posted")
    is_remote: Optional[bool] = Field(None, description="Whether the job is remote")
    source: Optional[str] = Field(None, description="Source of the job posting")
    category: Optional[str] = Field(None, description="Job category")
    created_at: Optional[str] = Field(None, description="When the job was created")
    updated_at: Optional[str] = Field(None, description="When the job was last updated")
    keywords: Optional[List[str]] = Field(default_factory=list, description="Keywords associated with the job")


@openapi.component
class RequestSearchJobSchema(BaseModel):
    title: str = Field(..., description="Job title", example="Software Engineer")
    company: str = Field(..., description="Company name", example="Google")
    location: str = Field(..., description="Job location", example="Mountain View, CA")
    salary: Optional[str] = Field(None, description="Job salary", example="$100,000 - $150,000")
    description: Optional[str] = Field(None, description="Job description")
    link: Optional[str] = Field(None, description="Job posting URL")
    posted_at: Optional[str] = Field(None, description="When the job was posted")
    is_remote: Optional[bool] = Field(None, description="Whether the job is remote")
    source: Optional[str] = Field(None, description="Source of the job posting")
    category: Optional[str] = Field(None, description="Job category")
    keywords: Optional[List[str]] = Field(default_factory=list, description="Keywords associated with the job")


@bp.route("", methods=["GET"], strict_slashes=False)
@openapi.tag("SearchJob")
@openapi.definition(
    summary="Get a list of job postings",
    description="This endpoint is used to get a list of job postings with filtering and pagination.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "title", str, location="query", description="Job title", required=False),
        openapi.definitions.Parameter(
            "company", str, location="query", description="Company name", required=False),
        openapi.definitions.Parameter(
            "location", str, location="query", description="Job location", required=False),
        openapi.definitions.Parameter(
            "salary", str, location="query", description="Job salary", required=False),
        openapi.definitions.Parameter(
            "is_remote", bool, location="query", description="Whether the job is remote", required=False),
        openapi.definitions.Parameter(
            "source", str, location="query", description="Source of the job posting", required=False),
        openapi.definitions.Parameter(
            "category", str, location="query", description="Job category", required=False),
        openapi.definitions.Parameter(
            "order_by", str, location="query", description="Order by. Example: `created_at,-updated_at`", required=False),
        openapi.definitions.Parameter(
            "page", int, location="query", description="Page number", required=False),
        openapi.definitions.Parameter(
            "page_size", int, location="query", description="Number of records per page", required=False),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved list of job postings",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {
                                "type": "integer",
                                "description": "Error code, 0 means success"
                            },
                            "message": {
                                "type": "string",
                                "description": "Response message"
                            },
                            "data": {
                                "type": "object",
                                "properties": {
                                    "total": {
                                        "type": "integer",
                                        "description": "Total number of records"
                                    },
                                    "page": {
                                        "type": "integer",
                                        "description": "Current page number"
                                    },
                                    "page_size": {
                                        "type": "integer",
                                        "description": "Number of records per page"
                                    },
                                    "records": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "$ref": "#/components/schemas/ResponseSearchJobSchema"
                                        }
                                    }
                                }
                            }
                        },
                        "required": ["error", "message", "data"]
                    }
                }
            }
        ),
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ]
)
@require_token
async def get_list(request):
    """
    Get a list of job postings with filtering and pagination.
    """
    try:
        # Parse query parameters
        query_params = request.args
        query = SearchJobQuery(**query_params)

        # Parse pagination parameters
        page = int(query_params.get("page", 1))
        page_size = int(query_params.get("page_size", 10))
        pagination = Pagination(page=page, page_size=page_size)

        # Get job postings
        jobs, total = await SearchJobRepository.get_list(query, pagination)

        # Create response
        response_data = ListResponse(
            total=total,
            page=page,
            page_size=page_size,
            records=jobs
        )

        return json_response(
            Response(error=ResponseCode.SUCCESS, message=SUCCESS_MESSAGE, data=response_data),
            200
        )
    except Exception as e:
        error_logger.exception("Error getting job postings: %s", str(e))
        return json_response(
            Response(error=ResponseCode.GET_LIST_FAILED, message=str(e), data={}),
            400
        )


@bp.route("", methods=["POST"], strict_slashes=False)
@openapi.tag("SearchJob")
@openapi.definition(
    summary="Create a new job posting",
    description="This endpoint is used to create a new job posting.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
    ],
    body=openapi.definitions.RequestBody(
        description="Job posting data",
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "$ref": "#/components/schemas/RequestSearchJobSchema",
                    "required": ["title", "company", "location"]
                },
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=201, description="Successfully created a new job posting",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseSearchJobSchema",
                            }
                        }
                    }
                }
            },
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@require_token
async def create(request):
    """
    Create a new job posting.
    """
    try:
        # Parse request body
        job_data = request.json

        # Create job posting
        job = await SearchJobRepository.create_one(job_data)

        return json_response(
            Response(error=ResponseCode.SUCCESS, message=SUCCESS_MESSAGE, data=job),
            201
        )
    except Exception as e:
        error_logger.exception("Error creating job posting: %s", str(e))
        return json_response(
            Response(error=ResponseCode.CREATE_FAILED, message=str(e), data={}),
            400
        )


@bp.route("/<job_id:str>", methods=["PUT"], strict_slashes=False)
@openapi.tag("SearchJob")
@openapi.definition(
    summary="Update a job posting",
    description="This endpoint is used to update a job posting.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "job_id", str, location="path", description="Job ID", required=True),
    ],
    body=openapi.definitions.RequestBody(
        description="Job posting data",
        content={
            "application/json": {
                "schema": {
                    "type": "object",
                    "$ref": "#/components/schemas/RequestSearchJobSchema",
                },
            }
        }
    ),
    response=[
        openapi.definitions.Response(
            status=201, description="Successfully updated a job posting",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer", "description": "Error code, 0 means success"},
                            "message": {"type": "string", "description": "Response message"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseSearchJobSchema",
                            }
                        }
                    }
                }
            },
        ),
        OPENAPI_BAD_REQUEST_RESPONSE,
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@require_token
async def update(request, job_id: str):
    """
    Update a job posting.
    """
    try:
        # Parse request body
        job_data = request.json

        # Update job posting
        found,  job = await SearchJobRepository.update(job_id, job_data)

        if found == 0:
            return json_response(
                Response(error=ResponseCode.GET_ONE_FAILED, message="Job posting not found", data={}),
                404
            )

        return json_response(
            Response(error=ResponseCode.SUCCESS, message=SUCCESS_MESSAGE, data=job),
            200
        )
    except Exception as e:
        error_logger.exception("Error updating job posting: %s", str(e))
        return json_response(
            Response(error=ResponseCode.UPDATE_FAILED, message=str(e), data={}),
            400
        )


@bp.route("/<job_id:str>", methods=["GET"], strict_slashes=False)
@openapi.tag("SearchJob")
@openapi.definition(
    summary="Get a job posting",
    description="This endpoint is used to get a job posting.",
    parameter=[
        openapi.definitions.Parameter(
            name="Authorization", location="header", type="string", description="Authorization header", required=True,
            schema=openapi.definitions.Schema(type="string", format="Bearer <JWT token>")),
        openapi.definitions.Parameter(
            "job_id", str, location="path", description="Job ID", required=True),
    ],
    response=[
        openapi.definitions.Response(
            status=200, description="Successfully retrieved a job posting",
            content={
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "error": {"type": "integer"},
                            "message": {"type": "string"},
                            "data": {
                                "type": "object",
                                "$ref": "#/components/schemas/ResponseSearchJobSchema",
                            }
                        }
                    }
                }
            }
        ),
        OPENAPI_NOT_FOUND_RESPONSE,
        OPENAPI_UNAUTHORIZED_RESPONSE,
    ],
)
@require_token
async def get_one(_: Request, job_id: str):
    """
    Get a job posting.
    """
    try:
        # Get job posting
        job = await SearchJobRepository.get_one(job_id)

        if not job:
            return json_response(
                Response(error=ResponseCode.GET_ONE_FAILED, message="Job posting not found", data={}),
                404
            )

        return json_response(
            Response(error=ResponseCode.SUCCESS, message=SUCCESS_MESSAGE, data=job),
            200
        )
    except Exception as e:
        error_logger.exception("Error getting job posting: %s", str(e))
        return json_response(
            Response(error=ResponseCode.GET_ONE_FAILED, message=str(e), data={}),
            400
        )
