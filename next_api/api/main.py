from sanic import Sanic
from sanic_ext import Extend
from tortoise.contrib.sanic import register_tortoise
from settings import APP_API_NAME, LOGGING_CONFIG, DATABASE_CONFIG, CORS_ORIGINS, CORS_ALLOW_METHODS
from api.ai import bp as ai_bp
from api.application_kit import bp as application_kit_bp
from api.article import bp as article_bp
from api.auth import bp as auth_bp
from api.cover_letter import bp as cover_letter_bp
from api.feature import bp as feature_bp
from api.follow_up_letter import bp as follow_up_letter_bp
from api.health import bp as health_bp
from api.job_description import bp as job_description_bp
from api.job_insights import bp as job_insights_bp
from api.job_search import bp as job_search_bp
from api.metric import bp as metric_bp
from api.mock_interview import bp as mock_interview_bp
from api.my import bp as my_bp
from api.permission import bp as permission_bp
from api.question import bp as question_bp
from api.resume import bp as resume_bp
from api.resume_adjustment import bp as resume_adjustment_bp
from api.role import bp as role_bp
from api.search_job import bp as search_job_bp
from api.subscription_plan import bp as subscription_plan_bp
from api.user import bp as user_bp
from api.webhook import bp as webhook_bp
from api.exceptions import CustomErrorHandler

def register_blueprints(app: Sanic):
    app.blueprint(ai_bp, url_prefix="/ai")
    app.blueprint(application_kit_bp, url_prefix="/application-kit")
    app.blueprint(article_bp, url_prefix="/articles")
    app.blueprint(auth_bp, url_prefix="/auth")
    app.blueprint(cover_letter_bp, url_prefix="/cover-letters")
    app.blueprint(feature_bp, url_prefix="/features")
    app.blueprint(follow_up_letter_bp, url_prefix="/follow-up-letters")
    app.blueprint(health_bp, url_prefix="/healthcheck")
    app.blueprint(job_description_bp, url_prefix="/jds")
    app.blueprint(job_insights_bp, url_prefix="/job-insights")
    app.blueprint(metric_bp)
    app.blueprint(mock_interview_bp, url_prefix="/mock-interviews")
    app.blueprint(permission_bp, url_prefix="/permissions")
    app.blueprint(question_bp, url_prefix="/questions")
    app.blueprint(resume_bp, url_prefix="/resumes")
    app.blueprint(resume_adjustment_bp, url_prefix="/resume-adjustments")
    app.blueprint(role_bp, url_prefix="/roles")
    app.blueprint(search_job_bp, url_prefix="/search-jobs")
    app.blueprint(subscription_plan_bp, url_prefix="/subscription-plans")
    app.blueprint(user_bp, url_prefix="/users")
    app.blueprint(my_bp, url_prefix="/my")
    app.blueprint(webhook_bp, url_prefix="/webhooks")
    app.blueprint(job_search_bp, url_prefix="/job-search")

# Temporary disabled this because of `register_cors`
# def register_openapi(app: Sanic):
#     app.config.OAS_URL_PREFIX = "/api-docs"
#     app.config.OAS_UI_DEFAULT = "swagger"

#     app.ext.openapi.describe(
#         "NextJob AI API",
#         version="1.0.0",
#         description=dedent(
#             """
#             # Introduction

#             This page contains the documentation for the NextJob AI API.
#             """
#         ),
#     )


def register_cors(app: Sanic):
    app.config.CORS_ORIGINS = CORS_ORIGINS
    app.config.CORS_AUTOMATIC_OPTIONS = True
    app.config.CORS_METHODS = CORS_ALLOW_METHODS
    Extend(app)


def create_app() -> Sanic:
    app = Sanic(
        APP_API_NAME,
        log_config=LOGGING_CONFIG,
        error_handler=CustomErrorHandler(),
    )
    register_cors(app)

    register_tortoise(app, config=DATABASE_CONFIG)
    # register_openapi(app)
    register_blueprints(app)

    return app
