from typing import List, Optional
from pydantic import BaseModel, Field

class UrlInfo(BaseModel):
    label: str = Field(default="NextCareer", description="Label of the URL, default: empty string")
    href: str = Field(default="", description="URL link, default: empty string")

class CustomField(BaseModel):
    id: str = Field(default="CustomField", description="Unique UUID identifier for the custom field")
    icon: str = Field(default="CustomField", description="Name of the icon for the custom field")
    name: str = Field(default="CustomField", description="Name of the custom field")
    value: str = Field(default="CustomField", description="Value of the custom field")

class PictureEffects(BaseModel):
    hidden: bool = Field(default=False, description="Whether the profile picture is hidden")
    border: bool = Field(default=False, description="Whether the profile picture has a border")
    grayscale: bool = Field(default=False, description="Whether the profile picture is grayscale")

class Picture(BaseModel):
    url: str = Field(default="https://i.imgur.com/HgwyOuJ.jpg", description="URL of the profile picture")
    size: int = Field(default=64, description="Size of the profile picture")
    aspectRatio: int = Field(default=1, description="Aspect ratio of the profile picture")
    borderRadius: int = Field(default=0, description="Border radius of the profile picture")
    effects: PictureEffects = Field(default_factory=PictureEffects, description="Effects applied to the profile picture")

class Basics(BaseModel):
    fullname: str = Field(default="NextCareer", description="The person's full name")
    headline: str = Field(default="NextCareer", description="Professional headline or title")
    email: str = Field(default="", description="Email address")
    phone: str = Field(default="NextCareer", description="Phone number with format")
    location: str = Field(default="NextCareer", description="City, State, ZIP code")
    url: UrlInfo = Field(default_factory=UrlInfo, description="Website information")
    customFields: List[CustomField] = Field(default_factory=list, description="Custom fields")
    picture: Picture = Field(default_factory=Picture, description="Profile picture information")

class SectionBase(BaseModel):
    name: str = Field(default="Section", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=True, description="Whether to separate links")
    visible: bool = Field(default=True, description="Whether to show the section")
    id: str = Field(default="SectionBase", description="Unique identifier for the section")

class SummarySection(SectionBase):
    id: str = Field(default="summary", description="Unique identifier for the section")
    content: str = Field(default="Summary", description="Content of the section")

class SkillItem(BaseModel):
    name: str = Field(default="Skill", description="Skill category")
    description: str = Field(default="Skill", description="Proficiency level")
    keywords: List[str] = Field(default_factory=list, description="Specific skills")
    level: int = Field(default=0, description="Level of the skill")
    visible: bool = Field(default=True, description="Whether to show the item")

class SkillsSection(SectionBase):
    items: List[SkillItem] = Field(default_factory=list, description="List of skills")

class ExperienceItem(BaseModel):
    company: str = Field(default="NextCareer", description="Company name")
    position: str = Field(default="Position", description="Job title")
    location: str = Field(default="Location", description="Job location")
    date: str = Field(default="Date", description="Employment period")
    summary: str = Field(default="summary", description="Job description and achievements")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the experience")
    visible: bool = Field(default=True, description="Whether to show the item")

class ExperienceSection(SectionBase):
    id: str = Field(default="experience", description="Unique identifier for the section")
    items: List[ExperienceItem] = Field(default_factory=list, description="List of experiences")

class EducationItem(BaseModel):
    institution: str = Field(default="My University", description="School or university name")
    studyType: str = Field(default="Degree", description="Degree type")
    area: str = Field(default="Field", description="Field of study")
    date: str = Field(default="Date", description="Education period")
    summary: str = Field(default="Summary", description="Summary of the education")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the education")
    visible: bool = Field(default=True, description="Whether to show the item")
    score: str = Field(default="4.0", description="Score of the education")

class EducationSection(SectionBase):
    id: str = Field(default="education", description="Unique identifier for the section")
    items: List[EducationItem] = Field(default_factory=list, description="List of education")

class CertificationItem(BaseModel):
    name: str = Field(default="Certification", description="Certification name")
    issuer: str = Field(default="Issuer", description="Issuing organization")
    date: str = Field(default="Date", description="Date obtained")
    visible: bool = Field(default=True, description="Whether to show the item")
    summary: str = Field(default="Summary", description="Summary of the certification")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the certification")

class CertificationsSection(SectionBase):
    id: str = Field(default="certifications", description="Unique identifier for the section")
    items: List[CertificationItem] = Field(default_factory=list, description="List of certifications")

class ProjectItem(BaseModel):
    name: str = Field(default="Project", description="Project name")
    description: str = Field(default="Description", description="Role or project type")
    summary: str = Field(default="Summary", description="Project description and achievements")
    date: str = Field(default="Date", description="Project period")
    keywords: List[str] = Field(default_factory=list, description="Specific keywords")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the project")
    visible: bool = Field(default=True, description="Whether to show the item")

class ProjectsSection(SectionBase):
    id: str = Field(default="projects", description="Unique identifier for the section")
    items: List[ProjectItem] = Field(default_factory=list, description="List of projects")

class PublicationItem(BaseModel):
    name: str = Field(default="Publication", description="Publication title")
    publisher: str = Field(default="Publisher", description="Publisher name")
    date: str = Field(default="Date", description="Publication date")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the publication")
    summary: str = Field(default="Summary", description="Publication description")
    visible: bool = Field(default=True, description="Whether to show the item")

class PublicationsSection(SectionBase):
    id: str = Field(default="publications", description="Unique identifier for the section")
    items: List[PublicationItem] = Field(default_factory=list, description="List of publications")

class VolunteerItem(BaseModel):
    organization: str = Field(default="Organization", description="Organization name")
    position: str = Field(default="Position", description="Volunteer position")
    date: str = Field(default="Date", description="Volunteer period")
    summary: str = Field(default="Summary", description="Description of volunteer work")
    location: str = Field(default="Location", description="Location of the volunteer")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the volunteer")
    visible: bool = Field(default=True, description="Whether to show the item")

class VolunteerSection(SectionBase):
    id: str = Field(default="volunteer", description="Unique identifier for the section")
    items: List[VolunteerItem] = Field(default_factory=list, description="List of volunteer experiences")

class LanguageItem(BaseModel):
    name: str = Field(default="Language", description="Language name")
    description: str = Field(default="Description", description="Proficiency level")
    visible: bool = Field(default=True, description="Whether to show the item")

class LanguagesSection(SectionBase):
    id: str = Field(default="languages", description="Unique identifier for the section")
    items: List[LanguageItem] = Field(default_factory=list, description="List of languages")

class InterestItem(BaseModel):
    name: str = Field(default="Interest", description="Interest category")
    visible: bool = Field(default=True, description="Whether to show the item")
    keywords: List[str] = Field(default_factory=list, description="Specific interests")

class InterestsSection(SectionBase):
    id: str = Field(default="interests", description="Unique identifier for the section")
    items: List[InterestItem] = Field(default_factory=list, description="List of interests")

class AwardItem(BaseModel):
    title: str = Field(default="Award", description="Award name")
    awarder: str = Field(default="Awarder", description="Awarding organization")
    date: str = Field(default="Date", description="Date received")
    summary: str = Field(default="Summary for the award", description="Description of the award")
    visible: bool = Field(default=True, description="Whether to show the item")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the award")

class AwardsSection(SectionBase):
    id: str = Field(default="awards", description="Unique identifier for the section")
    items: List[AwardItem] = Field(default_factory=list, description="List of awards")

class ReferenceItem(BaseModel):
    name: str = Field(default="Reference", description="Reference name or statement")
    visible: bool = Field(default=True, description="Whether to show the item")
    description: str = Field(default="Description", description="Description of the reference")
    summary: str = Field(default="Summary for the reference", description="Summary of the reference")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the reference")

class ReferencesSection(SectionBase):
    id: str = Field(default="references", description="Unique identifier for the section")
    items: List[ReferenceItem] = Field(default_factory=list, description="List of references")

class ProfileItem(BaseModel):
    name: str = Field(default="Profile", description="Profile name")
    visible: bool = Field(default=True, description="Whether to show the item")
    network: str = Field(default="linkedin", description="Network of the profile")
    icon: str = Field(default="linkedin", description="Icon of the profile")
    url: UrlInfo = Field(default_factory=UrlInfo, description="URL of the profile")

class ProfileSection(SectionBase):
    id: str = Field(default="profiles", description="Unique identifier for the section")
    name: str = Field(default="Profiles", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=True, description="Whether to separate links")
    visible: bool = Field(default=True, description="Whether to show the section")
    items: List[ProfileItem] = Field(default_factory=list, description="List of profiles")

class CustomItem(BaseModel):
    name: str = Field(default="Custom", description="Custom item name")
    visible: bool = Field(default=True, description="Whether to show the item")
    description: str = Field(default="Description", description="Description of the custom item")

class CustomSection(SectionBase):
    id: str = Field(default="custom", description="Unique identifier for the section")
    name: str = Field(default="Custom", description="Name of the section")
    columns: int = Field(default=1, description="Number of columns, must be greater than 0")
    separateLinks: bool = Field(default=True, description="Whether to separate links")
    visible: bool = Field(default=True, description="Whether to show the section")
    items: List[CustomItem] = Field(default_factory=list, description="List of custom items")

class Sections(BaseModel):
    summary: Optional[SummarySection] = Field(default=SummarySection, description="Summary section")
    skills: Optional[SkillsSection] = Field(default=SkillsSection, description="Skills section")
    experience: Optional[ExperienceSection] = Field(default=ExperienceSection, description="Experience section")
    education: Optional[EducationSection] = Field(default=EducationSection, description="Education section")
    certifications: Optional[CertificationsSection] = Field(default=CertificationsSection, description="Certifications section")
    projects: Optional[ProjectsSection] = Field(default=ProjectsSection, description="Projects section")
    publications: Optional[PublicationsSection] = Field(default=PublicationsSection, description="Publications section")
    volunteer: Optional[VolunteerSection] = Field(default=VolunteerSection, description="Volunteer section")
    languages: Optional[LanguagesSection] = Field(default=LanguagesSection, description="Languages section")
    interests: Optional[InterestsSection] = Field(default=InterestsSection, description="Interests section")
    awards: Optional[AwardsSection] = Field(default=AwardsSection, description="Awards section")
    references: Optional[ReferencesSection] = Field(default=ReferencesSection, description="References section")
    profile: Optional[ProfileSection] = Field(default=ProfileSection, description="Profile section")
    custom: Optional[dict] = Field(default={}, description="Custom section")

class CssMetadata(BaseModel):
    value: str = Field(default=".section {\n\toutline: 1px solid #000;\n\toutline-offset: 4px;\n}", description="Custom CSS value")
    visible: bool = Field(default=False, description="Whether CSS is visible")

class PageOptions(BaseModel):
    breakLine: bool = Field(default=True, description="Whether to break line")
    pageNumbers: bool = Field(default=True, description="Whether to show page numbers")

class PageMetadata(BaseModel):
    format: str = Field(default="a4", description="Page format")
    margin: int = Field(default=14, description="Page margin")
    options: PageOptions = Field(default_factory=PageOptions, description="Page options")

class ThemeMetadata(BaseModel):
    text: str = Field(default="#000000", description="Text color")
    primary: str = Field(default="#ca8a04", description="Primary color")
    background: str = Field(default="#ffffff", description="Background color")

class FontMetadata(BaseModel):
    size: int = Field(default=13, description="Font size")
    family: str = Field(default="Merriweather", description="Font family")
    subset: str = Field(default="latin", description="Font subset")
    variants: List[str] = Field(default=["regular"], description="Font variants")

class TypographyMetadata(BaseModel):
    font: FontMetadata = Field(default_factory=FontMetadata, description="Font metadata")
    hideIcons: bool = Field(default=False, description="Whether to hide icons")
    lineHeight: float = Field(default=1.75, description="Line height")
    underlineLinks: bool = Field(default=True, description="Whether to underline links")

class Metadata(BaseModel):
    css: CssMetadata = Field(default_factory=CssMetadata, description="CSS metadata")
    page: PageMetadata = Field(default_factory=PageMetadata, description="Page metadata")
    notes: str = Field(default="NextCareer", description="Additional notes")
    theme: ThemeMetadata = Field(default_factory=ThemeMetadata, description="Theme metadata")
    layout: List[List[List[str]]] = Field(
        default=[[["summary", "experience", "education", "publications", "projects", "volunteer", "references"],
                 ["profiles", "skills", "certifications", "interests", "languages", "awards"]]],
        description="Layout configuration for resume sections"
    )
    template: str = Field(default="glalie", description="Resume template name")
    typography: TypographyMetadata = Field(default_factory=TypographyMetadata, description="Typography metadata")

class ResumeSchema(BaseModel):
    basics: Optional[Basics] = Field(default=None, description="Basic resume information")
    sections: Optional[Sections] = Field(default=None, description="Resume sections")
    metadata: Optional[Metadata] = Field(default=None, description="Resume metadata")

class ResumeResponse(BaseModel):
    error: int = Field(default=0, description="Error code, 0 means no error")
    message: str = Field(default="Success", description="Response message")
    data: ResumeSchema = Field(default_factory=ResumeSchema, description="Resume data")




class CFG:
    prompt_basic_info = \
"""
Action: Extract the resume info below into only a JSON (Expecting property name enclosed in double quotes) with exactly the following format (If any fields cannot be found in the resume, please fill them with "N/A" instead of blank string "" or 0). Return a response with json format.
Context: A resume from a person work in Health Care domain.
There are possible abbreviations in the resume such as:
- RN: Registered Nurse
- BSN: Bachelor of Science in Nursing
- MSN: Master of Science in Nursing
- NP: Nurse Practitioner
- DNP: Doctor of Nursing Practice
- APRN: Advanced Practice Registered Nurse
- CRNA: Certified Registered Nurse Anesthetist
- CNM: Certified Nurse Midwife
- CNS: Clinical Nurse Specialist
- CNA: Certified Nursing Assistant
- LPN: Licensed Practical Nurse
- LVN: Licensed Vocational Nurse
- CMA: Certified Medical Assistant
- CNA: Certified Nursing Assistant
- CMA: Certified Medical Assistant
- BLS: Basic Life Support
- ACLS: Advanced Cardiac Life Support
- CVICU: Cardiovascular Intensive Care Unit
- SICU: Surgical Intensive Care Unit
- MICU: Medical Intensive Care Unit
- EHR: Electronic Health Record
- NREMT: National Registry of Emergency Medical Technicians
-----------
Json format:
{
"basic_information":
{"first_name",
"last_name",
"full_name",
"email",
"phone_number" (format: (XXX)-XXX-XXXX),
"has_compact_license" (possible values: "Yes", "No", "No Information"),
"date_of_birth" (format: yyyy-MM-dd)
},
"worker_address":
{"street",
"city",
"state" (2 uppercase characters),
"zipcode"}
}
-----------
TEXT:
"""

    prompt_relation_info = \
"""
Action: Extract the resume info (contain information of worker in healthcare domain) below into only a JSON (Expecting property name enclosed in double quotes) with exactly the following format (If any fields cannot be found in the resume, please fill them with "N/A" instead of blank string "" or 0).Return a response with json format.
Context: A resume from a person work in Health Care domain.
There are possible abbreviations in the resume such as:
- RN: Registered Nurse
- BSN: Bachelor of Science in Nursing
- MSN: Master of Science in Nursing
- NP: Nurse Practitioner
- DNP: Doctor of Nursing Practice
- APRN: Advanced Practice Registered Nurse
- CRNA: Certified Registered Nurse Anesthetist
- CNM: Certified Nurse Midwife
- CNS: Clinical Nurse Specialist
- CNA: Certified Nursing Assistant
- LPN: Licensed Practical Nurse
- LVN: Licensed Vocational Nurse
- CMA: Certified Medical Assistant
- CNA: Certified Nursing Assistant
- CMA: Certified Medical Assistant
- BLS: Basic Life Support
- ACLS: Advanced Cardiac Life Support
- CVICU: Cardiovascular Intensive Care Unit
- SICU: Surgical Intensive Care Unit
- MICU: Medical Intensive Care Unit
- EHR: Electronic Health Record
- NREMT: National Registry of Emergency Medical Technicians
-----------
Json format:
{
"work_experiences" (array of objects):
[
{
"title",
"facility_name",
"facility_state" (2 uppercase characters),
"country",
"facility_city",
"currently_employed" (boolean) (must be values: "yes","no"),
"start_year" (return null if cannot find in RESUME),
"start_month" (return null if cannot find in RESUME),
"start_day" (return null if cannot find in RESUME),
"end_year" (return null if cannot find in RESUME),
"end_month" (return null if cannot find in RESUME),
"end_day" (return null if cannot find in RESUME),
}
],
"worker_educations" (array of objects):
[
{
"major",
"school_name",
"school_state" (2 uppercase characters),
"school_country",
"degree_name",
"start_year" (return null if cannot find in RESUME),
"start_month" (return null if cannot find in RESUME),
"start_day" (return null if cannot find in RESUME),
"end_year" (return null if cannot find in RESUME),
"end_month" (return null if cannot find in RESUME),
"end_day" (return null if cannot find in RESUME)
}
],
"worker_references" (array of objects):
[
{
"facility_name",
"contact_email",
"contact_phone" (format: (XXX)-XXX-XXXX),
"contact_first_name",
"contact_last_name",
"job_title",
}
],
"certifications" (array of objects):
[
{
"title",
"expire_year" (return null if cannot find in RESUME),
"expire_month" (return null if cannot find in RESUME),
"expire_day" (return null if cannot find in RESUME)
}
]
}
-----------
RESUME:
"""

    new_prompt_basic_info = """
    You are an expert resume parser that extracts comprehensive information from resumes. Your task is to analyze the provided resume text and extract ALL relevant information into a structured JSON format. Follow these guidelines:

    1. Extract ALL information present in the resume, not just the most obvious parts
    2. Pay special attention to:
       - All work experiences, including internships and part-time jobs
       - All educational qualifications, including certifications and training
       - All skills mentioned, including technical and soft skills
       - All projects, achievements, and accomplishments
       - All languages and proficiency levels
       - All certifications and licenses
       - All publications, presentations, or research work
       - All volunteer work and community service
       - All awards and recognition
       - All professional memberships and affiliations
       - All references and recommendations
       - All interests and hobbies
       - All custom sections or additional information

    3. For each section:
       - Include complete details, not just summaries
       - Preserve specific dates, locations, and durations
       - Maintain the original wording where appropriate
       - Include all bullet points and achievements
       - Keep all technical terms and industry-specific language

    4. For dates:
       - Convert all dates to a consistent format
       - Include both start and end dates when available
       - Use "Present" for current positions
       - Preserve the original date format if specific

    5. For locations:
       - Include complete address information when available
       - Use standard state/province abbreviations
       - Include country information
       - Preserve specific location details

    6. For skills and qualifications:
       - List all technical skills with proficiency levels
       - Include all software, tools, and technologies
       - List all certifications and licenses
       - Include all languages and proficiency levels
       - List all soft skills and competencies

    7. For work experience:
       - Include complete job descriptions
       - List all responsibilities and achievements
       - Include all projects and initiatives
       - List all technologies and tools used
       - Include all team sizes and budgets managed
       - List all metrics and results achieved

    8. For education:
       - Include all degrees and qualifications
       - List all majors and minors
       - Include all relevant coursework
       - List all academic achievements
       - Include all research work
       - List all relevant projects

    9. For projects:
       - Include complete project descriptions
       - List all technologies used
       - Include all team roles and responsibilities
       - List all achievements and outcomes
       - Include all relevant metrics

    10. For URLs and links:
        - Include all professional profiles
        - List all portfolio links
        - Include all publication links
        - List all project links
        - Include all certification links

    Return the information in the following JSON format:

    {
      "basics": {
        "fullname": "The person's full name",
        "headline": "Professional headline or title",
        "email": "Email address",
        "phone": "Phone number with format",
        "location": "City, State, ZIP code",
        "url": {
          "label": "Website label (default: empty string)",
          "href": "Website URL (always starts with https://), default: empty string"
        },
        "customFields": [
          {
            "id": "Unique UUID identifier for the custom field",
            "icon": "Name of the icon for the custom field",
            "name": "Name of the custom field",
            "value": "Value of the custom field"
          }
        ],
        "picture": {
          "url": "URL of the profile picture, default: https://i.imgur.com/HgwyOuJ.jpg",
          "size": "Integer. Size of the profile picture, default: 64",
          "aspectRatio": "Integer. Aspect ratio of the profile picture, default: 1",
          "borderRadius": "Integer. Border radius of the profile picture, default: 0",
          "effects": {
            "hidden": "Boolean. Whether the profile picture is hidden, default: false",
            "border": "Boolean. Whether the profile picture has a border, default: false",
            "grayscale": "Boolean. Whether the profile picture is grayscale, default: false"
          }
        }
      },
      "metadata": {
        "template": "String. Template name, default: gengar",
        "layout": [
          [
            [
              "profiles",
              "summary",
              "experience",
              "volunteer",
              "references",
              "projects"
            ],
            [
              "skills",
              "education",
              "interests",
              "certifications",
              "awards",
              "publications",
              "languages"
            ]
          ]
        ],
        "css": {
          "value": "String. Custom CSS code",
          "visible": "Boolean. Whether to show custom CSS, default: false"
        },
        "page": {
          "margin": "Integer. Page margin in mm, default: 12",
          "format": "String. Page format (letter, a4, etc.), default: letter",
          "options": {
            "breakLine": "Boolean. Whether to break lines between sections, default: true",
            "pageNumbers": "Boolean. Whether to show page numbers, default: true"
          }
        },
        "theme": {
          "background": "String. Background color in hex, default: #ffffff",
          "text": "String. Text color in hex, default: #000000",
          "primary": "String. Primary color in hex, default: #57534e"
        },
        "typography": {
          "font": {
            "family": "String. Font family name, default: Lora",
            "subset": "String. Font subset, default: latin",
            "variants": [
              "String. Font variant, default: regular"
            ],
            "size": "Integer. Font size in pt, default: 12"
          },
          "lineHeight": "Float. Line height multiplier, default: 1.45",
          "hideIcons": "Boolean. Whether to hide icons, default: false",
          "underlineLinks": "Boolean. Whether to underline links, default: false"
        },
        "notes": "String. Notes about the resume, default: empty string"
      },
      "sections": {
        "summary": {
          "name": "Name of the section, default: Summary",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "summary",
          "content": "Content of the section"
        },
        "profiles": {
          "name": "Name of the section, default: Profiles",
          "id": "profiles",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "items": [
            {
              "name": "String. Profile name, default: linkedin",
              "id": "profile_1",
              "visible": "Boolean. Whether to show the item, always default: true",
              "network": "String. Network of the profile,always default: linkedin",
              "icon": "String. Icon of the profile, default: linkedin",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the profile (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "skills": {
          "name": "name of the section, default: Skills",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "skills",
          "items": [
            {
              "id": "skill_1",
              "name": "Skill category (e.g., Programming Languages, Tools), default: My Skill Category",
              "description": "String. Proficiency level (e.g., Advanced, Intermediate), default: My Proficiency Level",
              "keywords": ["String. Specific skill 1", "String. Specific skill 2", "..."],
              "level": "Integer. Level of the skill, default: 0",
              "visible": "Boolean. Whether to show the item, always default: true"
            }
          ]
        },
        "experience": {
          "name": "Name of the section, default: Experience",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "experience",
          "items": [
            {
              "id": "experience_1",
              "company": "Company name, default: My Company",
              "position": "Job title, default: My Job Title",
              "location": "Job location, default: My Location",
              "date": "Employment period, default: My Employment Period",
              "summary": "Job description and achievements, default: My Job Description and Achievements",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the experience (always starts with https://), default: empty string"
              },
              "visible": "Boolean. Whether to show the item, always default: true"
            }
          ]
        },
        "education": {
          "name": "Name of the section, default: My Education",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "education",
          "items": [
            {
              "id": "education_1",
              "institution": "School or university name, default: My University",
              "studyType": "Degree type, default: My Degree Type",
              "visible": "Boolean. Whether to show the item, always default: true",
              "score": "String. Score of the education, default: 4.0",
              "area": "Field of study, default: My Field of Study",
              "date": "Education period, default: My Education Period",
              "summary": "Summary of the education, default: My Summary of the Education",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the education (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "certifications": {
          "name": "Name of the section, default: Certifications",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "certifications",
          "items": [
            {
              "id": "certification_1",
              "name": "String. Certification name, default: My Certification Name",
              "issuer": "String. Issuing organization, default: My Issuing Organization",
              "date": "String. Date obtained, default: My Date Obtained",
              "visible": "Boolean. Whether to show the item, always default: true",
              "summary": "String. Summary of the certification, default: My Summary of the Certification",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the certification (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "projects": {
          "name": "Name of the section, default: Projects",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "projects",
          "items": [
            {
              "id": "project_1",
              "name": "Project name, default: My Project Name",
              "visible": "Boolean. Whether to show the item, always default: true",
              "description": "Role or project type, default: My Role or Project Type",
              "summary": "Project description and achievements, default: My Project Description and Achievements",
              "date": "Project period, default: My Project Period",
              "keywords": ["Specific keyword 1", "Specific keyword 2", "..."],
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the project (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "publications": {
          "name": "Name of the section, default: My Publications",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "publications",
          "items": [
            {
              "id": "publication_1",
              "visible": "Boolean. Whether to show the item, always default: true",
              "name": "Publication title, default: My Publication Title",
              "publisher": "Publisher name, default: My Publisher Name",
              "date": "Publication date, default: My Publication Date",
              "summary": "Publication description, default: My Publication Description",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the publication (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "volunteer": {
          "name": "Name of the section, default: My Volunteer",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "volunteer",
          "items": [
            {
              "id": "volunteer_1",
              "visible": "Boolean. Whether to show the item, always default: true",
              "organization": "Organization name, default: My Organization Name",
              "position": "Volunteer position, default: My Volunteer Position",
              "date": "Volunteer period, default: My Volunteer Period",
              "summary": "Description of volunteer work, default: My Description of Volunteer Work",
              "location": "Location of the volunteer, default: empty string",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the volunteer (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "languages": {
          "name": "Name of the section, default: My Languages",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "languages",
          "items": [
            {
              "id": "language_1",
              "name": "Language name, default: My Language Name",
              "description": "Proficiency level, default: My Proficiency Level",
              "visible": "Boolean. Whether to show the item, always default: true"
            }
          ]
        },
        "interests": {
          "name": "Name of the section, default: My Interests",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "interests",
          "items": [
            {
              "id": "interest_1",
              "name": "Interest category, default: My Interest Category",
              "visible": "Boolean. Whether to show the item, always default: true",
              "keywords": ["Specific interest 1", "Specific interest 2", "..."]
            }
          ]
        },
        "awards": {
          "name": "Name of the section, default: My Awards",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "awards",
          "items": [
            {
              "id": "award_1",
              "title": "Award name, default: My Award Name",
              "awarder": "Awarding organization, default: My Awarding Organization",
              "date": "Date received, default: My Date Received",
              "summary": "Description of the award, default: My Description of the Award",
              "visible": "Boolean. Whether to show the item, always default: true",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the award (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "references": {
          "name": "Name of the section, default: My References",
          "columns": "Integer. Number of columns, must be greater than 0, default: 1",
          "separateLinks": "Boolean. Whether to separate links, default: true",
          "visible": "Boolean. Whether to show the section, default: true",
          "id": "references",
          "items": [
            {
              "id": "reference_1",
              "name": "Reference name or statement, default: My Reference Name or Statement",
              "visible": "Boolean. Whether to show the item, always default: true",
              "description": "Description of the reference, default: My Description of the Reference",
              "summary": "Summary of the reference, default: My Summary of the Reference",
              "url": {
                "label": "Label of the URL, default: empty string",
                "href": "URL of the reference (always starts with https://), default: empty string"
              }
            }
          ]
        },
        "custom": {} #empty object
      }
    }

    Important notes:
    1. Extract ALL information from the resume, not just the most obvious parts
    2. Maintain the original wording and terminology where appropriate
    3. Include complete details for each section
    4. Use default values only when information is truly missing
    5. Ensure all dates are in a consistent format
    6. Include all URLs and links found in the resume
    7. Preserve all technical terms and industry-specific language
    8. Include all achievements and accomplishments
    9. List all skills and qualifications
    10. Maintain the hierarchical structure of the JSON format

    If any information is not available in the resume, use the default values specified in the JSON structure.
    """


