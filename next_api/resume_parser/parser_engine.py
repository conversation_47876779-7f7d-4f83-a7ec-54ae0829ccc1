import io
import re
import json
import os
import types
import string
import random
import base64
from typing import Any
from typing import Optional

from sanic.log import logger
from pydantic import BaseModel, Field
from resume_parser.CFG import CFG, ResumeSchema
from groq import Groq
from uuid_extensions import uuid7
import docx
import fitz
import pdfplumber
import openai
from utils import llm_helpers
from PIL import Image

class ResumeJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that can handle mappingproxy objects and other special types."""
    def default(self, obj):
        if isinstance(obj, types.MappingProxyType):
            # Convert mappingproxy to dict
            return dict(obj)
        elif isinstance(obj, type):
            # If it's a class, return its name
            return obj.__name__
        elif hasattr(obj, "__dict__"):
            # For objects with __dict__, convert to dict
            return {k: v for k, v in obj.__dict__.items()
                   if not k.startswith("_")}
        elif isinstance(obj, (list, tuple)):
            # For lists and tuples
            return [item for item in obj]
        # Let the base class handle other types or raise TypeError
        return super().default(obj)


def clean_text(pdf_str: str):
    pdf_str = re.sub(r'\s[,.]', ',', pdf_str)
    # pdf_str = re.sub(r'[\n]+', '\n', pdf_str)
    pdf_str = re.sub(r'[\s]+', ' ', pdf_str)
    pdf_str = re.sub(r'http[s]?(://)?', '', pdf_str)
    pdf_str = re.sub(r'[\u0080-\uffff]', '', pdf_str)
    return pdf_str


def find_matching_words(pdf_content: str):
    word_collections = [
        'experiences', 'experience', 'summary', 'profile',
        'school', 'university', 'college', 'education',
        'references', 'reference',
        "skill", "skills", "language",
    ]
    matched_words = []
    for word in word_collections:
        pattern = re.compile(r'\b{}\b'.format(re.escape(word)), re.IGNORECASE)
        if re.search(pattern, pdf_content):
            matched_words.append(word)
    return matched_words


class PDFReader(BaseModel):
    need_clean: bool = True

    def read(self, resume_file, fast=False):
        text = ""
        try:
            if fast:
                text = self.extract_text_from_pdf(resume_file)
            else:
                text = self.read_pdf(resume_file)

        except Exception as e:
            print(f"Can't read file {resume_file.name}, cause: {str(e)}")
            return ""

        if self.need_clean:
            text = clean_text(text)

        return text

    def read_pdf(self, file):
        output_string = io.StringIO()
        with io.BytesIO(file) as f:
            # Open PDF with pdfplumber
            with pdfplumber.open(f) as pdf:
                for page in pdf.pages:
                    # Extract text from each page
                    page_text = page.extract_text(keep_blank_chars=True)
                    output_string.write(page_text)
                    output_string.write("\n---PAGE---\n")

        return output_string.getvalue()

    def extract_text_from_pdf(self, file_bytes):
        """Extract raw text content from pdf file

        Args:
            filename (_type_): _description_
        """
        pdf = fitz.open(stream=file_bytes, filetype="pdf")
        cv_text = ""
        for page in pdf:
            cv_text += page.get_text() + '\n'

        return cv_text

    def extract_text_from_doc(self, file):
        document = docx.Document(file)
        text = ""
        for p in document.paragraphs:
            text += "\n" + p.text

        return text

# Add UUID7 to every item in the JSON that has an 'id' field
def add_uuid_to_items(data):
    if isinstance(data, dict):
        # If this is a dict with an 'id' field that needs a UUID
        if 'id' in data and (data['id'] == '' or data['id'] is None):
            data['id'] = str(uuid7())

        # Process all values in the dict
        for key, value in data.items():
            data[key] = add_uuid_to_items(value)

        return data
    elif isinstance(data, list):
        # Process all items in the list
        return [add_uuid_to_items(item) for item in list(data)]
    else:
        # Return primitive values as is
        return data

class ParserEngine(BaseModel):
    openai_api_key: str
    groq_api_key: str
    org: Optional[str] = None
    groq_model: str = llm_helpers.get_available_groq_model('llama-3.3-70b-versatile')
    general_description: str = (
        "Extract the resume info (contain information of worker) "
        "below into only a JSON"
        "with exactly the following format (If any fields cannot be found in the resume, "
        "please leave them default value). Do not return empty objects in the JSON."
    )
    prompt_basic_info: str = CFG.prompt_basic_info
    new_prompt_basic_info: str = CFG.new_prompt_basic_info
    # prompt_relation_info: str = CFG.prompt_relation_info
    # function_calling: list = CFG.function_calling
    resume_schema: BaseModel = Field(default_factory=ResumeSchema)
    # Check for missing required fields and add default values
    required_fields: dict = {
        "basics": {
            "fullname": "Your Full Name here",
            "headline": "Your Job role here",
            "email": "",
            "phone": "+84912123456",
            "location": "Your Location here",
            "url": {"label": "", "href": ""},
            "customFields": [],
            "picture": {
                "url": "https://i.imgur.com/HgwyOuJ.jpg",
                "size": 64,
                "aspectRatio": 1,
                "borderRadius": 0,
                "effects": {
                    "hidden": False,
                    "border": False,
                    "grayscale": False
                }
            }
        },
        "sections": {
            "summary": {
                "name": "Summary",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "summary",
                "content": "Next Career is a platform that helps you achieve your dream job."
            },
            "profiles": {
                "name": "Profiles",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "profiles",
                "items": [
                    {
                        "id": ''.join(random.choices(string.ascii_letters + string.digits, k=24)),
                        "visible": True,
                        "network": "linkedin",
                        "icon": "linkedin",
                        "url": {
                            "label": "",
                            "href": "https://linkedin.com"
                        }
                    }
                ]
            },
            "skills": {
                "name": "Skills",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "skills",
                "items": []
            },
            "education": {
                "name": "Education",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "education",
                "items": []
            },
            "experience": {
                "name": "Experience",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "experience",
                "items": []
            },
            "awards": {
                "name": "Awards",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "awards",
                "items": [
                ]
            },
            "projects": {
                "name": "Projects",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "projects",
                "items": []
            },
            "volunteer": {
                "name": "Volunteering",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "volunteer",
                "items": [
                ]
            },
            "interests": {
                "name": "Interests",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "interests",
                "items": [
                ]
            },
            "certifications": {
                "name": "Certifications",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "certifications",
                "items": [
                ]
            },
            "references": {
                "name": "References",
                "columns": 1,
                "separateLinks": True,
                "visible": False,
                "id": "references",
                "items": [
                ]
            },
            "languages": {
                "name": "Languages",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "languages",
                "items": [
                ]
            },
            "publications": {
                "name": "Publications",
                "columns": 1,
                "separateLinks": True,
                "visible": True,
                "id": "publications",
                "items": [
                ]
            },
            "custom": {}
        },
        "metadata": {
            "template": "gengar",
            "layout": [
                [
                    ["profiles", "summary", "experience", "volunteer", "references", "projects"],
                    ["skills", "education", "interests", "certifications", "awards", "publications", "languages"]
                ]
            ],
            "css": {
                "value": "",
                "visible": False
            },
            "page": {
                "margin": 12,
                "format": "letter",
                "options": {
                    "breakLine": True,
                    "pageNumbers": True
                }
            },
            "theme": {
                "background": "#ffffff",
                "text": "#000000",
                "primary": "#57534e"
            },
            "typography": {
                "font": {
                    "family": "Lora",
                    "subset": "latin",
                    "variants": ["regular"],
                    "size": 12
                }
            }
        }
    }

    def __init__(self, **data: Any) -> None:
        super().__init__(**data)
        openai.api_key = self.openai_api_key

        if self.org:
            openai.organization = self.org
        # del self.openai_api_key, self.org

    def word_count(self, text: str) -> int:
        """
        Returns the total number of words in the given text.
        """
        words = text.split()
        return len(words)

    def pdf2string(self: object, pdf_contents: bytes) -> str:
        # pdf2text
        pdf_reader = PDFReader(need_clean=True)
        cv_content = pdf_reader.read(pdf_contents, fast=False)
        return cv_content

    # Helper function to check and replace nextcareer.ai values
    def replace_nextcareer_value(self, value):
        if isinstance(value, str) and value == "https://nextcareer.ai":
            return " "
        if isinstance(value, str) and "Next Career is a platform that helps you achieve your dream job." in value:
            return " "
        return value

    def ensure_required_fields(self, data, template, path=""):
        if isinstance(template, dict) and isinstance(data, dict):
            for key, default_value in template.items():
                current_path = f"{path}.{key}" if path else key

                # If key doesn't exist in data, add it with default value
                if key not in data or data[key] is None:
                    logger.info(f"Adding missing required field: {current_path}")
                    data[key] = default_value
                    # Set visible to false for default values
                    if isinstance(default_value, dict) and 'visible' in default_value:
                        default_value['visible'] = False

                # Replace nextcareer.ai values
                if isinstance(data[key], str) and key != "href":
                    data[key] = self.replace_nextcareer_value(data[key])

                # If key exists but is a container type, recurse
                elif isinstance(default_value, (dict, list)) and isinstance(data[key], (dict, list)):
                    self.ensure_required_fields(data[key], default_value, current_path)

        elif isinstance(template, list) and isinstance(data, list):
            # For lists, we don't add default items, but we ensure each item has required structure
            if template and data and isinstance(template[0], dict):
                template_item = template[0]
                for i, item in enumerate(data):
                    if isinstance(item, dict):
                        # Ensure 'visible' field exists
                        if 'visible' not in item:
                            logger.info(f"Adding missing 'visible' field to item: {path}[{i}]")
                            item['visible'] = True

                        # Ensure 'url' field exists
                        if 'url' not in item:
                            logger.info(f"Adding missing 'url' field to item: {path}[{i}]")
                            item['url'] = {
                                "label": "",
                                "href": "",
                                "visible": False
                            }

                        # Replace nextcareer.ai values in dict items
                        for key, value in item.items():
                            if key == "url":
                                if "href" in value:
                                    if value["href"] == "https://nextcareer.ai":
                                        value["visible"] = False
                                        value["href"]=""
                                    # elif value["href"][:5] != "https":
                                    #     value["visible"] = False
                                    #     value["href"] = "https://." + value["href"]
                                # Ensure url has visible field
                                if "visible" not in value:
                                    value["visible"] = False
                            elif isinstance(value, str) and key != "href":
                                item[key] = self.replace_nextcareer_value(value)
                            elif isinstance(value, dict):
                                for sub_key, sub_value in value.items():
                                    if isinstance(sub_value, str) and sub_key != "href":
                                        value[sub_key] = self.replace_nextcareer_value(sub_value)

                        self.ensure_required_fields(item, template_item, f"{path}[{i}]")
                        # Check if item matches default values
                        is_default = True
                        for key, value in template_item.items():
                            if key not in item or item[key] != value:
                                is_default = False
                                break
                        # Set visible to false for default items
                        if is_default and 'visible' in item:
                            item['visible'] = False

        if path == "basics" and isinstance(data, dict) and isinstance(template, dict):
            # Ensure url exists in basics
            if 'url' not in data:
                data['url'] = {
                    "label": "",
                    "href": "",
                    "visible": False
                }

            if 'url' in data:
                # Ensure url has visible field
                if 'visible' not in data['url']:
                    data['url']['visible'] = False

                if 'href' in data['url']:
                    # item['url']['href'] = self.replace_nextcareer_value(item['url']['href'])
                    if data['url']['href'] == "https://nextcareer.ai":
                        data['url']['href']=""
                        data['url']['visible'] = False
                    # elif data['url']['href'][:5] != "https":
                    #     data['url']['visible'] = False
                    #     data['url']['href'] = "https://." + data['url']['href']
                if 'label' in data['url']:
                    data['url']['label'] = self.replace_nextcareer_value(data['url']['label'])

        # Special handling for sections to ensure all required section objects exist
        if path == "sections" and isinstance(data, dict) and isinstance(template, dict):
            required_sections = [
                "awards",
                "volunteer",
                "interests",
                "languages",
                "publications",
                "references",
                "certifications",
                "projects",
                "experience",
                "skills",
                "education"
            ]

            if 'summary' in data and data['summary']['columns'] <= 0:
                data['summary']['columns'] = 1

            for section in required_sections:
                if 'columns' in data[section]:
                    # Ensure columns is always > 0
                    if data[section]['columns'] <= 0:
                        data[section]['columns'] = 1
                if 'items' not in data[section] or data[section]['items'] == []:
                    logger.info(f"Adding missing required section: {section}")
                    if section in template:
                        data[section]['items'] = template[section]['items']
                        # Set visible to false for default items
                        for item in data[section]['items']:
                            if 'visible' in item:
                                item['visible'] = False
                            # Ensure url exists
                            if 'url' not in item:
                                item['url'] = {
                                    "label": "",
                                    "href": "",
                                    "visible": False
                                }
                            # Replace nextcareer.ai values in items
                            for key, value in item.items():
                                if isinstance(value, str) and key != "href":
                                    item[key] = self.replace_nextcareer_value(value)
                                elif isinstance(value, dict):
                                    for sub_key, sub_value in value.items():
                                        if isinstance(sub_value, str) and sub_key != "href":
                                            value[sub_key] = self.replace_nextcareer_value(sub_value)
                                if key == "url":
                                    # Ensure url has visible field
                                    if "visible" not in value:
                                        value["visible"] = False
                                    if "href" in value:
                                        if value["href"] == "https://nextcareer.ai":
                                            value["visible"] = False
                                            value["href"]=""
                                        # elif value["href"][:5] != "https":
                                        #     value["visible"] = False
                                        #     value["href"] = "https://." + value["href"]
                for item in data[section]['items']:
                    # Ensure visible field exists
                    if 'visible' not in item:
                        item['visible'] = True

                    # Ensure url exists
                    if 'url' not in item:
                        item['url'] = {
                            "label": "",
                            "href": "",
                            "visible": False
                        }

                    # Replace nextcareer.ai values in items
                    for key, value in item.items():
                        if isinstance(value, str) and key != "href":
                            item[key] = self.replace_nextcareer_value(value)
                        elif isinstance(value, dict):
                            for sub_key, sub_value in value.items():
                                if isinstance(sub_value, str) and sub_key != "href":
                                    value[sub_key] = self.replace_nextcareer_value(sub_value)
                                if sub_key == "url":
                                    # Ensure url has visible field
                                    if "visible" not in value:
                                        value["visible"] = False
                                    if "href" in value:
                                        if value["href"] == "https://nextcareer.ai":
                                            value["visible"] = False
                                            value["href"]=""
                                        # elif value["href"][:5] != "https":
                                        #     value["visible"] = False
                                        #     value["href"] = "https://." + value["href"]
                    if 'url' in item:
                        # Ensure url has visible field
                        if 'visible' not in item['url']:
                            item['url']['visible'] = False

                        if 'href' in item['url']:
                            # item['url']['href'] = self.replace_nextcareer_value(item['url']['href'])
                            if item['url']['href'] == "https://nextcareer.ai":
                                item['url']['visible'] = False
                                item['url']['href']=""
                            # elif item['url']['href'][:5] != "https":
                            #     item['url']['visible'] = False
                            #     item['url']['href'] = "https://." + item['url']['href']
                        if 'label' in item['url']:
                            item['url']['label'] = self.replace_nextcareer_value(item['url']['label'])

                if section not in data or data[section] is None or data[section] == {}:
                    logger.info(f"Adding missing required section: {section}")
                    # If the section exists in template, use that structure
                    if section in template:
                        data[section] = template[section]
                        # Set visible to false for default section
                        if 'visible' in data[section]:
                            data[section]['visible'] = False
                        # Ensure columns is always > 0
                        if 'columns' in data[section] and data[section]['columns'] <= 0:
                            data[section]['columns'] = 1
                    else:
                        # Create a default section structure if not in template
                        data[section] = {
                            "name": section.capitalize(),
                            "columns": 1,  # Default to 1 column to ensure it's > 0
                            "separateLinks": True,
                            "visible": False,  # Set visible to false for default section
                            "id": section,
                            "items": [{
                                "id": ''.join(random.choices(string.ascii_letters + string.digits, k=24)),
                                "visible": False,  # Set visible to false for default item
                                "name": "A very important project",
                                "description": "A very important description",
                                "date": "",
                                "summary": " ",  # Set empty space for default summary
                                "keywords": [],
                                "url": {
                                    "label": "",  # Set empty space for default label
                                    "href": "",   # Set empty space for default href
                                    "visible": False  # Set visible to false for default URL
                                }
                            }]
                        }
                        # Ensure the 'visible' field is present for the section
                        if 'visible' not in data[section]:
                            logger.info(f"Adding missing 'visible' field to section: {section}")
                            data[section]['visible'] = False  # Set visible to false for default section
    def query_resume_langchain(
        self: object, pdf_contents: str
    ) -> dict:
        groq_client: Groq = Groq(api_key=self.groq_api_key)
        response = groq_client.chat.completions.create(
            model=self.groq_model,
            messages=[
                {"role": "system", "content": self.new_prompt_basic_info},
                {"role": "user", "content": "Extract the resume info (contain information of worker) "
        "below into only a JSON"
        "with exactly the following format (If any fields cannot be found in the resume, "
        "please leave them default value). Do not return empty objects in the JSON. Here is the content: \n" +pdf_contents}
            ],
            stream=False,
            seed=23,
            temperature=0.11,
            top_p=1,
            presence_penalty=0,
            response_format={"type": "json_object"}
        )
        try:
            # Parse the response content as JSON first
            content_json = json.loads(response.choices[0].message.content)

            # Validate against schema
            try:
                adjustment_content = ResumeSchema.model_validate(content_json)
                result_json = json.loads(adjustment_content.model_dump_json())
            except Exception as e:
                logger.error(f"Schema validation error: {str(e)}")
                # Fallback to direct use if validation fails
                result_json = content_json
            # Format all summary fields with HTML formatting using Groq
            try:
                # Function to recursively find and format all summary fields in the JSON
                def format_summary_fields(data, LLM_FORMAT_MODEL):
                    if isinstance(data, dict):
                        # Process this dict
                        for key, value in list(data.items()):  # Use list() to avoid modification during iteration
                            if key == "summary" and isinstance(value, str) and len(value) > 10 and 'nextcareer.ai' not in value:
                                # Format this summary field
                                format_response = groq_client.chat.completions.create(
                                    model=LLM_FORMAT_MODEL,
                                    messages=[
                                        {"role": "system", "content": "You are an expert at formatting plain text into well-structured HTML. Convert the given text into HTML format with appropriate tags like <p>, <ul>, <li>, <strong>, <em>, etc. Create logical paragraph breaks, bullet points for lists, and emphasize important information. Make the content more readable and professional. Do not edit, change or modify the content, just format it."},
                                        {"role": "user", "content": f"Format this text into professional HTML with appropriate structure, keep content as it is, do not edit, change or modify the content, just format it:\n\n{value}"}
                                    ],
                                    temperature=0.2,
                                    top_p=1,
                                    seed=4,
                                    stream=False
                                )
                                formatted_summary = format_response.choices[0].message.content.strip()
                                # Clean up any markdown code blocks or extra quotes
                                if formatted_summary.startswith("```html"):
                                    formatted_summary = formatted_summary.replace("```html", "").replace("```", "").strip()
                                data[key] = formatted_summary
                                logger.info(f"Formatted a summary field for data: {data['id']}")
                                logger.info("-"*20)
                            elif isinstance(value, (dict, list)):
                                # Recursively process nested structures
                                format_summary_fields(value, LLM_FORMAT_MODEL)
                    elif isinstance(data, list):
                        # Process each item in the list
                        for item in data:
                            format_summary_fields(item, LLM_FORMAT_MODEL)

                # Special case for the main summary section content
                if "sections" in result_json and "summary" in result_json["sections"] and "content" in result_json["sections"]["summary"]:
                    content = result_json["sections"]["summary"]["content"]
                    if isinstance(content, str) and len(content) > 10 and 'nextcareer.ai' not in content:
                        format_response = groq_client.chat.completions.create(
                            model=llm_helpers.get_available_groq_model(),
                            messages=[
                                {"role": "system", "content": "You are an expert at formatting plain text into well-structured HTML. Convert the given text into HTML format with appropriate tags like <p>, <ul>, <li>, <strong>, <em>, etc. Create logical paragraph breaks, bullet points for lists, and emphasize important information. Make the content more readable and professional. Do not edit, change or modify the content, just format it."},
                                {"role": "user", "content": f"Format this resume summary into professional HTML:\n\n{content}"}
                            ],
                            temperature=0.2,
                            seed=4,
                            top_p=1,
                            stream=False
                        )
                        formatted_content = format_response.choices[0].message.content.strip()
                        # Clean up any markdown code blocks or extra quotes
                        if formatted_content.startswith("```html"):
                            formatted_content = formatted_content.replace("```html", "").replace("```", "").strip()
                        result_json["sections"]["summary"]["content"] = formatted_content
                        logger.info("Formatted main summary content")

                # Process the entire result_json to find all summary fields
                LLM_FORMAT_MODEL = llm_helpers.get_available_groq_model()
                logger.info(f"LLM_FORMAT_MODEL: {LLM_FORMAT_MODEL}")
                format_summary_fields(result_json, LLM_FORMAT_MODEL)

                logger.info("Successfully formatted all summary fields with HTML")
            except Exception as format_error:
                logger.error(f"Error formatting summaries with HTML: {str(format_error)}")
                # Continue with unformatted summaries if formatting fails
                pass
            # Apply the check to ensure all required fields exist
            self.ensure_required_fields(result_json, self.required_fields)
            result_json["metadata"]= self.required_fields["metadata"]
            return result_json
        except Exception as e:
            logger.error(f"Error processing resume JSON: {str(e)}")
            logger.error(f"Response content: {response.choices[0].message.content}")
            raise ValueError(f"Error serializing to JSON: {str(e)}")

    def get_pdf_pages_image(self, pdf_doc):
        # Open PDF from file path
        # pdf_doc = fitz.open(pdf_path)

        # Get dimensions from first page to set the combined image size
        first_page = pdf_doc[0]
        first_pix = first_page.get_pixmap()
        width = first_pix.width
        height = first_pix.height * len(pdf_doc)

        # Create a new image to hold all pages
        combined_image = Image.new("RGB", (width, height))

        # Process each page and add to the combined image
        for i in range(len(pdf_doc)):
            page = pdf_doc[i]
            pix = page.get_pixmap()
            img_data = pix.tobytes("png")
            page_image = Image.open(io.BytesIO(img_data))

            # Paste this page into the combined image
            combined_image.paste(page_image, (0, i * first_pix.height))

        return combined_image

    def parse_resume_from_pdf(self, pdf_bytes):
        """
        Parse a resume from a PDF file and return structured JSON data.

        Args:
            pdf_bytes (bytes): PDF file content as bytes

        Returns:
            dict: Structured resume data in JSON format
        """
        logger.info("Starting resume parsing from PDF bytes")

        # Get image from PDF
        logger.info("Converting PDF to image")
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        front_image = self.get_pdf_pages_image(pdf_doc)

        # Create a temporary file path
        logger.info("Creating temporary image file")
        temp_dir = os.path.dirname(os.path.abspath(__file__))
        temp_image_path = os.path.join(temp_dir, f"temp_resume_{uuid7()}.png")
        front_image.save(temp_image_path)
        logger.info(f"Saved temporary image to: {temp_image_path}")

        # Create prompt for resume parsing
        logger.info("Creating prompt for resume parsing")
        prompt = f"""
        {self.new_prompt_basic_info}

        Please analyze the resume image provided and extract all relevant information according to the guidelines above.
        Return the structured data in the specified JSON format.

        Important validation requirements:
        1. All section items must have a non-empty 'name' field
        2. All items in 'projects' section must have a 'summary' field (even if it's just a brief description)
        3. All integers in the JSON must be greater than 0
        4. Make sure you follow the format strictly
        5. Ensure all JSON fields are properly formatted with correct syntax
        6. Do not include any invalid characters in the JSON output
        """

        # Read the image file and convert to base64
        logger.info("Converting image to base64")
        with open(temp_image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        # Make API call to Groq
        logger.info("Making API call to Groq for resume parsing")
        groq_client = Groq(api_key=self.groq_api_key)
        response = groq_client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=[
                {
                    "role": "system",
                    "content": prompt
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Please help me analyze and extract the image content to a json format."},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                    ]
                }
            ],
            response_format={"type": "json_object"}
        )
        logger.info("Received response from Groq API")

        # Clean up temporary file
        try:
            logger.info(f"Cleaning up temporary image file: {temp_image_path}")
            os.remove(temp_image_path)
        except Exception as e:
            logger.warning(f"Failed to remove temporary file: {str(e)}")

        # Extract the parsed resume data
        try:
            logger.info("Parsing JSON response from Groq")
            response_content = response.choices[0].message.content

            # Validate JSON before parsing
            try:
                # First check if the response is valid JSON
                resume_data = json.loads(response_content)
                logger.info("Successfully parsed JSON response")
            except json.JSONDecodeError as json_err:
                logger.error(f"Invalid JSON response: {str(json_err)}")
                logger.error(f"Response content: {response_content}")
                raise ValueError(f"Error parsing JSON response: {str(json_err)}")

            # Pre-validation fixes for common issues
            if "sections" in resume_data:
                # Fix for interests section
                if "interests" in resume_data["sections"] and "items" in resume_data["sections"]["interests"]:
                    for item in resume_data["sections"]["interests"]["items"]:
                        if "name" not in item or not item["name"]:
                            item["name"] = "Interest" # Default placeholder

                # Fix for projects section
                if "projects" in resume_data["sections"] and "items" in resume_data["sections"]["projects"]:
                    for item in resume_data["sections"]["projects"]["items"]:
                        if "summary" not in item or not item["summary"]:
                            # Create a summary from other fields if available
                            if "description" in item and item["description"]:
                                item["summary"] = item["description"]
                            elif "name" in item and item["name"]:
                                item["summary"] = f"Project involving {item['name']}"
                            else:
                                item["summary"] = "Project details"

            # Validate against schema
            try:
                logger.info("Validating resume data against schema")
                adjustment_content = ResumeSchema.model_validate(resume_data)
                result_json = adjustment_content.model_dump(mode="json", exclude_unset=True)
                logger.info("Schema validation successful")
            except Exception as e:
                logger.error(f"Schema validation error: {str(e)}")
                logger.info("Falling back to direct use of resume data")
                result_json = resume_data

            # Format all summary fields with HTML formatting using Groq
            try:
                logger.info("Starting HTML formatting of summary fields")

                # Function to recursively find and format all summary fields in the JSON
                def format_summary_fields(data, LLM_FORMAT_MODEL):
                    if isinstance(data, dict):
                        # Process this dict
                        for key, value in list(data.items()):  # Use list() to avoid modification during iteration
                            if key == "summary" and isinstance(value, str) and len(value) > 10 and 'nextcareer.ai' not in value:
                                # Format this summary field
                                logger.info(f"Formatting summary field for item with ID: {data.get('id', 'unknown')}")
                                format_response = groq_client.chat.completions.create(
                                    model=LLM_FORMAT_MODEL,
                                    messages=[
                                        {"role": "system", "content": "You are an expert at formatting plain text into well-structured HTML. Convert the given text into HTML format with appropriate tags like <p>, <ul>, <li>, <strong>, <em>, etc. Create logical paragraph breaks, bullet points for lists, and emphasize important information. Make the content more readable and professional. Do not edit, change or modify the content, just format it."},
                                        {"role": "user", "content": f"Format this text into professional HTML with appropriate structure, keep content as it is, do not edit, change or modify the content, just format it:\n\n{value}"}
                                    ],
                                    temperature=0.2,
                                    top_p=1,
                                    seed=4,
                                    stream=False
                                )
                                formatted_summary = format_response.choices[0].message.content.strip()
                                # Clean up any markdown code blocks or extra quotes
                                if formatted_summary.startswith("```html"):
                                    formatted_summary = formatted_summary.replace("```html", "").replace("```", "").strip()
                                data[key] = formatted_summary
                                logger.info(f"Formatted a summary field for data: {data.get('id', 'unknown')}")
                                logger.info("-"*20)
                            elif isinstance(value, (dict, list)):
                                # Recursively process nested structures
                                format_summary_fields(value, LLM_FORMAT_MODEL)
                    elif isinstance(data, list):
                        # Process each item in the list
                        for item in data:
                            format_summary_fields(item, LLM_FORMAT_MODEL)

                # Special case for the main summary section content
                if "sections" in result_json and "summary" in result_json["sections"] and "content" in result_json["sections"]["summary"]:
                    content = result_json["sections"]["summary"]["content"]
                    if isinstance(content, str) and len(content) > 10 and 'nextcareer.ai' not in content:
                        logger.info("Formatting main summary section content")
                        format_response = groq_client.chat.completions.create(
                            model=llm_helpers.get_available_groq_model(),
                            messages=[
                                {"role": "system", "content": "You are an expert at formatting plain text into well-structured HTML. Convert the given text into HTML format with appropriate tags like <p>, <ul>, <li>, <strong>, <em>, etc. Create logical paragraph breaks, bullet points for lists, and emphasize important information. Make the content more readable and professional. Do not edit, change or modify the content, just format it."},
                                {"role": "user", "content": f"Format this resume summary into professional HTML:\n\n{content}"}
                            ],
                            temperature=0.2,
                            seed=4,
                            top_p=1,
                            stream=False
                        )
                        formatted_content = format_response.choices[0].message.content.strip()
                        # Clean up any markdown code blocks or extra quotes
                        if formatted_content.startswith("```html"):
                            formatted_content = formatted_content.replace("```html", "").replace("```", "").strip()
                        result_json["sections"]["summary"]["content"] = formatted_content
                        logger.info("Formatted main summary content successfully")

                # Process the entire result_json to find all summary fields
                LLM_FORMAT_MODEL = llm_helpers.get_available_groq_model()
                logger.info(f"Using LLM model for formatting: {LLM_FORMAT_MODEL}")
                format_summary_fields(result_json, LLM_FORMAT_MODEL)

                logger.info("Successfully formatted all summary fields with HTML")
            except Exception as format_error:
                logger.error(f"Error formatting summaries with HTML: {str(format_error)}")
                logger.info("Continuing with unformatted summaries")
                # Continue with unformatted summaries if formatting fails
                pass

            # Apply the check to ensure all required fields exist
            logger.info("Ensuring all required fields exist in the result")
            self.ensure_required_fields(result_json, self.required_fields)
            result_json["metadata"] = self.required_fields["metadata"]

            logger.info("Resume parsing completed successfully")
            return result_json
        except Exception as e:
            logger.error(f"Error processing resume JSON: {str(e)}")
            logger.error(f"Response content: {response.choices[0].message.content}")
            raise ValueError(f"Error serializing to JSON: {str(e)}")
    def _safe_json_serialize(self, obj):
        """Safely serialize an object to JSON, handling special Python types."""
        if isinstance(obj, type):
            # If it's a class, return its name
            return obj.__name__
        elif hasattr(obj, "__dict__"):
            # For objects with __dict__, convert to dict
            return {k: self._safe_json_serialize(v) for k, v in obj.__dict__.items()
                   if not k.startswith("_")}
        elif isinstance(obj, (list, tuple)):
            # For lists and tuples
            return [self._safe_json_serialize(item) for item in obj]
        elif isinstance(obj, dict):
            # For dictionaries
            return {k: self._safe_json_serialize(v) for k, v in obj.items()}
        elif isinstance(obj, (int, float, str, bool, type(None))):
            # Basic types can be serialized directly
            return obj
        else:
            # For other types, convert to string
            return str(obj)
