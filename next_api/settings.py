import os
import sys

APP_ENV = os.getenv("APP_ENV", "PRODUCTION")
APP_DEBUG = os.getenv("APP_DEBUG", "False") == "True"

APP_API_NAME = os.getenv("APP_API_NAME", "NextAPI")
APP_API_HOST = os.getenv("APP_API_HOST", "0.0.0.0")
APP_API_PORT = int(os.getenv("APP_API_PORT", "8000"))

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        'sanic.root': {
            'level': os.getenv('LOGGER_ROOT_LEVEL', 'INFO'),
            'handlers': os.getenv('LOGGER_ROOT_HANDLERS', 'console').split(','),
        },
        'sanic.error': {
            'level': os.getenv('LOGGER_ERROR_LEVEL', 'DEBUG'),
            'handlers':  os.getenv('LOGGER_ERROR_HANDLERS', 'error_console').split(','),
            'propagate': True,
            'qualname': 'sanic.error',
        },
        'sanic.access': {
            'level': os.getenv('LOGGER_ACCESS_LEVEL', 'INFO'),
            'handlers': os.getenv('LOGGER_ACCESS_HANDLERS', 'access_console').split(','),
            'propagate': True,
            'qualname': 'sanic.access',
        },
        'sanic.server': {
            'level': os.getenv('LOGGER_SERVER_LEVEL', 'INFO'),
            'handlers': os.getenv('LOGGER_SERVER_HANDLERS', 'console').split(','),
            'propagate': True,
            'qualname': 'sanic.server',
        },
        'sanic.websockets': {
            'level': os.getenv('LOGGER_WEBSOCKETS_LEVEL', 'INFO'),
            'handlers': os.getenv('LOGGER_WEBSOCKETS_HANDLERS', 'console').split(','),
            'propagate': True,
            'qualname': 'sanic.websockets',
        },
    },
    'handlers': {
        'console': {
            'class': os.getenv('LOG_HANDLERS_CONSOLE', 'logging.StreamHandler'),
            'formatter': 'generic',
            'stream': sys.stdout
        },
        'error_console': {
            'class': os.getenv('LOG_HANDLERS_ERROR_CONSOLE', 'logging.StreamHandler'),
            'formatter': 'generic',
            'stream': sys.stderr
        },
        'access_console': {
            'class': os.getenv('LOG_HANDLERS_ACCESS_CONSOLE', 'logging.StreamHandler'),
            'formatter': 'access',
            'stream': sys.stdout,
        },
        'cloud_logging': {
            'class': os.getenv('LOG_HANDLERS_CLOUD_LOGGING', 'google.cloud.logging.handlers.StructuredLogHandler'),
            'formatter': 'generic',
            'stream': sys.stdout,
        }
    },
    'formatters': {
        'generic': {'class': os.getenv('LOGGER_GENERIC_FORMATTER', 'sanic.logging.formatter.JSONFormatter')},
        'access': {'class': os.getenv('LOGGER_ACCESS_FORMATTER', 'sanic.logging.formatter.JSONAccessFormatter')},
    }
}

DATABASE_CONFIG = {
    'connections': {
        'default': {
            'engine': 'tortoise.backends.asyncpg',
            'credentials': {
                'host': os.getenv('DB_DEFAULT_HOST', 'localhost'),
                'port': int(os.getenv('DB_DEFAULT_PORT', '5432')),
                'user': os.getenv('DB_DEFAULT_USER', 'postgres_user'),
                'password': os.getenv('DB_DEFAULT_PASSWORD', 'postgres_pw'),
                'database': os.getenv('DB_DEFAULT_DATABASE', 'next_ai'),
            },
        },
    },
    'apps': {
        'next_api': {
            'models': ['models',],
            'default_connection': 'default',
            'schema': 'public',
        },
    },
    # 'routers': ['utils.db.Router'],
    'use_tz': True,
    # 'timezone': 'UTC',
}

CACHE_CONFIG = {
    'default': {
        'type': 'redis',
        'url': os.getenv('CACHE_DEFAULT_URL', 'redis://localhost:6379/0'),
        'host': os.getenv('CACHE_DEFAULT_HOST', 'localhost'),
        'port': int(os.getenv('CACHE_DEFAULT_PORT', '6379')),
        'db': int(os.getenv('CACHE_DEFAULT_DB', '0')),
        'options': {
            'max_connections': int(os.getenv('CACHE_DEFAULT_MAX_CONNECTIONS', '1')),
        },
    },
}

# If empty, a random salt will be generated for each time a password is hashed
PASSWORD_SALT = os.getenv("PASSWORD_SALT", "")
PASSWORD_ITERATIONS = int(os.getenv("PASSWORD_ITERATIONS", "100000"))
PASSWORD_SEP = os.getenv("PASSWORD_SEP", "$")

# JWT
JWT_ISSUER = os.getenv("JWT_ISSUER", "NextAI")
JWT_PRIVATE_KEY_PATH = os.getenv("JWT_PRIVATE_KEY_PATH")
JWT_PRIVATE_KEY = os.getenv("JWT_PRIVATE_KEY")
JWT_PUBLIC_KEY_PATH = os.getenv("JWT_PUBLIC_KEY_PATH")
JWT_PUBLIC_KEY = os.getenv("JWT_PUBLIC_KEY")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "PS256")
JWT_EXPIRE_TIME = int(os.getenv("JWT_EXPIRE_TIME", "90000"))  # 25 hours
JWT_REFRESH_EXPIRE_TIME = int(
    os.getenv("JWT_REFRESH_EXPIRE_TIME", "172800"))  # 48 hours

# API Servers
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*")
CORS_ALLOW_METHODS = os.getenv(
    "CORS_ALLOW_METHODS", "GET,POST,PUT,DELETE,OPTIONS")

# AI parts
ASSISTANT_ID = os.environ.get("ASSISTANT_ID", "")
MODEL_LIST = [
    {
        "model_name": "gpt-4o",
        "litellm_params": {
            "model": "gpt-4o-mini-2024-07-18",
            "api_key": os.environ.get('OPENAI_API_KEY', ""),
        },
    },
    {
        "model_name": "gpt-4o",
        "litellm_params": {
            "model": "gpt-4o",
            "api_key": os.environ.get('OPENAI_API_KEY', ""),
        },
    },
    {
        "model_name": "gpt-4o",
        "litellm_params": {
            "model": "gpt-4o-mini",
            "api_key": os.environ.get('OPENAI_API_KEY', ""),
        },
    },
]


LEMONSQUEEZY_API_KEY = os.environ.get("LEMONSQUEEZY_API_KEY", "")
LEMONSQUEEZY_STORE_ID = os.environ.get("LEMONSQUEEZY_STORE_ID", "")
LEMONSQUEEZY_API_URL = os.environ.get("LEMONSQUEEZY_API_URL", "")
LEMONSQUEEZY_SIGNING_SECRET = os.environ.get("LEMONSQUEEZY_SIGNING_SECRET", "")
LEMONSQUEEZY_EDU_VARIANT = [x.strip() for x in os.environ.get("LEMONSQUEEZY_EDU_VARIANT", "658251,703046").split(",")]

AUTH0_CLIENT_ID = os.environ.get("AUTH0_CLIENT_ID", "")
AUTH0_CLIENT_SECRET = os.environ.get("AUTH0_CLIENT_SECRET", "")
AUTH0_DOMAIN = os.environ.get("AUTH0_DOMAIN", "")
AUTH0_APP_SECRET_KEY = os.environ.get("AUTH0_APP_SECRET_KEY", "")
AUTH0_CALLBACK_URL = os.environ.get("AUTH0_CALLBACK_URL", "")

MOCK_INTERVIEW_FREE_QUESTIONS_LIMIT = int(os.environ.get("MOCK_INTERVIEW_FREE_QUESTIONS_LIMIT", "10"))

POLAR_OAT = os.environ.get("POLAR_OAT", "")
POLAR_SECRET = os.environ.get("POLAR_SECRET", "")
POLAR_SERVER = os.environ.get("POLAR_SERVER", "")
POLAR_EDU_PRODUCT_ID = [x.strip() for x in os.environ.get(
    "POLAR_EDU_PRODUCT_ID", "067e714f-f54f-7e15-8000-cd4c7996b6de,067e714f-f54f-7f68-8000-8ae0f39e1ef0").split(",")]
LOOPS_API_KEY = os.environ.get("LOOPS_API_KEY", "")

LOOPS_EMAIL_CONFIG = {
    "register_email": {
        "transactional_id": os.environ.get("LOOPS_REGISTER_EMAIL_TRANSACTIONAL_ID", ""),
        "team_name": os.environ.get("LOOPS_REGISTER_EMAIL_TEAM_NAME", "NextCareer.AI"),
        "product_name": os.environ.get("LOOPS_REGISTER_EMAIL_PRODUCT_NAME", "NextCareer.AI"),
    }
}
