import requests
import hmac
import hashlib
from uuid import UUI<PERSON>
from typing import Op<PERSON>, <PERSON><PERSON>, Union
from pydantic import BaseModel
from settings import LEMONSQUEEZY_API_KEY, LEMONSQUEEZY_STORE_ID, LEMONSQUEEZY_API_URL, LEMONSQUEEZY_SIGNING_SECRET


class EventMetadata(BaseModel):
    test_mode: bool
    event_name: str
    webhook_id: str


class OrderAttributes(BaseModel):
    store_id: int
    customer_id: int
    identifier: UUID
    order_number: int
    user_name: str
    user_email: str


class OrderData(BaseModel):
    type: str
    id: str
    attributes: OrderAttributes


class SubscriptionAttributes(BaseModel):
    store_id: int
    customer_id: int
    order_id: int
    order_item_id: int
    product_id: int
    variant_id: int
    product_name: str
    variant_name: str
    user_name: str
    user_email: str


class SubscriptionData(BaseModel):
    type: str
    id: str
    attributes: SubscriptionAttributes


def parse_webhook_event(event: dict) -> <PERSON><PERSON>[
    EventMetadata,
    Optional[Union[SubscriptionData, OrderData]]
]:
    metadata = EventMetadata(**event["meta"])
    if metadata.event_name in ["subscription_created", "subscription_updated", "subscription_cancelled"]:
        data = SubscriptionData(**event["data"])
    elif metadata.event_name in ["order_refunded", "order_created", "order_updated"]:
        data = OrderData(**event["data"])
    else:
        data = None
    return metadata, data


def sign_request(request_body: Union[str, bytes]) -> str:
    if isinstance(request_body, str):
        return hmac.new(LEMONSQUEEZY_SIGNING_SECRET.encode(), request_body.encode(), hashlib.sha256).hexdigest()
    return hmac.new(LEMONSQUEEZY_SIGNING_SECRET.encode(), request_body, hashlib.sha256).hexdigest()


def check_signature(signature: str, request_body: Union[str, bytes]) -> bool:
    return hmac.compare_digest(signature,  sign_request(request_body))


def checkout(variant_id: int, email: str, name: str, redirect_url: str) -> dict:
    headers = {
        "Authorization": f"Bearer {LEMONSQUEEZY_API_KEY}",
        "Accept": "application/vnd.api+json",
        "Content-Type": "application/vnd.api+json",
    }

    checkout_data = {
        "data": {
            "type": "checkouts",
            "attributes": {
                "product_options": {
                    "enabled_variants": [
                        variant_id
                    ],
                    "redirect_url": redirect_url,
                },
            },
            "checkout_data": {
                "email": email,
                "name": name,
                "variant_quantities": [
                    {
                        "variant_id": variant_id,
                        "quantity": 1
                    },
                ],
                "custom": {
                    "user_id": "000000000000000000000000",
                    "user_email": email,
                    "user_name": name,
                }
            },
            "relationships": {
                "store": {
                    "data": {
                        "id": LEMONSQUEEZY_STORE_ID,
                        "type": "stores"
                    }
                },
                "variant": {
                    "data": {
                        "id": str(variant_id),
                        "type": "variants",
                    },
                },
            },
        },
    }

    response = requests.post(f"{LEMONSQUEEZY_API_URL}/checkouts", headers=headers, json=checkout_data)
    return response.json()
