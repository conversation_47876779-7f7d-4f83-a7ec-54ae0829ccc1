import os
import requests
from typing import Optional
from pydantic import BaseModel, ConfigDict
from sanic.log import error_logger

GOOGLE_OAUTH_ENDPOINT = os.getenv(
    "GOOGLE_OAUTH_ENDPOINT",
    "https://people.googleapis.com/v1/people/me?personFields=names,emailAddresses"
)


class GooglePeople(BaseModel):
    model_config = ConfigDict(extra="allow")

    primary_email: Optional[str] = None
    display_name: Optional[str] = None


def get_people_info(access_token: str) -> Optional[GooglePeople]:
    """Get the email address from the Google access token.

    Args:
        access_token (str): The Google access token

    Returns:
        Optional[str]: The email address if found, None otherwise
    """
    headers = {
        "Authorization": f"Bearer {access_token}",
    }
    resp = requests.get(GOOGLE_OAUTH_ENDPOINT, headers=headers, timeout=10)
    if resp.status_code != 200:
        error_logger.exception("Failed to get email from Google: %s", resp.text)
        return None
    result = GooglePeople()
    for _name in resp.json().get("names", []):
        if _name.get("metadata", {}).get("primary"):
            result.display_name = _name.get("displayName")
            break
    for _email in resp.json().get("emailAddresses", []):
        if _email.get("metadata", {}).get("primary"):
            result.primary_email = _email.get("value")
            break
    return result
