from typing import List
import httpx
from datetime import datetime
import html
from models.job_search import JobResponse, JobSearchRequest
from clients.job_search.base import JobSearchProvider
from clients.job_search.utils import categorize_job
from sanic.log import logger
from uuid_extensions import uuid7
class <PERSON><PERSON><PERSON><PERSON><PERSON>ider(JobSearchProvider):
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://jooble.org/api/"
        self.headers = {
            "Content-Type": "application/json"
        }

    async def search_jobs(self, request: JobSearchRequest) -> List[JobResponse]:
        async with httpx.AsyncClient() as client:
            # Construct the request body according to Jooble API documentation
            body = {
                "keywords": request.keyword,
                "location": request.location if request.location else ""
            }

            try:
                # Print the full URL and body for debugging
                print(f"Requesting URL: {self.base_url}{self.api_key}")
                print(f"With body: {body}")

                response = await client.post(
                    f"{self.base_url}{self.api_key}",
                    json=body,
                    headers=self.headers
                )

                # Print response status for debugging
                logger.info(f"Jooble response status: {response.status_code}")

                # If there's an error, print the response body
                if response.status_code != 200:
                    logger.error(f"Jooble error response: {response.text}")
                    return []

                data = response.json()
                jobs = []
                # Process each job in the response
                for job in data.get("jobs", []):
                    try:
                        title = job.get("title", "")
                        description = html.unescape(job.get("snippet", ""))

                        # Categorize the job and get matched keywords
                        category, matched_keywords = categorize_job(title, description)

                        # Get company name
                        company = job.get("company")

                        # Get location
                        location = job.get("location", "")

                        # Get working type
                        working_type = job.get("type", None)

                        # Get remote status (Jooble doesn't provide this directly, so we'll set a default)
                        is_remote = None

                        # Get salary information if available
                        salary = job.get("salary", None)

                        # Get posted date if available
                        posted_at = None
                        if job.get("updated"):
                            try:
                                # Parse Jooble's date format
                                posted_at = datetime.fromisoformat(job.get("updated").replace('Z', '+00:00')).date()
                            except Exception as e:
                                logger.error(f"Error parsing date {job.get('updated')}: {e}")

                        # Get job link
                        link = job.get("link")

                        # Get source
                        source = job.get("source", "Jooble")
                        if source.lower() != "jooble":
                            source = "(Jooble)" + " " + source

                        # Convert UUID to string for id field
                        job_id = str(uuid7())

                        jobs.append(JobResponse(
                            id=job_id,
                            provider_id=str(job.get("id", "")),
                            title=title,
                            company=company,
                            location=location,
                            salary=salary,
                            description=description,
                            url=link,  # Add url field which is required
                            posted_at=posted_at,
                            is_remote=is_remote,
                            source=source,
                            keywords=matched_keywords if matched_keywords else [],
                            category=category,
                            working_type=working_type
                        ))
                    except Exception as e:
                        logger.error(f"Error occurred while processing job: {e}")
                        continue
                return jobs

            except httpx.HTTPError as e:
                logger.error(f"HTTP error occurred: {e}")
                return []
            except Exception as e:
                logger.error(f"Error occurred: {e}")
                return []