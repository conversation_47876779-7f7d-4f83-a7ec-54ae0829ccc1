from typing import List
import httpx
from datetime import datetime
import html
from uuid_extensions import uuid7
from models.job_search import JobResponse, JobSearchRequest
from clients.job_search.base import JobSearchProvider
from clients.job_search.utils import categorize_job

class FindworkProvider(JobSearchProvider):
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://findwork.dev/api/jobs/"
        self.headers = {
            "Authorization": f"Token {api_key}",
            "Content-Type": "application/json"
        }

    async def search_jobs(self, request: JobSearchRequest) -> List[JobResponse]:
        async with httpx.AsyncClient() as client:
            # Construct the URL with search parameters according to Findwork API documentation
            params = {
                "search": request.keyword,
                "location": request.location if request.location else "",
                "source": "",
                "remote": "true",  # Include remote jobs by default
                "employment_type": "",
                "order_by": ""
            }

            try:
                # Print the full URL for debugging
                print(f"Requesting URL: {self.base_url}")
                print(f"With params: {params}")

                response = await client.get(
                    self.base_url,
                    params=params,
                    headers=self.headers
                )

                # Print response status and headers for debugging
                print(f"Response status: {response.status_code}")

                # If there's an error, print the response body
                if response.status_code != 200:
                    print(f"Error response: {response.text}")
                    return []
                data = response.json()
                jobs = []

                for job in data.get("results", []):
                    title = job.get("role", "")
                    # Get description from the 'text' field and decode HTML entities
                    description = html.unescape(job.get("text", ""))

                    # Categorize the job and get matched keywords
                    category, matched_keywords = categorize_job(title, description)

                    # Get company name
                    company = job.get("company_name")
                    # get company_num_employees
                    # company_num_employees = job.get("company_num_employees", "")

                    # Get location
                    location = job.get("location")

                    # Get remote status
                    is_remote = job.get("remote", None)

                    # get logo url
                    logo_url = job.get("logo", None)

                    # Get employment type
                    employment_type = job.get("employment_type", "")
                    if not employment_type and is_remote:
                        employment_type = "Remote"

                    # Get salary information if available
                    salary = None
                    if job.get("salary_min") or job.get("salary_max"):
                        salary_min = job.get("salary_min")
                        salary_max = job.get("salary_max")
                        currency = job.get("salary_currency", "GBP")
                        if salary_min and salary_max:
                            salary = f"{salary_min} - {salary_max} {currency}"
                        elif salary_min:
                            salary = f"From {salary_min} {currency}"
                        elif salary_max:
                            salary = f"Up to {salary_max} {currency}"

                    # Get posted date if available
                    posted_at = None
                    if job.get("date_posted"):
                        try:
                            posted_at = datetime.fromisoformat(job.get("date_posted").replace('Z', '+00:00')).date()
                        except Exception as e:
                            print(f"Error parsing date {job.get('date_posted')}: {e}")

                    # Get job link
                    link = job.get("url")

                    # Get keywords from the API response if available
                    api_keywords = job.get("keywords", [])
                    if api_keywords:
                        # Combine API keywords with our categorized keywords
                        all_keywords = list(set(matched_keywords + api_keywords))
                    else:
                        all_keywords = matched_keywords

                    # Get source
                    source = job.get("source", "Findwork")
                    if source != "findwork":
                        source = "(Findwork)" + " " + source

                    jobs.append(JobResponse(
                        id=str(uuid7()),
                        provider_id=job.get("id", None),
                        title=title,
                        company=company,
                        # number_of_employees=company_num_employees,
                        location=location,
                        salary=salary,
                        description=description,
                        url=link,
                        posted_at=posted_at,
                        is_remote=is_remote,
                        source=source,
                        keywords=all_keywords if all_keywords else [],
                        category=category,
                        working_type=employment_type,
                        logo=logo_url
                    ))

                return jobs

            except httpx.HTTPError as e:
                print(f"HTTP error occurred: {e}")
                return []
            except Exception as e:
                print(f"Error occurred: {e}")
                return []