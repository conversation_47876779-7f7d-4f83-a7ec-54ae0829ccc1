from typing import List, Tuple
# Skill keywords for different domains
SKILL_KEYWORDS = {
    'programming': ['python', 'java', 'javascript', 'c++', 'c#', 'ruby', 'php', 'swift', 'kotlin', 'go', 'rust'],
    'frameworks': ['react', 'angular', 'vue', 'django', 'flask', 'spring', 'laravel', 'express', 'node.js'],
    'databases': ['sql', 'mysql', 'postgresql', 'mongodb', 'oracle', 'redis', 'elasticsearch'],
    'cloud': ['aws', 'azure', 'gcp', 'cloud', 'kubernetes', 'docker', 'terraform'],
    'devops': ['ci/cd', 'jenkins', 'git', 'ansible', 'puppet', 'chef'],
    'ai_ml': ['machine learning', 'deep learning', 'nlp', 'computer vision', 'tensorflow', 'pytorch', 'scikit-learn'],
    'sales': ['sales', 'negotiation', 'crm', 'salesforce', 'business development', 'account management'],
    'marketing': ['digital marketing', 'seo', 'sem', 'social media', 'content marketing', 'email marketing'],
    'healthcare': ['medical', 'healthcare', 'patient care', 'clinical', 'nursing', 'pharmacy'],
    'education': ['teaching', 'curriculum', 'instruction', 'education', 'training', 'mentoring'],
    'finance': ['accounting', 'financial analysis', 'banking', 'investment', 'risk management'],
    'legal': ['law', 'legal', 'contract', 'litigation', 'compliance', 'regulatory'],
    'retail': ['retail', 'merchandising', 'inventory', 'sales', 'customer service'],
    'customer_service': ['customer service', 'support', 'help desk', 'client relations'],
    'hr': ['recruitment', 'talent management', 'employee relations', 'compensation', 'benefits'],
    'design': ['ui/ux', 'graphic design', 'web design', 'product design', 'adobe creative suite'],
    'consulting': ['consulting', 'business analysis', 'strategy', 'management consulting'],
    'manufacturing': ['manufacturing', 'production', 'quality control', 'industrial', 'operations'],
    'logistics': ['logistics', 'supply chain', 'warehouse', 'inventory', 'transportation'],
    'research': ['research', 'analysis', 'data analysis', 'laboratory', 'scientific'],
    'media': ['media', 'public relations', 'communications', 'journalism', 'content creation'],
    'security': ['cybersecurity', 'information security', 'network security', 'security operations']
}

# Job categories with their associated keywords
JOB_CATEGORIES = {
    'IT Jobs': {
        'keywords': SKILL_KEYWORDS['programming'] + SKILL_KEYWORDS['frameworks'] + 
                   SKILL_KEYWORDS['databases'] + SKILL_KEYWORDS['cloud'] + 
                   SKILL_KEYWORDS['devops'],
        'title_keywords': ['developer', 'engineer', 'programmer', 'architect', 'devops', 'sre']
    },
    'AI & Machine Learning Jobs': {
        'keywords': SKILL_KEYWORDS['ai_ml'],
        'title_keywords': ['ai', 'machine learning', 'ml', 'data scientist', 'ai engineer', 'ml engineer']
    },
    'Sales Jobs': {
        'keywords': SKILL_KEYWORDS['sales'],
        'title_keywords': ['sales', 'business development', 'account executive']
    },
    'Marketing Jobs': {
        'keywords': SKILL_KEYWORDS['marketing'],
        'title_keywords': ['marketing', 'digital marketing', 'content marketing']
    },
    'Healthcare & Nursing Jobs': {
        'keywords': SKILL_KEYWORDS['healthcare'],
        'title_keywords': ['healthcare', 'medical', 'nursing', 'doctor', 'physician']
    },
    'Teaching Jobs': {
        'keywords': SKILL_KEYWORDS['education'],
        'title_keywords': ['teacher', 'professor', 'instructor', 'educator']
    },
    'Accounting & Finance Jobs': {
        'keywords': SKILL_KEYWORDS['finance'],
        'title_keywords': ['accountant', 'finance', 'financial', 'banking']
    },
    'Legal Jobs': {
        'keywords': SKILL_KEYWORDS['legal'],
        'title_keywords': ['lawyer', 'attorney', 'legal', 'paralegal']
    },
    'Retail Jobs': {
        'keywords': SKILL_KEYWORDS['retail'],
        'title_keywords': ['retail', 'store', 'shop', 'merchandising']
    },
    'Customer Services Jobs': {
        'keywords': SKILL_KEYWORDS['customer_service'],
        'title_keywords': ['customer service', 'support', 'help desk']
    },
    'HR & Recruitment Jobs': {
        'keywords': SKILL_KEYWORDS['hr'],
        'title_keywords': ['hr', 'human resources', 'recruiter', 'talent']
    },
    'Creative & Design Jobs': {
        'keywords': SKILL_KEYWORDS['design'],
        'title_keywords': ['designer', 'ui/ux', 'graphic design', 'creative']
    },
    'Consultancy Jobs': {
        'keywords': SKILL_KEYWORDS['consulting'],
        'title_keywords': ['consultant', 'consulting', 'business analyst']
    },
    'Manufacturing Jobs': {
        'keywords': SKILL_KEYWORDS['manufacturing'],
        'title_keywords': ['manufacturing', 'production', 'industrial']
    },
    'Logistics & Warehouse Jobs': {
        'keywords': SKILL_KEYWORDS['logistics'],
        'title_keywords': ['logistics', 'warehouse', 'supply chain']
    },
    'Scientific & QA Jobs': {
        'keywords': SKILL_KEYWORDS['research'],
        'title_keywords': ['scientist', 'researcher', 'qa', 'quality assurance']
    },
    'PR, Advertising & Marketing Jobs': {
        'keywords': SKILL_KEYWORDS['media'] + SKILL_KEYWORDS['marketing'],
        'title_keywords': ['pr', 'advertising', 'marketing', 'media']
    },
    'Admin Jobs': {
        'keywords': ['admin', 'administrative', 'office', 'assistant', 'coordinator'],
        'title_keywords': ['admin', 'administrative', 'office', 'assistant']
    },
    'Security Jobs': {
        'keywords': SKILL_KEYWORDS['security'],
        'title_keywords': ['security', 'cybersecurity', 'information security']
    }
} 

def categorize_job(title: str, description: str = None) -> Tuple[str, List[str]]:
    """
    Categorize a job based on its title and description.
    Returns a tuple of (category, matched_keywords)
    """
    title = title.lower()
    description = description.lower() if description else ""
    
    best_category = "Other"
    best_match_count = 0
    matched_keywords = []
    
    for category, criteria in JOB_CATEGORIES.items():
        match_count = 0
        category_keywords = []
        
        # Check title keywords
        for keyword in criteria['title_keywords']:
            if keyword.lower() in title:
                match_count += 2  # Title matches are weighted more heavily
                category_keywords.append(keyword)
        
        # Check skill keywords in both title and description
        for keyword in criteria['keywords']:
            if keyword.lower() in title or keyword.lower() in description:
                match_count += 1
                category_keywords.append(keyword)
        
        if match_count > best_match_count:
            best_match_count = match_count
            best_category = category
            matched_keywords = category_keywords
    
    return best_category, matched_keywords 