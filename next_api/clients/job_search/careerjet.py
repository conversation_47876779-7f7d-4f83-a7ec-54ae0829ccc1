import json
import logging
from datetime import datetime
from email.utils import parsedate_to_datetime
from typing import List

import httpx
import requests
from sanic.log import logger
from uuid_extensions import uuid7

from clients.job_search.base import JobSearchProvider
from clients.job_search.utils import categorize_job
from models.job_search import JobSearchRequest, JobResponse
logger = logging.getLogger(__name__)

class CareerjetProvider(JobSearchProvider):
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.locale = "en_GB"  # Default to UK, can be made configurable
        self.base_url = "http://public.api.careerjet.net/search"
        self.user_ip = self._get_user_ip()
        self.user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"

    def _get_user_ip(self):
        """Get the IP address of the server"""
        try:
            # Try to get the IP address from a public API
            response = requests.get("https://api.ipify.org?format=json", timeout=5)
            if response.status_code == 200:
                return response.json().get("ip", "127.0.0.1")
        except Exception as e:
            logger.error(f"Error getting IP address: {e}")

        # Fallback to localhost if we can't get the IP
        return "127.0.0.1"

    def _parse_date(self, date_str: str) -> datetime.date:
        """Parse date string from Careerjet API"""
        try:
            if not date_str:
                return None
            # Parse RFC 2822 date format
            dt = parsedate_to_datetime(date_str)
            return dt.date()
        except Exception as e:
            logger.error(f"Error parsing date {date_str}: {e}")
            return None

    async def search_jobs(self, request: JobSearchRequest) -> List[JobResponse]:
        try:
            # Prepare search parameters
            search_params = {
                'keywords': request.keyword,
                'location': request.location if request.location else '',
                'affid': self.api_key,
                'user_ip': self.user_ip,
                'user_agent': self.user_agent,
                'url': 'https://nextjobsearch.com/search',
                'pagesize': '20',
                'page': '1',
                'contracttype': '',  # all contract types
                'contractperiod': ''  # all contract periods
            }

            logger.info(f"Searching with Careerjet API with params: {search_params}")

            # Create a timeout for the request
            timeout = httpx.Timeout(30.0, connect=10.0)

            async with httpx.AsyncClient(timeout=timeout) as client:
                # Make the request
                response = await client.get(
                    self.base_url,
                    params=search_params,
                    follow_redirects=True
                )

                logger.info(f"Careerjet Response status: {response.status_code}")

                # Check response status
                if response.status_code != 200:
                    logger.error(f"Error response from Careerjet API: {response.text}")
                    return []

                # Parse response
                try:
                    result = response.json()
                except json.JSONDecodeError as e:
                    logger.error(f"Error decoding JSON from Careerjet API: {e}")
                    logger.error(f"Response text: {response.text[:500]}...")
                    return []
                except Exception as e:
                    logger.error(f"Error parsing Careerjet API: {e}")
                    return []

                # Check if the search was successful
                if not result or 'jobs' not in result:
                    logger.error(f"Unexpected response structure from Careerjet API: {result}")
                    return []

                jobs = []
                for job in result.get('jobs', []):
                    try:
                        title = job.get('title', '')
                        description = job.get('description', '')

                        # Categorize the job and get matched keywords
                        category, matched_keywords = categorize_job(title, description)

                        # Get company name
                        company = job.get('company', '')

                        # Get location
                        location = job.get('locations', '')

                        # Get salary information if available
                        salary = job.get('salary')

                        # Get posted date if available
                        posted_at = self._parse_date(job.get('date'))

                        # Get job link
                        link = job.get('url')

                        # Determine if job is remote based on description or title
                        is_remote = False
                        if 'remote' in title.lower() or 'remote' in description.lower():
                            is_remote = True

                        jobs.append(JobResponse(
                            id=str(uuid7()),
                            title=title,
                            company=company,
                            location=location,
                            salary=salary,
                            description=description,
                            url=link,
                            posted_at=posted_at,
                            is_remote=is_remote,
                            source="Careerjet",
                            keywords=matched_keywords if matched_keywords else [],
                            category=category
                        ))
                    except Exception as e:
                        logger.error(f"Error processing job from Careerjet: {e}")
                        continue

                return jobs

        except httpx.HTTPError as e:
            logger.error(f"HTTP error occurred with Careerjet API: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error occurred with Careerjet API: {e}")
            return []
