from typing import List
import httpx
from datetime import datetime
from uuid_extensions import uuid7
import logging
from models.job_search import JobResponse, JobSearchRequest
from clients.job_search.base import JobSearchProvider
from clients.job_search.utils import categorize_job

logger = logging.getLogger(__name__)


class AdzunaProvider(JobSearchProvider):
    def __init__(self, api_key: str, app_id: str):
        self.api_key = api_key
        self.app_id = app_id
        self.base_url = "https://api.adzuna.com/v1/api/jobs"
        self.country = "us"
        self.countries = ['gb', 'us', 'at', 'au', 'be', 'br', 'ca', 'ch', 'de', 'es', 'fe', 'in', 'it', 'mx', 'nl', 'nz', 'pl', 'sg', 'za']

    def parse_date(self, date_str):
        """Parse date string from Adzuna API, handling timezone indicator"""
        if not date_str:
            return None

        try:
            # Replace 'Z' with '+00:00' for proper ISO format
            if date_str.endswith('Z'):
                date_str = date_str.replace('Z', '+00:00')
            return datetime.fromisoformat(date_str).date()
        except Exception as e:
            logger.error(f"Error parsing date {date_str}: {e}")
            return None

    async def search_jobs(self, request: JobSearchRequest) -> List[JobResponse]:
        timeout = httpx.Timeout(30.0, connect=10.0)
        # Construct the URL with country and page number
        url = f"{self.base_url}/{self.country}/search/1"

        # Parameters according to Adzuna API documentation
        params = {
            "app_id": self.app_id,
            "app_key": self.api_key,
            "what": request.keyword,
            "where": request.location,
            "results_per_page": 20
        }

        # async with httpx.AsyncClient() as client:
        async with httpx.AsyncClient(timeout=timeout) as client:
            try:
                logger.info(f"Requesting Adzuna API with params: {params}")

                response = await client.get(url, params=params,
                    follow_redirects=True)

                logger.info(f"Adzuna Response status: {response.status_code}")

                if response.status_code != 200:
                    logger.error(f"Error response from Adzuna API: {response.text}")
                    return []

                data = response.json()
                # logger.info("-"*30)
                # logger.info(f"Adzuna data: {data}")

                # Check if the response contains results
                if not data.get("results"):
                    logger.warning(f"No results found in Adzuna response. Count: {data.get('count', 0)}")
                    if "error" in data:
                        logger.error(f"Adzuna API error: {data.get('error')}")
                    return []

                jobs = []

                for job in data.get("results", []):
                    try:
                        title = job.get("title", "")
                        description = job.get("description", "")

                        # Categorize the job and get matched keywords
                        _, matched_keywords = categorize_job(title, description)
                        category = job.get("category", {}).get("label", "")

                        # Extract salary information
                        salary = None
                        if job.get("salary_is_posted") or job.get("salary_min") or job.get("salary_max"):
                            salary_min = job.get("salary_min")
                            salary_max = job.get("salary_max")
                            currency = job.get("salary_currency", "GBP")
                            if salary_min and salary_max:
                                salary = f"{salary_min} - {salary_max} {currency}"
                            elif salary_min:
                                salary = f"From {salary_min} {currency}"
                            elif salary_max:
                                salary = f"Up to {salary_max} {currency}"

                        # Get company name
                        company = None
                        if job.get("company") and isinstance(job.get("company"), dict):
                            company = job.get("company", {}).get("display_name")

                        # Get location
                        location = None
                        if job.get("location") and isinstance(job.get("location"), dict):
                            location = ", ".join(job.get("location", {}).get("area", []))
                            if location == "":
                                location = job.get("location", {}).get("display_name", "")

                        # Get contract type
                        working_type = job.get("contract_type", "")
                        is_remote = False
                        if job.get("contract_type") == "remote" or ("remote" in job.get("workplace_type", "").lower()):
                            is_remote = True

                        # Parse the date using our helper method
                        posted_at = self.parse_date(job.get("created"))

                        jobs.append(JobResponse(
                            id=str(uuid7()),
                            provider_id=job.get("id", None),
                            title=title,
                            company=company,
                            location=location,
                            salary=salary,
                            description=description,
                            url=job.get("redirect_url", ""),
                            posted_at=posted_at,
                            is_remote=is_remote,
                            source="Adzuna",
                            keywords=matched_keywords if matched_keywords else [],
                            category=category,
                            working_type=working_type
                        ))
                    except Exception as e:
                        logger.error(f"(Adzuna) Error processing job from Adzuna: {e}")
                        continue

                return jobs

            except httpx.HTTPError as e:
                logger.error(f"(Adzuna) HTTP error occurred with Adzuna API: {e}")
                return []
            except Exception as e:
                logger.error(f"(Adzuna) Unexpected error occurred with Adzuna API: {e}")
                return []