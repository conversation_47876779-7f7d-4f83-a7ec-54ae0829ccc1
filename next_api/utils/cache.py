from typing import Optional
from redis import Redis, ConnectionPool
from sanic.log import logger
from settings import CACHE_CONFIG

class CacheManager:
    _connections = {}

    def get_connection(self, name: str) -> Optional[Redis]:
        if name not in self._connections:
            config = CACHE_CONFIG[name]
            conn = self.get_redis_connection(config)
            try:
                conn.ping()
                self._connections[name] = conn
            except Exception as e:
                logger.error("Error connecting to Redis: %s", e)
                return None
        return self._connections[name]

    def get_redis_connection(self, config: dict) -> Redis:
        if config.get('url', None):
            pool = ConnectionPool.from_url(config.get('url'))
        else:
            pool = ConnectionPool(
                host=config.get('host', 'localhost'),
                port=config.get('port', 6379),
                db=config.get('db', None),
                **config.get('options', {})
            )
        return Redis(connection_pool=pool)


CACHE_MANAGER = CacheManager()
