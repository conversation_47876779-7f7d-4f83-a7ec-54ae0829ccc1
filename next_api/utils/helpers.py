import json
import decimal
import datetime
import uuid
import random
import string
import re


class ExtendedJsonEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, uuid.UUID):
            return str(o)
        if isinstance(o, decimal.Decimal):
            return f'{o:.2f}'
        if isinstance(o, datetime.datetime):
            return o.isoformat()
        if isinstance(o, datetime.date):
            return o.isoformat()
        return json.JSONEncoder.default(self, o)


def generate_random_string(
    length: int,
    digits: bool = True,
    lowers: bool = True,
    uppers: bool = True,
    punctuations: bool = True,
    exclude: list[str] = None,
) -> str:
    """Generate a random string with specified characteristics.

    Args:
        length (int): Length of the random string to generate
        digits (bool, optional): Include digits. Defaults to True.
        lowers (bool, optional): Include lowercase letters. Defaults to True. 
        uppers (bool, optional): Include uppercase letters. Defaults to True.
        punctuations (bool, optional): Include punctuation characters. Defaults to True.
        exclude (list[str], optional): Characters to exclude from the generated string. Defaults to None.

    Returns:
        str: Random string with the specified length and character types
    """
    runes = ''
    if digits:
        runes += string.digits
    if lowers:
        runes += string.ascii_lowercase
    if uppers:
        runes += string.ascii_uppercase
    if punctuations:
        runes += string.punctuation
    if exclude:
        runes = list(set(runes) - set(exclude))
    return ''.join(random.choice(runes) for _ in range(length))


def is_edu(email: str) -> bool:
    """Check if an email address is from an educational organization.

    Args:
        email (str): Email address to check

    Returns:
        bool: True if the email is from an educational organization, False otherwise
    """
    if not email or '@' not in email:
        return False

    # Common educational domain suffixes
    edu_domains = [
        r'\.edu$',  # US educational institutions
        r'\.edu\.[a-z]{2}$',  # Country-specific edu domains (e.g., edu.au)
        r'\.ac\.[a-z]{2}$',  # Academic institutions (e.g., ac.uk, ac.jp)
        r'\.sch\.[a-z]{2}$',  # Schools in some countries
    ]
    
    domain = email.split('@')[1].lower()
    return any(re.search(pattern, domain) for pattern in edu_domains)
