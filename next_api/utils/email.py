import requests
from sanic.log import error_logger, logger
from settings import LOOPS_EMAIL_CONFIG


class LoopsClient:
    """
    A client for the Loops API.
    """

    __api_key: str = ""

    def __init__(self, api_key: str):
        self.set_api_key(api_key)

    def set_api_key(self, api_key: str):
        self.__api_key = api_key

    def send_register_email(self, data: dict):
        url = "https://app.loops.so/api/v1/transactional"
        headers = {
            "Authorization": f"Bearer {self.__api_key}",
            "Content-Type": "application/json",
        }
        data = {
            "transactionalId": LOOPS_EMAIL_CONFIG["register_email"]["transactional_id"],
            "email": data["to"],
            "dataVariables": {
                "team_name": LOOPS_EMAIL_CONFIG["register_email"]["team_name"],
                "product_name": LOOPS_EMAIL_CONFIG["register_email"]["product_name"],
                "fullname": data.get("fullname", data["to"]),
            },
        }
        response = requests.post(url, headers=headers, json=data)
        if response.status_code != 200:
            error_logger.error("Send register email failed: %s", response.json())
            return False
        logger.info("Send register email successfully: %s", data["dataVariables"]["fullname"])
        return True
