import jwt
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, <PERSON>, <PERSON><PERSON>
from uuid_extensions import uuid7str
from sanic.log import error_logger
from settings import JWT_ALGORITHM, JWT_ISSUER, JWT_PRIVATE_KEY_PATH, JWT_PRIVATE_KEY, \
    JWT_PUBLIC_KEY_PATH, JWT_PUBLIC_KEY, JWT_EXPIRE_TIME
from utils.helpers import ExtendedJsonEncoder


class JWT:

    _private_key: str = None
    _public_key: str = None
    _issuer: str = None
    _algorithm: str = None

    def __init__(self, issuer: Optional[str] = None, algorithm: Optional[str] = None):
        self.issuer = issuer
        self.algorithm = algorithm
        self.load_private_key()
        self.load_public_key()

    @property
    def issuer(self) -> str:
        return self._issuer

    @issuer.setter
    def issuer(self, value: Optional[str]):
        if value is None or not isinstance(value, str) or value == "":
            value = JWT_ISSUER
        self._issuer = value

    @property
    def algorithm(self) -> str:
        return self._algorithm

    @algorithm.setter
    def algorithm(self, value: Optional[str]):
        if value is None or not isinstance(value, str) or value == "":
            value = JWT_ALGORITHM
        self._algorithm = value

    def load_private_key(self) -> str:
        if JWT_PRIVATE_KEY_PATH is not None and Path(JWT_PRIVATE_KEY_PATH).exists():
            with open(JWT_PRIVATE_KEY_PATH, "r") as f:
                self._private_key = f.read()
        self._private_key = JWT_PRIVATE_KEY

    def load_public_key(self) -> str:
        if JWT_PUBLIC_KEY_PATH is not None and Path(JWT_PUBLIC_KEY_PATH).exists():
            with open(JWT_PUBLIC_KEY_PATH, "r") as f:
                self._public_key = f.read()
        self._public_key = JWT_PUBLIC_KEY

    def encode(self, payload: dict, headers: Optional[dict] = None, algorithm: Optional[str] = None) -> Tuple[dict, str]:
        if algorithm is None or not isinstance(algorithm, str) or algorithm == "":
            algorithm = self.algorithm
        if headers is None or not headers or not isinstance(headers, dict):
            headers = {"alg": algorithm, "typ": "JWT"}
        if "exp" not in payload:
            payload["exp"] = datetime.now() + timedelta(seconds=JWT_EXPIRE_TIME)
        payload["jti"] = uuid7str()
        payload["iss"] = self.issuer
        return payload, jwt.encode(payload, self._private_key, algorithm, headers, json_encoder=ExtendedJsonEncoder)

    def decode(self, token: str, options: Optional[dict] = None, algorithms: Optional[List[str]] = None) -> dict:
        if algorithms is None or not algorithms:
            algorithms = [self.algorithm]
        if JWT_ALGORITHM == "HS256":
            # use the same key
            key = self._private_key
        else:
            key = self._public_key
        try:
            result = jwt.decode(token, key, algorithms=algorithms,
                                options=options, issuer=self.issuer)
        except jwt.InvalidTokenError as e:
            error_logger.exception(e)
            return {}
        return result
