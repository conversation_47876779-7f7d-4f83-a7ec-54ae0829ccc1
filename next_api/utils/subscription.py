from typing import Union, Dict
from uuid import UUID
from models.user_subscription import UserSubscription


async def is_premium_user(user_id: Union[str, UUID]) -> bool:
    """Check if a user has an active premium subscription.

    Args:
        user_id: The user ID to check

    Returns:
        bool: True if the user has an active premium subscription, False otherwise
    """   
    # Premium subscription plan codes
    premium_plan_codes = [
        "premium_monthly",
        "premium_yearly_59",
        "education_monthly",
        "education_yearly"
    ]
    
    if isinstance(user_id, str):
        user_id = UUID(user_id)

    # Get all active subscriptions for the user
    active_subscriptions = await UserSubscription.filter(
        user_id=user_id,
        status=UserSubscription.Status.ACTIVE
    ).prefetch_related('subscription_plan').all()

    # Check if any of the active subscriptions is a premium plan
    for subscription in active_subscriptions:
        if subscription.subscription_plan.code in premium_plan_codes:
            return True

    return False


async def check_premium_access(user: Union[Dict, UUID, str]) -> bool:
    """Convenience wrapper for is_premium_user that accepts either a user dict or ID.
    
    Args:
        user: Either a user dictionary with an 'id' key, or a user ID as UUID or string
        
    Returns:
        bool: True if the user has premium access, False otherwise
    """
    if isinstance(user, dict) and 'id' in user:
        user_id = user['id']
    else:
        user_id = user
        
    return await is_premium_user(user_id) 