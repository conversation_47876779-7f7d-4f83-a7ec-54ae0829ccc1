from datetime import datetime
from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from pydantic import BaseModel, ConfigDict, field_validator
from models.role_permission import Role, Permission
from models.user import User
from repositories.base import Pagination, OrderBy


class RoleOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "name", "-name",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class RoleQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    name: Optional[str] = None
    name__contains: Optional[str] = None
    name__icontains: Optional[str] = None
    name__startswith: Optional[str] = None
    name__endswith: Optional[str] = None

    status: Optional[Role.Status] = None
    status__in: Optional[List[Role.Status]] = None
    status__not_in: Optional[List[Role.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator("id__in", "id__not_in", mode="before")
    @classmethod
    def validate_id_in(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "name", "name__contains", "name__icontains", "name__startswith", "name__endswith",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v


class RoleRepository:

    @staticmethod
    async def create_one(data: Dict) -> Dict:
        return await Role.create(**data)

    @staticmethod
    async def get_one(pk: Union[str, UUID], prefetch_user: bool = False, prefetch_permission: bool = False) -> dict:
        if isinstance(pk, str):
            pk = UUID(pk)
        role = await Role.get_or_none(id=pk).values()
        if role is None:
            return {}

        if prefetch_user:
            role['users'] = await User.filter(roles__role_id=pk).values()
        if prefetch_permission:
            role['permissions'] = await Permission.all().filter(roles__id=pk).values('id', 'name')
        return role

    @staticmethod
    async def get_list(q: RoleQuery, p: Pagination) -> Tuple[List[dict], int]:
        query = Role.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.name is not None:
            query = query.filter(name=q.name)
        if q.name__contains is not None:
            query = query.filter(name__contains=q.name__contains)
        if q.name__icontains is not None:
            query = query.filter(name__icontains=q.name__icontains)
        if q.name__startswith is not None:
            query = query.filter(name__startswith=q.name__startswith)
        if q.name__endswith is not None:
            query = query.filter(name__endswith=q.name__endswith)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = RoleOrderBy(q.order_by)
        records = query.order_by(*order_by.fields) \
            .offset(p.offset).limit(p.limit).values()
        return await records, total

    @staticmethod
    async def update_one(pk: Union[str, UUID], data: Dict) -> Tuple[int, Optional[dict]]:
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        affected_rows = Role.filter(id=pk).update(**data)

        updated_record = None
        if affected_rows > 0:
            updated_record = await Role.get(pk=pk).values()

        return affected_rows, updated_record
