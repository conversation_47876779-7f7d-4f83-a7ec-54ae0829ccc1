import json
from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from decimal import Decimal
from sanic.exceptions import NotFound, BadRequest
from sanic.log import logger
from pydantic import BaseModel, ConfigDict, field_validator
from tortoise.functions import Count
from models.user import User
from repositories.base import Pagination, OrderBy
from api.base import ResponseCode
from utils.security.password import stringify_password, verify_password
from utils.cache import CACHE_MANAGER
from utils.helpers import ExtendedJsonEncoder
from settings import PASSWORD_SALT, PASSWORD_ITERATIONS, PASSWORD_SEP


class UserOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "fullname", "-fullname",
        "min_salary", "-min_salary",
        "expected_salary", "-expected_salary",
        "exp_level", "-exp_level",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
        "status", "-status",
        "provider", "-provider",
        "locale", "-locale",
        "two_factor_enabled", "-two_factor_enabled",
        "auth0_id", "-auth0_id",
        "profession", "-profession",
        "location", "-location",
    )
    _default_order_by = ("created_at",)


class UserQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    email: Optional[str] = None
    email__contains: Optional[str] = None
    email__icontains: Optional[str] = None
    email__startswith: Optional[str] = None
    email__endswith: Optional[str] = None

    fullname: Optional[str] = None
    fullname__contains: Optional[str] = None
    fullname__icontains: Optional[str] = None
    fullname__startswith: Optional[str] = None
    fullname__endswith: Optional[str] = None

    min_salary: Optional[Decimal] = None
    min_salary__gt: Optional[Decimal] = None
    min_salary__gte: Optional[Decimal] = None
    min_salary__lt: Optional[Decimal] = None
    min_salary__lte: Optional[Decimal] = None
    min_salary__isnull: Optional[bool] = None
    min_salary__not_isnull: Optional[bool] = None

    expected_salary: Optional[Decimal] = None
    expected_salary__gt: Optional[Decimal] = None
    expected_salary__gte: Optional[Decimal] = None
    expected_salary__lt: Optional[Decimal] = None
    expected_salary__lte: Optional[Decimal] = None
    expected_salary__isnull: Optional[bool] = None
    expected_salary__not_isnull: Optional[bool] = None

    exp_level: Optional[int] = None
    exp_level__gt: Optional[int] = None
    exp_level__gte: Optional[int] = None
    exp_level__lt: Optional[int] = None
    exp_level__lte: Optional[int] = None
    exp_level__isnull: Optional[bool] = None
    exp_level__not_isnull: Optional[bool] = None

    linked_in: Optional[str] = None

    locale: Optional[str] = None

    two_factor_enabled: Optional[bool] = None

    provider: Optional[User.Provider] = None

    status: Optional[User.Status] = None
    status__in: Optional[List[User.Status]] = None
    status__not_in: Optional[List[User.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    auth0_id: Optional[str] = None
    auth0_id__contains: Optional[str] = None
    auth0_id__icontains: Optional[str] = None
    auth0_id__startswith: Optional[str] = None
    auth0_id__endswith: Optional[str] = None

    profession: Optional[List[str]] = None
    profession__contains: Optional[str] = None
    profession__icontains: Optional[str] = None

    location: Optional[List[str]] = None
    location__contains: Optional[str] = None
    location__icontains: Optional[str] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator("id__in", "id__not_in", mode="before")
    @classmethod
    def validate_id_in(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(",")]
        return v

    @field_validator(
        "fullname", "fullname__contains", "fullname__icontains", "fullname__startswith", "fullname__endswith",
        "linked_in",
        "email", "email__contains", "email__icontains", "email__startswith", "email__endswith",
        "locale",
        "auth0_id", "auth0_id__contains", "auth0_id__icontains", "auth0_id__startswith", "auth0_id__endswith",
        "profession__contains", "profession__icontains",
        "location__contains", "location__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "min_salary", "min_salary__gt", "min_salary__gte", "min_salary__lt", "min_salary__lte",
        "expected_salary", "expected_salary__gt", "expected_salary__gte", "expected_salary__lt", "expected_salary__lte",
        mode="before"
    )
    @classmethod
    def validate_decimal(cls, v: Any) -> Any:
        if isinstance(v, list):
            v = Decimal(v[0])
        elif isinstance(v, str):
            v = Decimal(v)
        return v

    @field_validator(
        "exp_level", "exp_level__gt", "exp_level__gte", "exp_level__lt", "exp_level__lte",
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> Any:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(",")]
        return v

    @field_validator(
        "min_salary__isnull", "min_salary__not_isnull",
        "expected_salary__isnull", "expected_salary__not_isnull",
        "exp_level__isnull", "exp_level__not_isnull",
        "two_factor_enabled",
        mode="before"
    )
    @classmethod
    def validate_boolean(cls, v: Any) -> Any:
        if isinstance(v, list):
            v = v[0]
        if isinstance(v, str):
            return v.lower() in ("true", "1")
        if isinstance(v, int):
            return v == 1
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)

    @field_validator("profession", "location", mode="before")
    @classmethod
    def validate_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return v.split(",")
        return v


class UserRepository:

    USER_BY_ID_KEY = 'user:id:{{{id}}}'
    USER_BY_EMAIL_KEY = 'user:email:{{{email}}}'
    CACHE_EXPIRY = 3600  # 1 hour


    @staticmethod
    async def create_one(user: Dict[str, Any]) -> dict:
        """Create a single user record from a dictionary of user data.

        Args:
            user (Dict[str, Any]): Dictionary containing user fields and values.

        Returns:
            User: The created User instance.
        """
        if "password" in user and user["password"] is not None and user["password"] != "":
            user["password"] = stringify_password(
                user["password"],
                PASSWORD_SALT,
                PASSWORD_ITERATIONS,
                PASSWORD_SEP
            )
        record = await User.create(**user)
        record = dict(record)
        record.pop("password")
        return record

    @staticmethod
    async def create_many(users: List[Dict[str, Any]]) -> List[User]:
        """Create multiple user records in bulk.

        Args:
            users (List[Dict[str, Any]]): List of dictionaries containing user data.

        Returns:
            List[User]: List of created User instances.
        """
        user_models = []
        for user in users:
            if "password" in user and user["password"] is not None and user["password"] != "":
                user["password"] = stringify_password(
                    user["password"],
                    PASSWORD_SALT,
                    PASSWORD_ITERATIONS,
                    PASSWORD_SEP
                )
            user_models.append(User(**user))
        return await User.bulk_create(user_models)

    @staticmethod
    async def get_one(pk: Union[str, UUID]) -> Optional[dict]:
        """Get a single user that matches either the ID or email in the query.

        Args:
            id (Union[str, UUID]): ID of user to search for.

        Returns:
            Optional[dict]: Matching User instance if found, None otherwise.
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        # Try to get from cache first
        cache_key = UserRepository.USER_BY_ID_KEY.format(id=pk)
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cached_data = redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # If cache miss, get from database
        user = await User. \
            get_or_none(id=pk). \
            annotate(total_secrets=Count("secrets")). \
            values(
                "id",
                "email",
                "fullname",
                "picture",
                "min_salary",
                "expected_salary",
                "exp_level",
                "linked_in",
                "locale",
                "two_factor_enabled",
                "provider",
                "status",
                "created_at",
                "updated_at",
                "total_secrets",
                "profession",
                "location"
            )

        # Cache the result if found
        if user and redis:
            redis.setex(cache_key, UserRepository.CACHE_EXPIRY, json.dumps(user, cls=ExtendedJsonEncoder))

        return user

    @staticmethod
    async def get_by_email(email: str) -> Optional[dict]:
        """Get a single user by email.

        Args:
            email (str): The email to search for.

        Returns:
            Optional[dict]: Matching User instance if found, None otherwise.
        """
        # Try to get from cache first
        cache_key = UserRepository.USER_BY_EMAIL_KEY.format(email=email)
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cached_data = redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # If cache miss, get from database
        user = await User. \
            get_or_none(email=email). \
            annotate(total_secrets=Count("secrets")). \
            values(
                "id",
                "email",
                "fullname",
                "picture",
                "min_salary",
                "expected_salary",
                "exp_level",
                "linked_in",
                "locale",
                "two_factor_enabled",
                "provider",
                "status",
                "created_at",
                "updated_at",
                "total_secrets",
                "profession",
                "location"
            )

        # Cache the result if found
        if user and redis:
            redis.setex(cache_key, UserRepository.CACHE_EXPIRY, json.dumps(user, cls=ExtendedJsonEncoder))
            # Also cache by ID for consistency
            id_cache_key = UserRepository.USER_BY_ID_KEY.format(id=user["id"])
            redis.setex(id_cache_key, UserRepository.CACHE_EXPIRY, json.dumps(user, cls=ExtendedJsonEncoder))

        return user

    @staticmethod
    async def login(email: str, password: str) -> Optional[dict]:
        record = await User.get_or_none(email=email).values()
        if record is None:
            logger.warning("email %s not found", email)
            raise NotFound(message="wrong email or password", context={
                "error": ResponseCode.GET_ONE_FAILED,
            })
        if not verify_password(password, record.pop("password")):
            logger.warning("email %s provided wrong password", email)
            raise NotFound(message="wrong email or password", context={
                "error": ResponseCode.GET_ONE_FAILED,
            })
        return record

    @staticmethod
    async def get_list(q: UserQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of users matching the query criteria.

        Args:
            q (UserQuery): Query object containing filter criteria.
            p (Pagination): Pagination parameters for offset and limit.
            as_dict (bool, optional): Whether to return results as dictionaries. Defaults to True.

        Returns:
            Tuple[List[dict], int]: Tuple containing:
                - List of User instances or dictionaries matching the criteria
                - Total count of matching records before pagination
        """
        query = User.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.fullname is not None:
            query = query.filter(fullname=q.fullname)
        if q.fullname__contains is not None:
            query = query.filter(fullname__contains=q.fullname__contains)
        if q.fullname__icontains is not None:
            query = query.filter(fullname__icontains=q.fullname__icontains)
        if q.fullname__startswith is not None:
            query = query.filter(fullname__startswith=q.fullname__startswith)
        if q.fullname__endswith is not None:
            query = query.filter(fullname__endswith=q.fullname__endswith)
        if q.min_salary is not None:
            query = query.filter(min_salary=q.min_salary)
        if q.min_salary__gt is not None:
            query = query.filter(min_salary__gt=q.min_salary__gt)
        if q.min_salary__gte is not None:
            query = query.filter(min_salary__gte=q.min_salary__gte)
        if q.min_salary__lt is not None:
            query = query.filter(min_salary__lt=q.min_salary__lt)
        if q.min_salary__lte is not None:
            query = query.filter(min_salary__lte=q.min_salary__lte)
        if q.min_salary__isnull is not None:
            query = query.filter(min_salary__isnull=q.min_salary__isnull)
        if q.min_salary__not_isnull is not None:
            query = query.filter(
                min_salary__not_isnull=q.min_salary__not_isnull)
        if q.expected_salary is not None:
            query = query.filter(expected_salary=q.expected_salary)
        if q.expected_salary__gt is not None:
            query = query.filter(expected_salary__gt=q.expected_salary__gt)
        if q.expected_salary__gte is not None:
            query = query.filter(expected_salary__gte=q.expected_salary__gte)
        if q.expected_salary__lt is not None:
            query = query.filter(expected_salary__lt=q.expected_salary__lt)
        if q.expected_salary__lte is not None:
            query = query.filter(expected_salary__lte=q.expected_salary__lte)
        if q.expected_salary__isnull is not None:
            query = query.filter(
                expected_salary__isnull=q.expected_salary__isnull)
        if q.expected_salary__not_isnull is not None:
            query = query.filter(
                expected_salary__not_isnull=q.expected_salary__not_isnull)
        if q.exp_level is not None:
            query = query.filter(exp_level=q.exp_level)
        if q.exp_level__gt is not None:
            query = query.filter(exp_level__gt=q.exp_level__gt)
        if q.exp_level__gte is not None:
            query = query.filter(exp_level__gte=q.exp_level__gte)
        if q.exp_level__lt is not None:
            query = query.filter(exp_level__lt=q.exp_level__lt)
        if q.exp_level__lte is not None:
            query = query.filter(exp_level__lte=q.exp_level__lte)
        if q.exp_level__isnull is not None:
            query = query.filter(exp_level__isnull=q.exp_level__isnull)
        if q.exp_level__not_isnull is not None:
            query = query.filter(exp_level__not_isnull=q.exp_level__not_isnull)
        if q.linked_in is not None:
            query = query.filter(linked_in=q.linked_in)
        if q.locale is not None:
            query = query.filter(locale=q.locale)
        if q.two_factor_enabled is not None:
            query = query.filter(two_factor_enabled=q.two_factor_enabled)
        if q.provider is not None:
            query = query.filter(provider=q.provider)
        if q.email is not None:
            query = query.filter(email=q.email)
        if q.email__contains is not None:
            query = query.filter(email__contains=q.email__contains)
        if q.email__icontains is not None:
            query = query.filter(email__icontains=q.email__icontains)
        if q.email__startswith is not None:
            query = query.filter(email__startswith=q.email__startswith)
        if q.email__endswith is not None:
            query = query.filter(email__endswith=q.email__endswith)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)
        if q.auth0_id is not None:
            query = query.filter(auth0_id=q.auth0_id)
        if q.auth0_id__contains is not None:
            query = query.filter(auth0_id__contains=q.auth0_id__contains)
        if q.auth0_id__icontains is not None:
            query = query.filter(auth0_id__icontains=q.auth0_id__icontains)
        if q.auth0_id__startswith is not None:
            query = query.filter(auth0_id__startswith=q.auth0_id__startswith)
        if q.auth0_id__endswith is not None:
            query = query.filter(auth0_id__endswith=q.auth0_id__endswith)
        if q.profession is not None:
            query = query.filter(profession__contains=q.profession)
        if q.profession__contains is not None:
            query = query.filter(profession__contains=q.profession__contains)
        if q.profession__icontains is not None:
            query = query.filter(profession__icontains=q.profession__icontains)
        if q.location is not None:
            query = query.filter(location__contains=q.location)
        if q.location__contains is not None:
            query = query.filter(location__contains=q.location__contains)
        if q.location__icontains is not None:
            query = query.filter(location__icontains=q.location__icontains)

        total = await query.count()
        order_by = UserOrderBy(q.order_by)
        users = await query. \
            annotate(total_secrets=Count("secrets")). \
            order_by(*order_by.fields). \
            offset(p.offset).limit(p.limit).all().values(
                "id",
                "email",
                "fullname",
                "picture",
                "min_salary",
                "expected_salary",
                "exp_level",
                "linked_in",
                "locale",
                "two_factor_enabled",
                "provider",
                "status",
                "created_at",
                "updated_at",
                "auth0_id",
                "profession",
                "location",
                "total_secrets"
            )

        return users, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, User]) -> Tuple[int, Optional[dict]]:
        """Update a user record.

        Args:
            pk (Union[str, uuid.UUID]): Primary key of the user to update
            data (Union[dict, User]): Either a dictionary of user data or User instance to update

        Returns:
            Tuple[int, Optional[dict]]: Tuple containing:
                - Number of rows affected (0 or 1)
                - Updated User instance if update was successful, None otherwise
        """
        if isinstance(pk, str):
            pk = UUID(pk)
        if "updated_at" not in data:
            data["updated_at"] = datetime.now()

        if isinstance(data, dict):
            if "password" in data and data["password"] is not None and data["password"] != "":
                data["password"] = stringify_password(
                    data["password"],
                    PASSWORD_SALT,
                    PASSWORD_ITERATIONS,
                    PASSWORD_SEP
                )
            affected_rows = await User.filter(id=pk).update(**data)
        else:
            if data.password is not None and data.password != "":
                data.password = stringify_password(
                    data.password,
                    PASSWORD_SALT,
                    PASSWORD_ITERATIONS,
                    PASSWORD_SEP
                )
            affected_rows = await User.filter(id=pk).update(data)

        updated_user = None
        if affected_rows > 0:
            updated_user = await User.get(pk=pk).values(
                "id",
                "email",
                "fullname",
                "min_salary",
                "expected_salary",
                "exp_level",
                "linked_in",
                "status",
                "created_at",
                "updated_at"
            )
            # Clear cache for this user
            redis = CACHE_MANAGER.get_connection('default')
            if redis:
                # Clear cache by ID
                id_cache_key = UserRepository.USER_BY_ID_KEY.format(id=pk)
                redis.delete(id_cache_key)

                # Clear cache by email if available
                if updated_user.get("email"):
                    email_cache_key = UserRepository.USER_BY_EMAIL_KEY.format(email=updated_user.get("email"))
                    redis.delete(email_cache_key)

        return affected_rows, updated_user

    @staticmethod
    async def update_by_query(update_data: dict, q: UserQuery) -> int:
        """Update multiple user records that match the given query criteria.

        Args:
            update_data (dict): Dictionary containing the fields and values to update.
            q (UserQuery): Query object containing filter criteria for the users to update.

        Returns:
            int: Number of records affected by the update operation.

        Example:
            >>> query = UserQuery(status=User.Status.ACTIVE, exp_level__gte=5)
            >>> update_data = {"status": User.Status.INACTIVE}
            >>> affected_rows = await UserRepository.update_by_query(update_data, query)
        """
        query = User.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.fullname is not None:
            query = query.filter(fullname=q.fullname)
        if q.fullname__contains is not None:
            query = query.filter(fullname__contains=q.fullname__contains)
        if q.fullname__icontains is not None:
            query = query.filter(fullname__icontains=q.fullname__icontains)
        if q.fullname__startswith is not None:
            query = query.filter(fullname__startswith=q.fullname__startswith)
        if q.fullname__endswith is not None:
            query = query.filter(fullname__endswith=q.fullname__endswith)
        if q.min_salary is not None:
            query = query.filter(min_salary=q.min_salary)
        if q.min_salary__gt is not None:
            query = query.filter(min_salary__gt=q.min_salary__gt)
        if q.min_salary__gte is not None:
            query = query.filter(min_salary__gte=q.min_salary__gte)
        if q.min_salary__lt is not None:
            query = query.filter(min_salary__lt=q.min_salary__lt)
        if q.min_salary__lte is not None:
            query = query.filter(min_salary__lte=q.min_salary__lte)
        if q.min_salary__isnull is not None:
            query = query.filter(min_salary__isnull=q.min_salary__isnull)
        if q.min_salary__not_isnull is not None:
            query = query.filter(
                min_salary__not_isnull=q.min_salary__not_isnull)
        if q.expected_salary is not None:
            query = query.filter(expected_salary=q.expected_salary)
        if q.expected_salary__gt is not None:
            query = query.filter(expected_salary__gt=q.expected_salary__gt)
        if q.expected_salary__gte is not None:
            query = query.filter(expected_salary__gte=q.expected_salary__gte)
        if q.expected_salary__lt is not None:
            query = query.filter(expected_salary__lt=q.expected_salary__lt)
        if q.expected_salary__lte is not None:
            query = query.filter(expected_salary__lte=q.expected_salary__lte)
        if q.expected_salary__isnull is not None:
            query = query.filter(
                expected_salary__isnull=q.expected_salary__isnull)
        if q.expected_salary__not_isnull is not None:
            query = query.filter(
                expected_salary__not_isnull=q.expected_salary__not_isnull)
        if q.exp_level is not None:
            query = query.filter(exp_level=q.exp_level)
        if q.exp_level__gt is not None:
            query = query.filter(exp_level__gt=q.exp_level__gt)
        if q.exp_level__gte is not None:
            query = query.filter(exp_level__gte=q.exp_level__gte)
        if q.exp_level__lt is not None:
            query = query.filter(exp_level__lt=q.exp_level__lt)
        if q.exp_level__lte is not None:
            query = query.filter(exp_level__lte=q.exp_level__lte)
        if q.exp_level__isnull is not None:
            query = query.filter(exp_level__isnull=q.exp_level__isnull)
        if q.exp_level__not_isnull is not None:
            query = query.filter(exp_level__not_isnull=q.exp_level__not_isnull)
        if q.linked_in is not None:
            query = query.filter(linked_in=q.linked_in)
        if q.locale is not None:
            query = query.filter(locale=q.locale)
        if q.two_factor_enabled is not None:
            query = query.filter(two_factor_enabled=q.two_factor_enabled)
        if q.provider is not None:
            query = query.filter(provider=q.provider)
        if q.email is not None:
            query = query.filter(email=q.email)
        if q.email__contains is not None:
            query = query.filter(email__contains=q.email__contains)
        if q.email__icontains is not None:
            query = query.filter(email__icontains=q.email__icontains)
        if q.email__startswith is not None:
            query = query.filter(email__startswith=q.email__startswith)
        if q.email__endswith is not None:
            query = query.filter(email__endswith=q.email__endswith)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)
        if q.auth0_id is not None:
            query = query.filter(auth0_id=q.auth0_id)
        if q.auth0_id__contains is not None:
            query = query.filter(auth0_id__contains=q.auth0_id__contains)
        if q.auth0_id__icontains is not None:
            query = query.filter(auth0_id__icontains=q.auth0_id__icontains)
        if q.auth0_id__startswith is not None:
            query = query.filter(auth0_id__startswith=q.auth0_id__startswith)
        if q.auth0_id__endswith is not None:
            query = query.filter(auth0_id__endswith=q.auth0_id__endswith)

        if "updated_at" not in update_data:
            update_data["updated_at"] = datetime.now()
        if "password" in update_data and update_data["password"] is not None and update_data["password"] != "":
            update_data["password"] = stringify_password(
                update_data["password"],
                PASSWORD_SALT,
                PASSWORD_ITERATIONS,
                PASSWORD_SEP
            )
        return await query.update(**update_data)

    @staticmethod
    async def upsert_by_email(data: Dict[str, Any]) -> Tuple[dict, bool]:
        """Update a user by email if exists, or create a new user if not exists.

        Args:
            data (Dict[str, Any]): Dictionary containing user data including email

        Returns:
            Tuple[dict, bool]: Tuple containing:
                - dict: The created or updated user record
                - bool: True if created, False if updated

        Raises:
            BadRequest: If email is not provided in the data
        """
        if "email" not in data or not data["email"]:
            raise BadRequest(
                message="email is required",
                context={"error": ResponseCode.CREATE_FAILED}
            )

        # Try to find existing user by email
        existing_user = await User.get_or_none(email=data["email"])

        if existing_user:
            # Update existing user
            if "password" in data and data["password"] is not None and data["password"] != "":
                data["password"] = stringify_password(
                    data["password"],
                    PASSWORD_SALT,
                    PASSWORD_ITERATIONS,
                    PASSWORD_SEP
                )
            data["updated_at"] = datetime.now()
            await User.filter(id=existing_user.id).update(**data)

            # Get updated user data
            updated_user = await User.get(id=existing_user.id).values(
                "id",
                "email",
                "fullname",
                "picture",
                "min_salary",
                "expected_salary",
                "exp_level",
                "linked_in",
                "locale",
                "two_factor_enabled",
                "provider",
                "status",
                "auth0_id",
                "created_at",
                "updated_at"
            )
            # Clear cache for this user
            redis = CACHE_MANAGER.get_connection('default')
            if redis:
                # Clear cache by ID
                id_cache_key = UserRepository.USER_BY_ID_KEY.format(id=existing_user.id)
                redis.delete(id_cache_key)

                # Clear cache by email
                email_cache_key = UserRepository.USER_BY_EMAIL_KEY.format(email=data["email"])
                redis.delete(email_cache_key)
            return updated_user, False
        else:
            # Create new user
            created_user = await UserRepository.create_one(data)
            return created_user, True


