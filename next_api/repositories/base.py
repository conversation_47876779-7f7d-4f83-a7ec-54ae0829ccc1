import os
from typing import Union
from sanic_ext import openapi
from pydantic import BaseModel, Field


class Pagination:
    __page: int = 1
    __page_size: int = 100

    __max_page_size: int = int(os.getenv("MAX_PAGE_SIZE", '1000'))

    def __init__(self, page: Union[int, str] = 1, page_size: Union[int, str] = 100):
        self.page = int(page)
        self.page_size = int(page_size)

    @property
    def page(self) -> int:
        return self.__page

    @page.setter
    def page(self, value: int) -> None:
        self.__page = max(1, value)

    @property
    def page_size(self) -> int:
        return self.__page_size

    @page_size.setter
    def page_size(self, value: int) -> None:
        self.__page_size = min(self.__max_page_size, value)

    @property
    def offset(self) -> int:
        return (self.__page - 1) * self.page_size

    @property
    def limit(self) -> int:
        return min(self.__page_size, self.__max_page_size)


@openapi.component
class PaginationQuery(BaseModel):
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=100, ge=1, description="Number of records per page")


class OrderBy:
    _allows_fields: tuple[str] = []
    _fields: list[str] = []
    _default_order_by: tuple[str] = []

    def __init__(self, fields: any = None):
        self.fields = fields

    @property
    def fields(self) -> tuple[str]:
        return tuple(self._fields)

    @fields.setter
    def fields(self, value: list[str]) -> None:
        if value is None or not value:
            self._fields = self._default_order_by
        else:
            if isinstance(value, tuple):
                value = list(value)
            if isinstance(value, str):
                value = [v.strip() for v in value.split(",") if v.strip() != ""]
            self._fields = [
                field for field in value if field in self._allows_fields
            ]
