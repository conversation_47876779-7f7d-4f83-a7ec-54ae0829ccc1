import json
from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from models.article import Article, ArticleLike
from repositories.base import Pagination, OrderBy
from utils.cache import CACHE_MANAGER
from tortoise.transactions import in_transaction
from utils.helpers import ExtendedJsonEncoder

class ArticleOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "title", "-title",
        "author", "-author",
        "published_at", "-published_at",
        "likes_count", "-likes_count",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class ArticleQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    title: Optional[str] = None
    title__icontains: Optional[str] = None

    content: Optional[str] = None
    content__icontains: Optional[str] = None

    author: Optional[str] = None
    author__icontains: Optional[str] = None

    published_at: Optional[datetime] = None
    published_at__gte: Optional[datetime] = None
    published_at__lte: Optional[datetime] = None

    likes_count: Optional[int] = None
    likes_count__gte: Optional[int] = None
    likes_count__lte: Optional[int] = None

    tags: Optional[List[str]] = None
    tags__contains: Optional[List[str]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator("id__in", "id__not_in", mode="before")
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "published_at", "published_at__gte", "published_at__lte",
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class ArticleLikeQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    article_id: Optional[UUID] = None
    article_id__in: Optional[List[UUID]] = None
    article_id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", "article_id", "user_id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "article_id__in", "article_id__not_in",
        "user_id__in", "user_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v


class ArticleRepository:
    ARTICLE_CACHE_KEY = 'article:{{{article_id}}}'
    CACHE_EXPIRY = 3600  # 1 hour

    @staticmethod
    async def create_one(article: Dict[str, Any]) -> dict:
        """Create a single article record."""
        record = await Article.create(**article)
        return dict(record)

    @staticmethod
    async def create_many(articles: List[Dict[str, Any]]) -> List[Article]:
        """Create multiple article records in bulk."""
        records = await Article.bulk_create([Article(**article) for article in articles])
        return records

    @staticmethod
    async def get_one(q: ArticleQuery) -> Optional[dict]:
        """Get a single article record that matches the ID in the query."""
        if not q.id:
            return None

        # Try to get from cache first
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cache_key = ArticleRepository.ARTICLE_CACHE_KEY.format(article_id=str(q.id))
            cached_data = redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # If cache miss, get from database
        article = await Article.get_or_none(id=q.id).values()

        # Cache the result if found
        if article and redis:
            cache_key = ArticleRepository.ARTICLE_CACHE_KEY.format(article_id=str(q.id))
            redis.setex(cache_key, ArticleRepository.CACHE_EXPIRY, json.dumps(article, cls=ExtendedJsonEncoder))

        return article

    @staticmethod
    async def get_list(q: ArticleQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of articles matching the query criteria."""
        query = Article.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.title is not None:
            query = query.filter(title=q.title)
        if q.title__icontains is not None:
            query = query.filter(title__icontains=q.title__icontains)
        if q.content is not None:
            query = query.filter(content=q.content)
        if q.content__icontains is not None:
            query = query.filter(content__icontains=q.content__icontains)
        if q.author is not None:
            query = query.filter(author=q.author)
        if q.author__icontains is not None:
            query = query.filter(author__icontains=q.author__icontains)
        if q.published_at is not None:
            query = query.filter(published_at=q.published_at)
        if q.published_at__gte is not None:
            query = query.filter(published_at__gte=q.published_at__gte)
        if q.published_at__lte is not None:
            query = query.filter(published_at__lte=q.published_at__lte)
        if q.likes_count is not None:
            query = query.filter(likes_count=q.likes_count)
        if q.likes_count__gte is not None:
            query = query.filter(likes_count__gte=q.likes_count__gte)
        if q.likes_count__lte is not None:
            query = query.filter(likes_count__lte=q.likes_count__lte)
        if q.tags is not None:
            query = query.filter(tags=q.tags)
        if q.tags__contains is not None:
            query = query.filter(tags__contains=q.tags__contains)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = ArticleOrderBy(q.order_by)
        articles = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()

        return articles, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, Article]) -> Tuple[int, Optional[dict]]:
        """Update an article record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await Article.filter(id=pk).update(**data)
        else:
            affected_rows = await Article.filter(id=pk).update(data)

        updated_article = None
        if affected_rows > 0:
            updated_article = await Article.get(pk=pk).values()
            # Update cache
            redis = CACHE_MANAGER.get_connection('default')
            if redis:
                cache_key = ArticleRepository.ARTICLE_CACHE_KEY.format(article_id=str(pk))
                redis.setex(cache_key, ArticleRepository.CACHE_EXPIRY, json.dumps(updated_article))

        return affected_rows, updated_article

    @staticmethod
    def clear_cache(article_id: UUID):
        """Clear the cache for a specific article."""
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cache_key = ArticleRepository.ARTICLE_CACHE_KEY.format(article_id=str(article_id))
            redis.delete(cache_key)


class ArticleLikeRepository:
    ARTICLE_LIKES_CACHE_KEY = 'article:likes:{{{article_id}}}'
    USER_LIKES_CACHE_KEY = 'user:likes:{{{user_id}}}'
    CACHE_EXPIRY = 3600  # 1 hour

    @staticmethod
    async def create_one(article_like: Dict[str, Any]) -> dict:
        """Create a single article like record."""
        async with in_transaction():
            record = await ArticleLike.create(**article_like)
            # Update article likes count
            await Article.filter(id=article_like['article_id']).update(
                likes_count=Article.likes_count + 1
            )
            # Clear caches
            ArticleLikeRepository.clear_cache(
                article_id=article_like['article_id'],
                user_id=article_like['user_id']
            )
            ArticleRepository.clear_cache(article_like['article_id'])
            return dict(record)

    @staticmethod
    async def get_one(q: ArticleLikeQuery) -> Optional[dict]:
        """Get a single article like record that matches the ID in the query."""
        return await ArticleLike.get_or_none(id=q.id).values()

    @staticmethod
    async def get_list(q: ArticleLikeQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of article likes matching the query criteria."""
        # Try to get from cache if querying by article_id
        redis = CACHE_MANAGER.get_connection('default')
        if redis and q.article_id and not any([
            q.id, q.id__in, q.id__not_in,
            q.user_id, q.user_id__in, q.user_id__not_in,
            q.created_at, q.created_at__gte, q.created_at__lte
        ]):
            cache_key = ArticleLikeRepository.ARTICLE_LIKES_CACHE_KEY.format(
                article_id=str(q.article_id)
            )
            cached_data = redis.get(cache_key)
            if cached_data:
                data = json.loads(cached_data)
                start = p.offset
                end = p.offset + p.limit
                return data[start:end], len(data)

        query = ArticleLike.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.article_id is not None:
            query = query.filter(article_id=q.article_id)
        if q.article_id__in is not None:
            query = query.filter(article_id__in=q.article_id__in)
        if q.article_id__not_in is not None:
            query = query.filter(article_id__not_in=q.article_id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        total = await query.count()
        likes = await query.offset(p.offset).limit(p.limit).all().values()

        # Cache results if querying by article_id
        if redis and q.article_id and not any([
            q.id, q.id__in, q.id__not_in,
            q.user_id, q.user_id__in, q.user_id__not_in,
            q.created_at, q.created_at__gte, q.created_at__lte
        ]):
            cache_key = ArticleLikeRepository.ARTICLE_LIKES_CACHE_KEY.format(
                article_id=str(q.article_id)
            )
            redis.setex(cache_key, ArticleLikeRepository.CACHE_EXPIRY, json.dumps(likes))

        return likes, total

    @staticmethod
    async def delete(article_id: UUID, user_id: UUID) -> int:
        """Delete an article like record."""
        async with in_transaction():
            deleted = await ArticleLike.filter(
                article_id=article_id,
                user_id=user_id
            ).delete()
            if deleted:
                # Update article likes count
                await Article.filter(id=article_id).update(
                    likes_count=Article.likes_count - 1
                )
                # Clear caches
                ArticleLikeRepository.clear_cache(article_id=article_id, user_id=user_id)
                ArticleRepository.clear_cache(article_id)
            return deleted

    @staticmethod
    def clear_cache(article_id: UUID, user_id: UUID):
        """Clear caches related to article likes."""
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            article_cache_key = ArticleLikeRepository.ARTICLE_LIKES_CACHE_KEY.format(
                article_id=str(article_id)
            )
            user_cache_key = ArticleLikeRepository.USER_LIKES_CACHE_KEY.format(
                user_id=str(user_id)
            )
            redis.delete(article_cache_key, user_cache_key)
