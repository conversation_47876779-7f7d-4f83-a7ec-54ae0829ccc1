from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from sanic_ext import openapi
from pydantic import BaseModel, ConfigDict, field_validator, Field
from models.question import Question
from repositories.base import Pagination, OrderBy


class QuestionOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "status", "-status",
        "question", "-question",
        "answer", "-answer",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


@openapi.component
class QuestionQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = Field(None, description="Filter by question ID")
    id__in: Optional[List[UUID]] = Field(None, description="Filter by multiple question IDs (comma-separated)")
    id__not_in: Optional[List[UUID]] = Field(None, description="Exclude multiple question IDs (comma-separated)")

    question: Optional[str] = Field(None, description="Filter by exact question content match")
    question__contains: Optional[str] = Field(
        None, description="Filter by question content containing string (case sensitive)")
    question__icontains: Optional[str] = Field(
        None, description="Filter by question content containing string (case insensitive)")

    answer: Optional[str] = None
    answer__contains: Optional[str] = None
    answer__icontains: Optional[str] = None

    difficulty: Optional[Question.Difficulty] = None
    difficulty__in: Optional[List[Question.Difficulty]] = None
    difficulty__not_in: Optional[List[Question.Difficulty]] = None

    status: Optional[Question.Status] = None
    status__in: Optional[List[Question.Status]] = None
    status__not_in: Optional[List[Question.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    user_answer: Optional[str] = None
    user_answer__contains: Optional[str] = None
    user_answer__icontains: Optional[str] = None

    hint: Optional[str] = None
    hint__contains: Optional[str] = None
    hint__icontains: Optional[str] = None

    point: Optional[int] = None
    point__gte: Optional[int] = None
    point__lte: Optional[int] = None

    category: Optional[str] = None
    category__contains: Optional[str] = None
    category__icontains: Optional[str] = None

    overall_strengths: Optional[List[str]] = None
    overall_strengths__contains: Optional[str] = None
    overall_strengths__icontains: Optional[str] = None

    overall_improvements: Optional[List[str]] = None
    overall_improvements__contains: Optional[str] = None
    overall_improvements__icontains: Optional[str] = None

    analysis_situation: Optional[str] = None
    analysis_situation__contains: Optional[str] = None
    analysis_situation__icontains: Optional[str] = None

    analysis_task: Optional[str] = None
    analysis_task__contains: Optional[str] = None
    analysis_task__icontains: Optional[str] = None

    analysis_action: Optional[str] = None
    analysis_action__contains: Optional[str] = None
    analysis_action__icontains: Optional[str] = None

    analysis_result: Optional[str] = None
    analysis_result__contains: Optional[str] = None
    analysis_result__icontains: Optional[str] = None

    analysis_skills: Optional[str] = None
    analysis_skills__contains: Optional[str] = None
    analysis_skills__icontains: Optional[str] = None

    analysis_academic: Optional[str] = None
    analysis_academic__contains: Optional[str] = None
    analysis_academic__icontains: Optional[str] = None

    analysis_management: Optional[str] = None
    analysis_management__contains: Optional[str] = None
    analysis_management__icontains: Optional[str] = None

    analysis_personal: Optional[str] = None
    analysis_personal__contains: Optional[str] = None
    analysis_personal__icontains: Optional[str] = None

    analysis_seek_info: Optional[str] = None
    analysis_seek_info__contains: Optional[str] = None
    analysis_seek_info__icontains: Optional[str] = None

    analysis_patient_safety: Optional[str] = None
    analysis_patient_safety__contains: Optional[str] = None
    analysis_patient_safety__icontains: Optional[str] = None

    analysis_initiative: Optional[str] = None
    analysis_initiative__contains: Optional[str] = None
    analysis_initiative__icontains: Optional[str] = None

    analysis_escalate: Optional[str] = None
    analysis_escalate__contains: Optional[str] = None
    analysis_escalate__icontains: Optional[str] = None

    analysis_support: Optional[str] = None
    analysis_support__contains: Optional[str] = None
    analysis_support__icontains: Optional[str] = None

    analysis_strategy: Optional[str] = None
    analysis_strategy__contains: Optional[str] = None
    analysis_strategy__icontains: Optional[str] = None

    analysis_technology: Optional[str] = None
    analysis_technology__contains: Optional[str] = None
    analysis_technology__icontains: Optional[str] = None

    analysis_analytics: Optional[str] = None
    analysis_analytics__contains: Optional[str] = None
    analysis_analytics__icontains: Optional[str] = None

    analysis_results: Optional[str] = None
    analysis_results__contains: Optional[str] = None
    analysis_results__icontains: Optional[str] = None

    analysis_transformation: Optional[str] = None
    analysis_transformation__contains: Optional[str] = None
    analysis_transformation__icontains: Optional[str] = None

    @field_validator(
        "id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "question", "question__contains", "question__icontains",
        "answer", "answer__contains", "answer__icontains",
        "user_answer", "user_answer__contains", "user_answer__icontains",
        "hint", "hint__contains", "hint__icontains",
        "category", "category__contains", "category__icontains",
        "analysis_situation", "analysis_situation__contains", "analysis_situation__icontains",
        "analysis_task", "analysis_task__contains", "analysis_task__icontains",
        "analysis_action", "analysis_action__contains", "analysis_action__icontains",
        "analysis_result", "analysis_result__contains", "analysis_result__icontains",
        "analysis_skills", "analysis_skills__contains", "analysis_skills__icontains",
        "analysis_academic", "analysis_academic__contains", "analysis_academic__icontains",
        "analysis_management", "analysis_management__contains", "analysis_management__icontains",
        "analysis_personal", "analysis_personal__contains", "analysis_personal__icontains",
        "analysis_seek_info", "analysis_seek_info__contains", "analysis_seek_info__icontains",
        "analysis_patient_safety", "analysis_patient_safety__contains", "analysis_patient_safety__icontains",
        "analysis_initiative", "analysis_initiative__contains", "analysis_initiative__icontains",
        "analysis_escalate", "analysis_escalate__contains", "analysis_escalate__icontains",
        "analysis_support", "analysis_support__contains", "analysis_support__icontains",
        "analysis_strategy", "analysis_strategy__contains", "analysis_strategy__icontains",
        "analysis_technology", "analysis_technology__contains", "analysis_technology__icontains",
        "analysis_analytics", "analysis_analytics__contains", "analysis_analytics__icontains",
        "analysis_results", "analysis_results__contains", "analysis_results__icontains",
        "analysis_transformation", "analysis_transformation__contains", "analysis_transformation__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "status", "difficulty", "point",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        "difficulty__in", "difficulty__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)

    @field_validator(
        "overall_strengths", "overall_improvements",
        mode="before"
    )
    @classmethod
    def validate_json_array(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [v]
        return v


class QuestionRepository:

    @staticmethod
    async def create_one(question: Dict[str, Any]) -> Question:
        """Create a single question record from a dictionary of question data."""
        return await Question.create(**question)

    @staticmethod
    async def create_many(questions: List[Dict[str, Any]]) -> List[Question]:
        """Create multiple question records in bulk."""
        return await Question.bulk_create([Question(**question) for question in questions])

    @staticmethod
    async def get_one(q: QuestionQuery) -> Optional[Question]:
        """Get a single question that matches the ID in the query."""
        question = await Question.get_or_none(id=q.id)
        return question

    @staticmethod
    async def get_list(q: QuestionQuery, p: Pagination, as_dict: bool = True) \
            -> Tuple[Union[List[dict], List[Question]], int]:
        """Get a paginated list of questions matching the query criteria."""
        query = Question.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.question is not None:
            query = query.filter(question=q.question)
        if q.question__contains is not None:
            query = query.filter(question__contains=q.question__contains)
        if q.question__icontains is not None:
            query = query.filter(question__icontains=q.question__icontains)
        if q.answer is not None:
            query = query.filter(answer=q.answer)
        if q.answer__contains is not None:
            query = query.filter(answer__contains=q.answer__contains)
        if q.answer__icontains is not None:
            query = query.filter(answer__icontains=q.answer__icontains)
        if q.difficulty is not None:
            query = query.filter(difficulty=q.difficulty)
        if q.difficulty__in is not None:
            query = query.filter(difficulty__in=q.difficulty__in)
        if q.difficulty__not_in is not None:
            query = query.filter(difficulty__not_in=q.difficulty__not_in)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        if q.user_answer is not None:
            query = query.filter(user_answer=q.user_answer)
        if q.user_answer__contains is not None:
            query = query.filter(user_answer__contains=q.user_answer__contains)
        if q.user_answer__icontains is not None:
            query = query.filter(user_answer__icontains=q.user_answer__icontains)

        if q.hint is not None:
            query = query.filter(hint=q.hint)
        if q.hint__contains is not None:
            query = query.filter(hint__contains=q.hint__contains)
        if q.hint__icontains is not None:
            query = query.filter(hint__icontains=q.hint__icontains)

        if q.point is not None:
            query = query.filter(point=q.point)
        if q.point__gte is not None:
            query = query.filter(point__gte=q.point__gte)
        if q.point__lte is not None:
            query = query.filter(point__lte=q.point__lte)

        if q.category is not None:
            query = query.filter(category=q.category)
        if q.category__contains is not None:
            query = query.filter(category__contains=q.category__contains)
        if q.category__icontains is not None:
            query = query.filter(category__icontains=q.category__icontains)

        if q.overall_strengths is not None:
            query = query.filter(overall_strengths__contains=q.overall_strengths)
        if q.overall_strengths__contains is not None:
            query = query.filter(overall_strengths__contains=q.overall_strengths__contains)
        if q.overall_strengths__icontains is not None:
            query = query.filter(overall_strengths__icontains=q.overall_strengths__icontains)

        if q.overall_improvements is not None:
            query = query.filter(overall_improvements__contains=q.overall_improvements)
        if q.overall_improvements__contains is not None:
            query = query.filter(overall_improvements__contains=q.overall_improvements__contains)
        if q.overall_improvements__icontains is not None:
            query = query.filter(overall_improvements__icontains=q.overall_improvements__icontains)

        for field in [
            'analysis_situation', 'analysis_task', 'analysis_action', 'analysis_result',
            'analysis_skills', 'analysis_academic', 'analysis_management', 'analysis_personal',
            'analysis_seek_info', 'analysis_patient_safety', 'analysis_initiative',
            'analysis_escalate', 'analysis_support', 'analysis_strategy', 'analysis_technology',
            'analysis_analytics', 'analysis_results', 'analysis_transformation'
        ]:
            if getattr(q, field) is not None:
                query = query.filter(**{field: getattr(q, field)})
            if getattr(q, f"{field}__contains") is not None:
                query = query.filter(**{f"{field}__contains": getattr(q, f"{field}__contains")})
            if getattr(q, f"{field}__icontains") is not None:
                query = query.filter(**{f"{field}__icontains": getattr(q, f"{field}__icontains")})

        total = await query.count()
        order_by = QuestionOrderBy(q.order_by)
        if as_dict:
            questions = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()
        else:
            questions = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all()

        return questions, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, Question]) -> Tuple[int, Optional[Question]]:
        """Update a question record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await Question.filter(id=pk).update(**data)
        else:
            affected_rows = await Question.filter(id=pk).update(data)

        updated_question = None
        if affected_rows > 0:
            updated_question = await Question.get(pk=pk)

        return affected_rows, updated_question

    @staticmethod
    async def update_by_query(update_data: dict, q: QuestionQuery) -> int:
        """Update multiple question records that match the given query criteria."""
        query = Question.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.question is not None:
            query = query.filter(question=q.question)
        if q.question__contains is not None:
            query = query.filter(question__contains=q.question__contains)
        if q.question__icontains is not None:
            query = query.filter(question__icontains=q.question__icontains)
        if q.answer is not None:
            query = query.filter(answer=q.answer)
        if q.answer__contains is not None:
            query = query.filter(answer__contains=q.answer__contains)
        if q.answer__icontains is not None:
            query = query.filter(answer__icontains=q.answer__icontains)
        if q.difficulty is not None:
            query = query.filter(difficulty=q.difficulty)
        if q.difficulty__in is not None:
            query = query.filter(difficulty__in=q.difficulty__in)
        if q.difficulty__not_in is not None:
            query = query.filter(difficulty__not_in=q.difficulty__not_in)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if 'updated_at' not in update_data:
            update_data['updated_at'] = datetime.now()
        return await query.update(**update_data)
