import json
from typing import Dict, Any, Tuple, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from repositories.base import Pagination, OrderBy
from models.search_job import SearchJob
from utils.cache import CACHE_MANAGER


class SearchJobOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "title", "-title",
        "company", "-company",
        "location", "-location",
        "salary", "-salary",
        "posted_at", "-posted_at",
        "is_remote", "-is_remote",
        "source", "-source",
        "category", "-category",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
        "keywords", "-keywords",
    )
    _default_order_by = ("created_at",)


class SearchJobQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    title: Optional[str] = None
    title__icontains: Optional[str] = None

    company: Optional[str] = None
    company__icontains: Optional[str] = None

    location: Optional[str] = None
    location__icontains: Optional[str] = None

    salary__gte: Optional[float] = None
    salary__lte: Optional[float] = None

    description__icontains: Optional[str] = None

    posted_at: Optional[str] = None
    posted_at__gte: Optional[str] = None
    posted_at__lte: Optional[str] = None

    is_remote: Optional[bool] = None

    source: Optional[str] = None
    source__in: Optional[List[str]] = None

    category: Optional[str] = None
    category__in: Optional[List[str]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    keywords: Optional[List[str]] = None
    keywords__contains: Optional[str] = None
    keywords__overlap: Optional[List[str]] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        return v

    @field_validator(
        "id__in", "id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> List[UUID]:
        if isinstance(v, list):
            return [UUID(x) if isinstance(x, str) else x for x in v]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v


class SearchJobRepository:

    SEARCH_JOB_KEY = 'search_job:{{{id}}}'

    @staticmethod
    async def create_one(job: Dict[str, Any]) -> dict:
        """
        Create a new job posting
        """
        job_obj = await SearchJob.create(**job)
        return dict(job_obj)

    @staticmethod
    async def get_one(pk: Union[str, UUID]) -> Optional[dict]:
        """
        Get a single job posting by ID
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        cache_key = SearchJobRepository.SEARCH_JOB_KEY.format(id=pk)
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cached = redis.get(cache_key)
            if cached:
                return json.loads(cached)

        job = await SearchJob.get_or_none(id=pk)
        if job:
            job_dict = {
                "id": str(job.id),
                "title": job.title,
                "company": job.company,
                "location": job.location,
                "salary": float(job.salary) if job.salary else None,
                "description": job.description,
                "link": job.link,
                "posted_at": job.posted_at,
                "is_remote": job.is_remote,
                "source": job.source,
                "category": job.category,
                "keywords": job.keywords,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "updated_at": job.updated_at.isoformat() if job.updated_at else None,
            }
            if redis:
                redis.set(cache_key, json.dumps(job_dict))
            return job_dict
        return None

    @staticmethod
    async def get_list(q: SearchJobQuery, p: Pagination) -> Tuple[List[dict], int]:
        """
        Get a list of job postings with pagination
        """
        query = SearchJob.all()

        if q.id:
            query = query.filter(id=q.id)
        if q.id__in:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in:
            query = query.filter(id__not_in=q.id__not_in)

        if q.title:
            query = query.filter(title=q.title)
        if q.title__icontains:
            query = query.filter(title__icontains=q.title__icontains)

        if q.company:
            query = query.filter(company=q.company)
        if q.company__icontains:
            query = query.filter(company__icontains=q.company__icontains)

        if q.location:
            query = query.filter(location=q.location)
        if q.location__icontains:
            query = query.filter(location__icontains=q.location__icontains)

        if q.salary__gte is not None:
            query = query.filter(salary__gte=q.salary__gte)
        if q.salary__lte is not None:
            query = query.filter(salary__lte=q.salary__lte)

        if q.description__icontains:
            query = query.filter(description__icontains=q.description__icontains)

        if q.posted_at:
            query = query.filter(posted_at=q.posted_at)
        if q.posted_at__gte:
            query = query.filter(posted_at__gte=q.posted_at__gte)
        if q.posted_at__lte:
            query = query.filter(posted_at__lte=q.posted_at__lte)

        if q.is_remote is not None:
            query = query.filter(is_remote=q.is_remote)

        if q.source:
            query = query.filter(source=q.source)
        if q.source__in:
            query = query.filter(source__in=q.source__in)

        if q.category:
            query = query.filter(category=q.category)
        if q.category__in:
            query = query.filter(category__in=q.category__in)

        if q.created_at:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        order_by = SearchJobOrderBy(q.order_by)
        if order_by.fields:
            query = query.order_by(*order_by.fields)

        total = await query.count()
        jobs = await query.offset(p.offset).limit(p.limit)

        result = []
        for job in jobs:
            job_dict = {
                "id": str(job.id),
                "title": job.title,
                "company": job.company,
                "location": job.location,
                "salary": float(job.salary) if job.salary else None,
                "description": job.description,
                "link": job.link,
                "posted_at": job.posted_at,
                "is_remote": job.is_remote,
                "source": job.source,
                "category": job.category,
                "keywords": job.keywords,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "updated_at": job.updated_at.isoformat() if job.updated_at else None,
            }
            result.append(job_dict)

        return result, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Dict[str, Any]) -> Tuple[int, Optional[dict]]:
        """
        Update a job posting
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        job = await SearchJob.get_or_none(id=pk)
        if not job:
            return 0, None

        # Set updated_at to current datetime if not provided
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()

        await job.update_from_dict(data).save()
        SearchJobRepository.clear_cache(pk)
        return 1, await SearchJobRepository.get_one(pk)

    @staticmethod
    async def update_by_query(update_data: dict, q: SearchJobQuery) -> int:
        """
        Update multiple job postings based on query
        """
        # Set updated_at to current datetime if not provided
        if 'updated_at' not in update_data:
            update_data['updated_at'] = datetime.now()

        query = SearchJob.all()

        if q.id:
            query = query.filter(id=q.id)
        if q.id__in:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in:
            query = query.filter(id__not_in=q.id__not_in)

        if q.title:
            query = query.filter(title=q.title)
        if q.title__icontains:
            query = query.filter(title__icontains=q.title__icontains)

        if q.company:
            query = query.filter(company=q.company)
        if q.company__icontains:
            query = query.filter(company__icontains=q.company__icontains)

        if q.location:
            query = query.filter(location=q.location)
        if q.location__icontains:
            query = query.filter(location__icontains=q.location__icontains)

        if q.salary__gte is not None:
            query = query.filter(salary__gte=q.salary__gte)
        if q.salary__lte is not None:
            query = query.filter(salary__lte=q.salary__lte)

        if q.description__icontains:
            query = query.filter(description__icontains=q.description__icontains)

        if q.posted_at:
            query = query.filter(posted_at=q.posted_at)
        if q.posted_at__gte:
            query = query.filter(posted_at__gte=q.posted_at__gte)
        if q.posted_at__lte:
            query = query.filter(posted_at__lte=q.posted_at__lte)

        if q.is_remote is not None:
            query = query.filter(is_remote=q.is_remote)

        if q.source:
            query = query.filter(source=q.source)
        if q.source__in:
            query = query.filter(source__in=q.source__in)

        if q.category:
            query = query.filter(category=q.category)
        if q.category__in:
            query = query.filter(category__in=q.category__in)

        if q.created_at:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        if q.keywords:
            query = query.filter(keywords=q.keywords)
        if q.keywords__contains:
            query = query.filter(keywords__contains=q.keywords__contains)
        if q.keywords__overlap:
            query = query.filter(keywords__overlap=q.keywords__overlap)

        updated_count = await query.update(**update_data)
        return updated_count

    @staticmethod
    def clear_cache(id: UUID):
        """
        Clear cache for a job posting
        """
        cache_key = SearchJobRepository.SEARCH_JOB_KEY.format(id=id)
        redis = CACHE_MANAGER.get_connection('default')
        redis.delete(cache_key)
