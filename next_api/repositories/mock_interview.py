from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from models.mock_interview import MockInterview
from models.question import Question
from models.mock_interview_question import MockInterviewQuestion
from repositories.base import Pagination, OrderBy


class MockInterviewOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "position", "-position",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class MockInterviewQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    job_description_id: Optional[UUID] = None
    job_description_id__in: Optional[List[UUID]] = None
    job_description_id__not_in: Optional[List[UUID]] = None

    position: Optional[str] = None
    position__contains: Optional[str] = None
    position__icontains: Optional[str] = None

    job_description: Optional[str] = None
    job_description__contains: Optional[str] = None
    job_description__icontains: Optional[str] = None

    status: Optional[MockInterview.Status] = None
    status__in: Optional[List[MockInterview.Status]] = None
    status__not_in: Optional[List[MockInterview.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "user_id", "job_description_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        "job_description_id__in", "job_description_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "position", "position__contains", "position__icontains",
        "job_description", "job_description__contains", "job_description__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> Any:
        if isinstance(v, str):
            return int(v)
        if isinstance(v, list):
            return int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class MockInterviewRepository:

    @staticmethod
    async def create_one(mock_interview: Dict[str, Any]) -> MockInterview:
        """Create a single mock interview record from a dictionary of mock interview data."""
        return await MockInterview.create(**mock_interview)

    @staticmethod
    async def create_many(mock_interviews: List[Dict[str, Any]]) -> List[MockInterview]:
        """Create multiple mock interview records in bulk."""
        return await MockInterview.bulk_create([MockInterview(**mock_interview) for mock_interview in mock_interviews])

    @staticmethod
    async def get_one(q: MockInterviewQuery) -> Optional[MockInterview]:
        """Get a single mock interview that matches the ID in the query.

        Args:
            q: Query parameters
        """
        mock_interview = await MockInterview.get_or_none(id=q.id)
        return mock_interview

    @staticmethod
    async def get_list(q: MockInterviewQuery, p: Pagination, as_dict: bool = True) \
            -> Tuple[Union[List[dict], List[MockInterview]], int]:
        """Get a paginated list of mock interviews matching the query criteria."""
        query = MockInterview.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.job_description_id is not None:
            query = query.filter(job_description_id=q.job_description_id)
        if q.job_description_id__in is not None:
            query = query.filter(job_description_id__in=q.job_description_id__in)
        if q.job_description_id__not_in is not None:
            query = query.filter(job_description_id__not_in=q.job_description_id__not_in)
        if q.position is not None:
            query = query.filter(position=q.position)
        if q.position__contains is not None:
            query = query.filter(position__contains=q.position__contains)
        if q.position__icontains is not None:
            query = query.filter(position__icontains=q.position__icontains)
        if q.job_description__contains is not None:
            query = query.filter(
                job_description__contains=q.job_description__contains)
        if q.job_description__icontains is not None:
            query = query.filter(
                job_description__icontains=q.job_description__icontains)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = MockInterviewOrderBy(q.order_by)
        if as_dict:
            mock_interviews = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()
        else:
            mock_interviews = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all()

        return mock_interviews, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, MockInterview]) -> Tuple[int, Optional[MockInterview]]:
        """Update a mock interview record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await MockInterview.filter(id=pk).update(**data)
        else:
            affected_rows = await MockInterview.filter(id=pk).update(data)

        updated_mock_interview = None
        if affected_rows > 0:
            updated_mock_interview = await MockInterview.get(pk=pk)

        return affected_rows, updated_mock_interview

    @staticmethod
    async def update_by_query(update_data: dict, q: MockInterviewQuery) -> int:
        """Update multiple mock interview records that match the given query criteria."""
        query = MockInterview.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.position is not None:
            query = query.filter(position=q.position)
        if q.position__contains is not None:
            query = query.filter(position__contains=q.position__contains)
        if q.position__icontains is not None:
            query = query.filter(position__icontains=q.position__icontains)
        if q.job_description__contains is not None:
            query = query.filter(
                job_description__contains=q.job_description__contains)
        if q.job_description__icontains is not None:
            query = query.filter(
                job_description__icontains=q.job_description__icontains)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if 'updated_at' not in update_data:
            update_data['updated_at'] = datetime.now()
        return await query.update(**update_data)

    @staticmethod
    async def get_questions(
        mock_interview_id: Union[str, UUID],
        p: Pagination,
        as_dict: bool = False
    ) -> Tuple[Union[List[dict], List['Question']], int]:
        """Get a paginated list of questions for a mock interview."""
        if isinstance(mock_interview_id, str):
            mock_interview_id = UUID(mock_interview_id)

        # Get the mock interview questions through the junction table
        query = Question.filter(
            mock_interview_questions__mock_interview_id=mock_interview_id
        )

        total = await query.count()

        if as_dict:
            questions = await query.offset(p.offset).limit(p.limit).values()
        else:
            questions = await query.offset(p.offset).limit(p.limit)

        return questions, total

    @staticmethod
    async def add_questions(mock_interview_id: Union[str, UUID], question_ids: List[Union[str, UUID]]) \
            -> Tuple[Optional[MockInterview], int]:
        """Add questions to an existing mock interview."""
        if isinstance(mock_interview_id, str):
            mock_interview_id = UUID(mock_interview_id)

        # Convert string IDs to UUID if necessary
        question_ids = [UUID(qid) if isinstance(qid, str)
                        else qid for qid in question_ids]

        # Get the mock interview
        mock_interview = await MockInterview.get_or_none(id=mock_interview_id)
        if not mock_interview:
            return None, 0

        # Add questions through the junction table
        await MockInterviewQuestion.bulk_create([
            MockInterviewQuestion(
                mock_interview_id=mock_interview_id, question_id=qid)
            for qid in question_ids
        ])

        # Get the total count of questions
        question_count = await Question.filter(
            mock_interview_questions__mock_interview_id=mock_interview_id
        ).count()

        return mock_interview, question_count

    @staticmethod
    async def remove_questions(mock_interview_id: Union[str, UUID], question_ids: List[Union[str, UUID]]) \
            -> Tuple[Optional[MockInterview], int]:
        """Remove questions from an existing mock interview."""
        if isinstance(mock_interview_id, str):
            mock_interview_id = UUID(mock_interview_id)

        # Convert string IDs to UUID if necessary
        question_ids = [UUID(qid) if isinstance(qid, str)
                        else qid for qid in question_ids]

        # Get the mock interview
        mock_interview = await MockInterview.get_or_none(id=mock_interview_id)
        if not mock_interview:
            return None, 0

        # Remove questions through the junction table
        await MockInterviewQuestion.filter(
            mock_interview_id=mock_interview_id,
            question_id__in=question_ids
        ).delete()

        # Get the total count of questions
        question_count = await Question.filter(
            mock_interview_questions__mock_interview_id=mock_interview_id
        ).count()

        return mock_interview, question_count
