from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from tortoise.expressions import RawSQL
from models.job_insights import JobInsights
from repositories.base import Pagination, OrderBy


class JobInsightsOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "job_title", "-job_title",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class JobInsightsQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    jd_id: Optional[UUID] = None
    jd_id__in: Optional[List[UUID]] = None
    jd_id__not_in: Optional[List[UUID]] = None

    resume_id: Optional[UUID] = None
    resume_id__in: Optional[List[UUID]] = None
    resume_id__not_in: Optional[List[UUID]] = None

    job_title: Optional[str] = None
    job_title__contains: Optional[str] = None
    job_title__icontains: Optional[str] = None

    overview: Optional[str] = None
    overview__contains: Optional[str] = None
    overview__icontains: Optional[str] = None

    core_skills: Optional[List[Dict[str, Any]]] = None
    trending_skills: Optional[List[Dict[str, Any]]] = None
    soft_skills: Optional[List[Dict[str, Any]]] = None
    professional_courses: Optional[List[Dict[str, Any]]] = None
    certifications: Optional[List[Dict[str, Any]]] = None
    projects: Optional[List[Dict[str, Any]]] = None
    expected_salary: Optional[Dict[str, Any]] = None

    advises: Optional[List[str]] = None
    other_insights: Optional[List[str]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "jd_id", "resume_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "jd_id__in", "jd_id__not_in",
        "resume_id__in", "resume_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "job_title", "job_title__contains", "job_title__icontains",
        "overview", "overview__contains", "overview__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "advises", "other_insights",
        mode="before"
    )
    @classmethod
    def validate_str_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return v.split(',')
        if isinstance(v, list):
            result = []
            for item in v:
                result.extend(item.split(','))
            return result
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class JobInsightsRepository:

    @staticmethod
    async def create_one(job_insights: Dict[str, Any]) -> dict:
        """Create a single job insights record."""
        record = await JobInsights.create(**job_insights)
        return dict(record)

    @staticmethod
    async def create_many(job_insights_list: List[Dict[str, Any]]) -> List[JobInsights]:
        """Create multiple job insights records in bulk."""
        return await JobInsights.bulk_create([JobInsights(**insights) for insights in job_insights_list])

    @staticmethod
    async def get_one(q: JobInsightsQuery) -> Optional[dict]:
        """Get a single job insights record that matches the ID in the query."""
        return await JobInsights.get_or_none(id=q.id).values()

    @staticmethod
    async def get_list(q: JobInsightsQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of job insights matching the query criteria."""
        query = JobInsights.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.jd_id is not None:
            query = query.filter(jd_id=q.jd_id)
        if q.jd_id__in is not None:
            query = query.filter(jd_id__in=q.jd_id__in)
        if q.jd_id__not_in is not None:
            query = query.filter(jd_id__not_in=q.jd_id__not_in)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.resume_id__in is not None:
            query = query.filter(resume_id__in=q.resume_id__in)
        if q.resume_id__not_in is not None:
            query = query.filter(resume_id__not_in=q.resume_id__not_in)
        if q.job_title is not None:
            query = query.filter(job_title=q.job_title)
        if q.job_title__contains is not None:
            query = query.filter(job_title__contains=q.job_title__contains)
        if q.job_title__icontains is not None:
            query = query.filter(job_title__icontains=q.job_title__icontains)
        if q.overview is not None:
            query = query.filter(overview=q.overview)
        if q.overview__contains is not None:
            query = query.filter(overview__contains=q.overview__contains)
        if q.overview__icontains is not None:
            query = query.filter(overview__icontains=q.overview__icontains)
        if q.core_skills is not None:
            query = query.filter(core_skills__contains=q.core_skills)
        if q.trending_skills is not None:
            query = query.filter(trending_skills__contains=q.trending_skills)
        if q.soft_skills is not None:
            query = query.filter(soft_skills__contains=q.soft_skills)
        if q.professional_courses is not None:
            query = query.filter(professional_courses__contains=q.professional_courses)
        if q.certifications is not None:
            query = query.filter(certifications__contains=q.certifications)
        if q.projects is not None:
            query = query.filter(projects__contains=q.projects)
        if q.expected_salary is not None:
            query = query.filter(expected_salary__contains=q.expected_salary)
        if q.advises is not None:
            query = query.annotate(overlapped=RawSQL(
                "advises && ARRAY[" + ",".join([f"'{advice}'" for advice in q.advises]) + "]::TEXT[]"))
            query = query.filter(overlapped=True)
        if q.other_insights is not None:
            query = query.annotate(overlapped=RawSQL(
                "other_insights && ARRAY[" + ",".join([f"'{insight}'" for insight in q.other_insights]) + "]::TEXT[]"))
            query = query.filter(overlapped=True)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = JobInsightsOrderBy(q.order_by)
        insights = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()

        return insights, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, JobInsights]) -> Tuple[int, Optional[dict]]:
        """Update a job insights record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now(datetime.UTC)
        if isinstance(data, dict):
            affected_rows = await JobInsights.filter(id=pk).update(**data)
        else:
            affected_rows = await JobInsights.filter(id=pk).update(data)

        updated_insights = None
        if affected_rows > 0:
            updated_insights = await JobInsights.get(pk=pk).values()

        return affected_rows, updated_insights
