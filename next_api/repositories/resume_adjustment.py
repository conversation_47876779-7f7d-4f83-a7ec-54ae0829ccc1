from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from models.resume_adjustment import ResumeAdjustment
from repositories.base import Pagination, OrderBy


class ResumeAdjustmentOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "job_title", "-job_title",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class ResumeAdjustmentQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    resume_id: Optional[UUID] = None
    resume_id__in: Optional[List[UUID]] = None
    resume_id__not_in: Optional[List[UUID]] = None

    job_title: Optional[str] = None
    job_title__contains: Optional[str] = None
    job_title__icontains: Optional[str] = None

    status: Optional[ResumeAdjustment.Status] = None
    status__in: Optional[List[ResumeAdjustment.Status]] = None
    status__not_in: Optional[List[ResumeAdjustment.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "resume_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "resume_id__in", "resume_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "job_title", "job_title__contains", "job_title__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class ResumeAdjustmentRepository:

    @staticmethod
    async def create_one(adjustment: Dict[str, Any]) -> ResumeAdjustment:
        """Create a single resume adjustment record."""
        return await ResumeAdjustment.create(**adjustment)

    @staticmethod
    async def create_many(adjustments: List[Dict[str, Any]]) -> List[ResumeAdjustment]:
        """Create multiple resume adjustment records in bulk."""
        return await ResumeAdjustment.bulk_create([ResumeAdjustment(**adj) for adj in adjustments])

    @staticmethod
    async def get_one(q: ResumeAdjustmentQuery) -> Optional[Dict[str, Any]]:
        """Get a single resume adjustment that matches the ID in the query."""
        adjustment = await ResumeAdjustment \
            .get_or_none(id=q.id) \
            .values()
        return adjustment

    @staticmethod
    async def get_list(q: ResumeAdjustmentQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of resume adjustments matching the query criteria."""
        query = ResumeAdjustment.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.resume_id__in is not None:
            query = query.filter(resume_id__in=q.resume_id__in)
        if q.resume_id__not_in is not None:
            query = query.filter(resume_id__not_in=q.resume_id__not_in)
        if q.job_title is not None:
            query = query.filter(job_title=q.job_title)
        if q.job_title__contains is not None:
            query = query.filter(job_title__contains=q.job_title__contains)
        if q.job_title__icontains is not None:
            query = query.filter(job_title__icontains=q.job_title__icontains)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = ResumeAdjustmentOrderBy(q.order_by)
        adjustments = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).values()

        return adjustments, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: dict) -> Tuple[int, Optional[Dict[str, Any]]]:
        """Update a resume adjustment record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        affected_rows = await ResumeAdjustment.filter(id=pk).update(**data)
        updated_adjustment = None
        if affected_rows > 0:
            updated_adjustment = await ResumeAdjustment.get(pk=pk).values()

        return affected_rows, updated_adjustment
