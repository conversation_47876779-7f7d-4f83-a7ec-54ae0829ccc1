from typing import Optional, <PERSON><PERSON>
from datetime import datetime, timezone, timed<PERSON>ta
from sanic.exceptions import BadRequest
from sanic.log import error_logger
from api.base import ResponseCode
from models.token import AccessToken
from utils.security.token import JWT
from utils.cache import CACHE_MANAGER
from clients.google_auth import GooglePeople, get_people_info


class AccessTokenRepository:

    ACCESS_TOKEN_KEY = 'access_token:{jti}'
    GOOGLE_ACCESS_TOKEN_KEY = 'gtk:{jti}'

    def __init__(self, issuer: Optional[str] = None, algorithm: Optional[str] = None):
        self.encoder = JWT(issuer, algorithm)

    def generate_user_access_token(self, user: dict, exp_at: Optional[int] = None) -> Tuple[dict, str]:
        payload = {
            "user_id": user["id"],
            "email": user["email"],
            "iat": datetime.now(tz=timezone.utc),
            "exp": exp_at,
        }
        return self.encoder.encode(payload)

    def verify_user_access_token(self, token: str) -> bool:
        decoded_token = self.encoder.decode(token)
        return decoded_token != {}

    def generate_user_refresh_token(self, data: dict, exp_at: Optional[int] = None) -> Tuple[dict, str]:
        payload = {
            "user_id": data["id"],
            "iat": datetime.now(tz=timezone.utc),
            "exp": exp_at,
        }
        return self.encoder.encode(payload)

    async def generate_user_tokens(self, user: dict, options: dict = None) -> Tuple[str, str]:
        if not isinstance(options, dict):
            options = {}
        user["id"] = str(user["id"])
        exp_at = options.get("at_exp", None)
        at_payload, at = self.generate_user_access_token(user, exp_at)

        exp_rt = options.get("rt_exp", None)
        rt_payload, rt = self.generate_user_refresh_token(user, exp_rt)
        await AccessToken.create(
            id=at_payload["jti"],
            user_id=user["id"],
            claims=at_payload,
            issued_at=at_payload["iat"],
            expired_at=exp_at,
            rt_id=rt_payload["jti"],
            rt_expired_at=exp_rt,
        )
        return at, rt

    async def refresh_token(self, user: dict, claims: str, options: dict = None) -> Tuple[str, str]:
        at_model = await AccessToken.get_or_none(rt_id=claims["jti"]).values()
        if not at_model:
            raise BadRequest(message="Invalid token", context={
                "error": ResponseCode.GET_ONE_FAILED,
            })
        if at_model["user_id"] != user["id"]:
            raise BadRequest(message="Invalid token", context={
                "error": ResponseCode.UNAUTHORIZED,
            })
        self.revoke_token(at_model)
        await AccessToken.filter(id=claims["jti"]).delete()
        return await self.generate_user_tokens(user, options)

    async def get_one(self, jti: str) -> Optional[dict]:
        return await AccessToken.get_or_none(id=jti).values()

    def revoke_token(self, data: dict) -> bool:
        key = self.ACCESS_TOKEN_KEY.format(jti=data["id"])
        rd = CACHE_MANAGER.get_connection('default')
        if rd is None:
            error_logger.error("Error connecting to Redis")
            return False
        rd.hmset(key, data["claims"])
        ttl = int((data["expired_at"] + timedelta(seconds=10)).timestamp())
        rd.expire(key, ttl)
        return True

    def get_token_from_cache(self, jti: str) -> dict:
        key = self.ACCESS_TOKEN_KEY.format(jti=jti)
        rd = CACHE_MANAGER.get_connection('default')
        if rd is None:
            error_logger.error("Error connecting to Redis")
            return {}
        return rd.hgetall(key)

    @classmethod
    def get_info_from_google(cls, gtk: str) -> Optional[GooglePeople]:
        key = cls.GOOGLE_ACCESS_TOKEN_KEY.format(jti=gtk)
        rd = CACHE_MANAGER.get_connection('default')
        if rd is None:
            error_logger.error("Error connecting to Redis")
            return None
        if rd.exists(key):
            cr = {k.decode('utf-8'): v.decode('utf-8') for k, v in rd.hgetall(key).items()}
            return GooglePeople(**cr)
        gp = get_people_info(gtk)
        if gp:
            rd.hmset(key, gp.model_dump())
        return gp
