import json
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from repositories.base import Pagination, OrderBy
from models.user_feature import UserFeature
from models.feature import Feature
from models.user_subscription import UserSubscription
from models.subscription_plan_feature import SubscriptionPlanFeature
from utils.cache import CACHE_MANAGER
from tortoise.transactions import in_transaction


class UserFeatureOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "user_id", "-user_id",
        "feature_id", "-feature_id",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class UserFeatureQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    feature_id: Optional[UUID] = None
    feature_id__in: Optional[List[UUID]] = None
    feature_id__not_in: Optional[List[UUID]] = None

    feature_value__isnull: Optional[bool] = None
    feature_value__not_isnull: Optional[bool] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", "user_id", "feature_id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        "feature_id__in", "feature_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> List[UUID]:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(",")]
        return v

    @field_validator(
        "feature_value__isnull", "feature_value__not_isnull",
        mode="before"
    )
    @classmethod
    def validate_boolean(cls, v: Any) -> Any:
        if isinstance(v, list):
            v = v[0]
        if isinstance(v, str):
            return v.lower() in ("true", "1")
        if isinstance(v, int):
            return v == 1
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v


class UserFeatureRepository:

    USER_FEATURE_KEY = 'user_feature:{{{user_id}}}'

    @staticmethod
    async def create_one(user_feature: Dict[str, Any]) -> dict:
        """Create a single user feature record.

        Args:
            user_feature (Dict[str, Any]): Dictionary containing user feature fields and values.

        Returns:
            dict: The created UserFeature instance as a dictionary.
        """
        record = await UserFeature.create(**user_feature)
        return dict(record)

    @staticmethod
    async def create_many(user_features: List[Dict[str, Any]]) -> List[UserFeature]:
        """Create multiple user feature records in bulk.

        Args:
            user_features (List[Dict[str, Any]]): List of dictionaries containing user feature data.

        Returns:
            List[UserFeature]: List of created UserFeature instances.
        """
        feature_models = [UserFeature(**feature) for feature in user_features]
        return await UserFeature.bulk_create(feature_models)

    @staticmethod
    async def get_one(pk: Union[str, UUID]) -> Optional[dict]:
        """Get a single user feature by ID.

        Args:
            pk (Union[str, UUID]): ID of user feature to retrieve.

        Returns:
            Optional[dict]: Matching UserFeature instance if found, None otherwise.
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        return await UserFeature.get_or_none(id=pk).values()

    @staticmethod
    async def get_list(q: UserFeatureQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of user features matching the query criteria.

        Args:
            q (UserFeatureQuery): Query object containing filter criteria.
            p (Pagination): Pagination parameters for offset and limit.

        Returns:
            Tuple[List[dict], int]: Tuple containing:
                - List of UserFeature instances as dictionaries
                - Total count of matching records before pagination
        """
        query = UserFeature.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)

        if q.feature_id is not None:
            query = query.filter(feature_id=q.feature_id)
        if q.feature_id__in is not None:
            query = query.filter(feature_id__in=q.feature_id__in)
        if q.feature_id__not_in is not None:
            query = query.filter(feature_id__not_in=q.feature_id__not_in)

        if q.feature_value__isnull is not None:
            query = query.filter(feature_value__isnull=q.feature_value__isnull)
        if q.feature_value__not_isnull is not None:
            query = query.filter(feature_value__not_isnull=q.feature_value__not_isnull)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = UserFeatureOrderBy(q.order_by)
        features = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).values()

        return features, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Dict[str, Any]) -> Tuple[int, Optional[dict]]:
        """Update a user feature record.

        Args:
            pk (Union[str, UUID]): Primary key of the user feature to update
            data (Dict[str, Any]): Dictionary of user feature data to update

        Returns:
            Tuple[int, Optional[dict]]: Tuple containing:
                - Number of rows affected (0 or 1)
                - Updated UserFeature instance if successful, None otherwise
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        if "updated_at" not in data:
            data["updated_at"] = datetime.now()

        affected_rows = await UserFeature.filter(id=pk).update(**data)
        updated_feature = None
        if affected_rows > 0:
            updated_feature = await UserFeature.get(id=pk).values()

        return affected_rows, updated_feature

    @staticmethod
    async def update_by_query(update_data: dict, q: UserFeatureQuery) -> int:
        """Update multiple user feature records that match the query criteria.

        Args:
            update_data (dict): Dictionary containing the fields and values to update
            q (UserFeatureQuery): Query object containing filter criteria

        Returns:
            int: Number of records affected by the update operation
        """
        query = UserFeature.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)

        if q.feature_id is not None:
            query = query.filter(feature_id=q.feature_id)
        if q.feature_id__in is not None:
            query = query.filter(feature_id__in=q.feature_id__in)
        if q.feature_id__not_in is not None:
            query = query.filter(feature_id__not_in=q.feature_id__not_in)

        if q.feature_value__isnull is not None:
            query = query.filter(feature_value__isnull=q.feature_value__isnull)
        if q.feature_value__not_isnull is not None:
            query = query.filter(feature_value__not_isnull=q.feature_value__not_isnull)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        if "updated_at" not in update_data:
            update_data["updated_at"] = datetime.now()

        return await query.update(**update_data)

    @staticmethod
    async def get_all_features(user: dict) -> Dict[str, dict]:
        """Get all features available to a user based on their active subscriptions and UserFeature records.
        First tries to get from cache, falls back to database if cache miss.

        Args:
            user (dict): The user to get features for

        Returns:
            Dict[str, dict]: Dictionary of feature codes and their values
        """
        # Get Redis connection
        redis = CACHE_MANAGER.get_connection('default')
        cache_key = UserFeatureRepository.USER_FEATURE_KEY.format(user_id=str(user['id']))

        # Try to get from cache first
        if redis:
            cached_features = redis.hgetall(cache_key)
            if cached_features:
                # Convert cached data back to dictionary
                return {
                    k.decode(): json.loads(v.decode())
                    for k, v in cached_features.items()
                }

        # If cache miss or no Redis connection, get from database
        # Get all active subscriptions for the user
        active_subscriptions: List[UserSubscription] = await UserSubscription.filter(
            user_id=user['id'],
            status=UserSubscription.Status.ACTIVE
        ).all()

        # Get all features in those subscriptions
        subscription_ids = [active_sub.subscription_plan_id for active_sub in active_subscriptions]
        subscription_features = await SubscriptionPlanFeature.filter(
            subscription_plan_id__in=subscription_ids
        ).prefetch_related('feature').all()

        # Get user's features
        user_features = await UserFeature.filter(
            user_id=user['id'],
            feature_id__in=[sub_feat.feature_id for sub_feat in subscription_features]
        ).prefetch_related('feature').all()

        features = {}

        # Build features dictionary from user's features and subscription features
        for user_feature in user_features:
            features[user_feature.feature.code] = user_feature.feature_value

        # Cache the results if Redis is available
        if redis:
            # Convert dictionary values to JSON strings for Redis storage
            cache_data = {
                k: json.dumps(v)
                for k, v in features.items()
            }
            # Store as hash with 1 hour expiry
            if cache_data:  # Only set cache if we have features
                redis.hmset(cache_key, cache_data)
                redis.expire(cache_key, 3600)  # 1 hour expiry

        return features

    @staticmethod
    async def use_feature(user_id: UUID, feature_code: str, amount: int = 1) -> Tuple[int, dict]:
        """Use a feature and update its balance.

        Args:
            user_id (UUID): The ID of the user
            feature_code (str): The feature code to use
            amount (int, optional): Amount to deduct from balance. Defaults to 1.

        Returns:
            Tuple[int, dict]: 
                - Status code (0: success, 1: insufficient balance/feature not found, 2: balance field not defined)
                - Feature values dictionary
        """
        balance_field_map = {
            Feature.Code.RESUME_BUILDER: 'max_resumes',
            Feature.Code.RESUME_AI_BUILDER: 'max_ai_generations',
            Feature.Code.COVER_LETTER_BUILDER: 'max_cover_letters',
            Feature.Code.COVER_LETTER_AI_BUILDER: 'max_ai_generations',
            Feature.Code.MOCK_INTERVIEW_AI: 'no_questions',
            Feature.Code.INTERVIEW_AI_SUGGESTION: 'max_suggestions'
        }

        balance_field = balance_field_map.get(feature_code)
        if not balance_field:
            return 1, {}

        async with in_transaction():
            user_feature = await UserFeature.filter(
                user_id=user_id,
                feature__code=feature_code
            ).prefetch_related('feature').select_for_update().first()

            if not user_feature:
                return 1, {}

            if balance_field not in user_feature.feature_value:
                return 2, {}

            current_balance = user_feature.feature_value.get(balance_field, 0)

            if current_balance < amount:
                return 1, dict(user_feature)

            try:
                user_feature.feature_value[balance_field] = current_balance - amount
                user_feature.updated_at = datetime.now()
                await user_feature.save(update_fields=['feature_value', 'updated_at'])

                redis = CACHE_MANAGER.get_connection('default')
                if redis:
                    cache_key = UserFeatureRepository.USER_FEATURE_KEY.format(user_id=str(user_id))
                    redis.delete(cache_key)

                return 0, dict(user_feature)

            except Exception:
                return 1, {}

    @staticmethod
    def clear_cache(user_id: UUID):
        redis = CACHE_MANAGER.get_connection('default')
        if redis:
            cache_key = UserFeatureRepository.USER_FEATURE_KEY.format(user_id=str(user_id))
            redis.delete(cache_key)
