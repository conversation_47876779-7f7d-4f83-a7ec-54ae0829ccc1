from typing import Optional
import litellm
from utils.cache import CACHE_MANAGER


class AIThreadRepository:

    OPENAI_THREAD_ID_KEY = 'openai:thread:{session_id}'

    @classmethod
    def get_thread_id(cls, session_id: str) -> Optional[str]:
        """
        Retrieves the OpenAI thread ID associated with a session ID from cache.

        Args:
            session_id (str): The session ID to look up

        Returns:
            Optional[str]: The thread ID if found in cache, None otherwise
        """
        key = cls.OPENAI_THREAD_ID_KEY.format(session_id=session_id)
        rd = CACHE_MANAGER.get_connection('default')
        value = rd.get(key)
        if value is None:
            return None
        return value.decode('utf-8')

    @classmethod
    def set_thread_id(cls, session_id: str, thread_id: str):
        key = cls.OPENAI_THREAD_ID_KEY.format(session_id=session_id)
        rd = CACHE_MANAGER.get_connection('default')
        return rd.set(key, thread_id)

    @classmethod
    def create_thread_id(cls) -> str:
        thread: litellm.Thread = litellm.create_thread(
            custom_llm_provider="openai",
            messages=[{"role": "user", "content": "Hey, how's it going?"}],
        )
        return thread.id
