from typing import Dict, Any, List, Optional
from datetime import datetime
from uuid import UUID
from tortoise.transactions import atomic
from models.polar_order import (
    PolarOrder, PolarOrderItem, PolarCustomer,
    PolarProduct, PolarDiscount, PolarSubscription
)


class PolarOrderRepository:

    @staticmethod
    @atomic()
    async def create_order_from_webhook(webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a complete order record from Polar webhook data.

        Args:
            webhook_data: The webhook payload data containing order information

        Returns:
            Dict containing the created order record
        """
        order_data = webhook_data.get('data', {})

        # Create or update customer
        customer_data = order_data.get('customer', {})
        if customer_data:
            customer = await PolarOrderRepository._create_or_update_customer(customer_data)

        # Create or update product
        product_data = order_data.get('product', {})
        if product_data:
            product = await PolarOrderRepository._create_or_update_product(product_data)

        # Create or update discount
        discount_data = order_data.get('discount', {})
        if discount_data:
            discount = await PolarOrderRepository._create_or_update_discount(discount_data)

        # Create or update subscription
        subscription_data = order_data.get('subscription', {})
        if subscription_data:
            subscription = await PolarOrderRepository._create_or_update_subscription(subscription_data)

        # Create order
        order = await PolarOrderRepository._create_order(order_data)

        # Create order items
        items_data = order_data.get('items', [])
        if items_data:
            await PolarOrderRepository._create_order_items(order.id, items_data)

        return dict(order)

    @staticmethod
    async def _create_or_update_customer(customer_data: Dict[str, Any]) -> PolarCustomer:
        """Create or update a polar customer record."""
        polar_customer_id = customer_data.get('id')

        customer_fields = {
            'polar_customer_id': polar_customer_id,
            'external_id': customer_data.get('external_id'),
            'email': customer_data.get('email'),
            'email_verified': customer_data.get('email_verified', False),
            'name': customer_data.get('name'),
            'billing_address': customer_data.get('billing_address'),
            'tax_id': customer_data.get('tax_id', []),
            'organization_id': customer_data.get('organization_id'),
            'deleted_at': datetime.fromisoformat(customer_data['deleted_at'].replace('Z', '+00:00')) if customer_data.get('deleted_at') else None,
            'avatar_url': customer_data.get('avatar_url'),
            'metadata': customer_data.get('metadata', {}),
            'created_at': datetime.fromisoformat(customer_data['created_at'].replace('Z', '+00:00')) if customer_data.get('created_at') else datetime.now(),
            'modified_at': datetime.fromisoformat(customer_data['modified_at'].replace('Z', '+00:00')) if customer_data.get('modified_at') else datetime.now(),
        }

        customer, created = await PolarCustomer.get_or_create(
            polar_customer_id=polar_customer_id,
            defaults=customer_fields
        )

        if not created:
            # Update existing customer
            for field, value in customer_fields.items():
                if field != 'polar_customer_id':
                    setattr(customer, field, value)
            await customer.save()

        return customer

    @staticmethod
    async def _create_or_update_product(product_data: Dict[str, Any]) -> PolarProduct:
        """Create or update a polar product record."""
        polar_product_id = product_data.get('id')

        product_fields = {
            'polar_product_id': polar_product_id,
            'name': product_data.get('name'),
            'description': product_data.get('description'),
            'recurring_interval': product_data.get('recurring_interval'),
            'is_recurring': product_data.get('is_recurring', False),
            'is_archived': product_data.get('is_archived', False),
            'organization_id': product_data.get('organization_id'),
            'metadata': product_data.get('metadata', {}),
            'created_at': datetime.fromisoformat(product_data['created_at'].replace('Z', '+00:00')) if product_data.get('created_at') else datetime.now(),
            'modified_at': datetime.fromisoformat(product_data['modified_at'].replace('Z', '+00:00')) if product_data.get('modified_at') else datetime.now(),
        }

        product, created = await PolarProduct.get_or_create(
            polar_product_id=polar_product_id,
            defaults=product_fields
        )

        if not created:
            # Update existing product
            for field, value in product_fields.items():
                if field != 'polar_product_id':
                    setattr(product, field, value)
            await product.save()

        return product

    @staticmethod
    async def _create_or_update_discount(discount_data: Dict[str, Any]) -> PolarDiscount:
        """Create or update a polar discount record."""
        polar_discount_id = discount_data.get('id')

        discount_fields = {
            'polar_discount_id': polar_discount_id,
            'name': discount_data.get('name'),
            'code': discount_data.get('code'),
            'duration': discount_data.get('duration'),
            'type': discount_data.get('type'),
            'amount': discount_data.get('amount'),
            'currency': discount_data.get('currency'),
            'starts_at': datetime.fromisoformat(discount_data['starts_at'].replace('Z', '+00:00')) if discount_data.get('starts_at') else None,
            'ends_at': datetime.fromisoformat(discount_data['ends_at'].replace('Z', '+00:00')) if discount_data.get('ends_at') else None,
            'max_redemptions': discount_data.get('max_redemptions'),
            'redemptions_count': discount_data.get('redemptions_count', 0),
            'organization_id': discount_data.get('organization_id'),
            'metadata': discount_data.get('metadata', {}),
            'created_at': datetime.fromisoformat(discount_data['created_at'].replace('Z', '+00:00')) if discount_data.get('created_at') else datetime.now(),
            'modified_at': datetime.fromisoformat(discount_data['modified_at'].replace('Z', '+00:00')) if discount_data.get('modified_at') else datetime.now(),
        }

        discount, created = await PolarDiscount.get_or_create(
            polar_discount_id=polar_discount_id,
            defaults=discount_fields
        )

        if not created:
            # Update existing discount
            for field, value in discount_fields.items():
                if field != 'polar_discount_id':
                    setattr(discount, field, value)
            await discount.save()

        return discount

    @staticmethod
    async def _create_or_update_subscription(subscription_data: Dict[str, Any]) -> PolarSubscription:
        """Create or update a polar subscription record."""
        polar_subscription_id = subscription_data.get('id')

        subscription_fields = {
            'polar_subscription_id': polar_subscription_id,
            'amount': subscription_data.get('amount'),
            'currency': subscription_data.get('currency'),
            'recurring_interval': subscription_data.get('recurring_interval'),
            'status': subscription_data.get('status'),
            'current_period_start': datetime.fromisoformat(subscription_data['current_period_start'].replace('Z', '+00:00')) if subscription_data.get('current_period_start') else None,
            'current_period_end': datetime.fromisoformat(subscription_data['current_period_end'].replace('Z', '+00:00')) if subscription_data.get('current_period_end') else None,
            'cancel_at_period_end': subscription_data.get('cancel_at_period_end', False),
            'canceled_at': datetime.fromisoformat(subscription_data['canceled_at'].replace('Z', '+00:00')) if subscription_data.get('canceled_at') else None,
            'started_at': datetime.fromisoformat(subscription_data['started_at'].replace('Z', '+00:00')) if subscription_data.get('started_at') else None,
            'ends_at': datetime.fromisoformat(subscription_data['ends_at'].replace('Z', '+00:00')) if subscription_data.get('ends_at') else None,
            'ended_at': datetime.fromisoformat(subscription_data['ended_at'].replace('Z', '+00:00')) if subscription_data.get('ended_at') else None,
            'customer_id': subscription_data.get('customer_id'),
            'product_id': subscription_data.get('product_id'),
            'discount_id': subscription_data.get('discount_id'),
            'checkout_id': subscription_data.get('checkout_id'),
            'customer_cancellation_reason': subscription_data.get('customer_cancellation_reason'),
            'customer_cancellation_comment': subscription_data.get('customer_cancellation_comment'),
            'metadata': subscription_data.get('metadata', {}),
            'created_at': datetime.fromisoformat(subscription_data['created_at'].replace('Z', '+00:00')) if subscription_data.get('created_at') else datetime.now(),
            'modified_at': datetime.fromisoformat(subscription_data['modified_at'].replace('Z', '+00:00')) if subscription_data.get('modified_at') else datetime.now(),
        }

        subscription, created = await PolarSubscription.get_or_create(
            polar_subscription_id=polar_subscription_id,
            defaults=subscription_fields
        )

        if not created:
            # Update existing subscription
            for field, value in subscription_fields.items():
                if field != 'polar_subscription_id':
                    setattr(subscription, field, value)
            await subscription.save()

        return subscription

    @staticmethod
    async def _create_order(order_data: Dict[str, Any]) -> PolarOrder:
        """Create a polar order record."""
        order_fields = {
            'polar_order_id': order_data.get('id'),
            'status': order_data.get('status'),
            'paid': order_data.get('paid', False),
            'subtotal_amount': order_data.get('subtotal_amount'),
            'discount_amount': order_data.get('discount_amount'),
            'net_amount': order_data.get('net_amount'),
            'amount': order_data.get('amount'),
            'tax_amount': order_data.get('tax_amount'),
            'total_amount': order_data.get('total_amount'),
            'refunded_amount': order_data.get('refunded_amount'),
            'refunded_tax_amount': order_data.get('refunded_tax_amount'),
            'currency': order_data.get('currency'),
            'billing_reason': order_data.get('billing_reason'),
            'billing_address': order_data.get('billing_address'),
            'customer_id': order_data.get('customer_id'),
            'product_id': order_data.get('product_id'),
            'discount_id': order_data.get('discount_id'),
            'subscription_id': order_data.get('subscription_id'),
            'checkout_id': order_data.get('checkout_id'),
            'user_id': order_data.get('user_id'),
            'metadata': order_data.get('metadata', {}),
            'custom_field_data': order_data.get('custom_field_data', {}),
            'created_at': datetime.fromisoformat(order_data['created_at'].replace('Z', '+00:00')) if order_data.get('created_at') else datetime.now(),
            'modified_at': datetime.fromisoformat(order_data['modified_at'].replace('Z', '+00:00')) if order_data.get('modified_at') else datetime.now(),
        }

        order = await PolarOrder.create(**order_fields)
        return order

    @staticmethod
    async def _create_order_items(order_id: UUID, items_data: List[Dict[str, Any]]) -> List[PolarOrderItem]:
        """Create polar order item records."""
        items = []
        for item_data in items_data:
            item_fields = {
                'polar_order_id': order_id,
                'label': item_data.get('label'),
                'amount': item_data.get('amount'),
                'tax_amount': item_data.get('tax_amount'),
                'proration': item_data.get('proration', False),
                'product_price_id': item_data.get('product_price_id'),
                'created_at': datetime.fromisoformat(item_data['created_at'].replace('Z', '+00:00')) if item_data.get('created_at') else datetime.now(),
                'modified_at': datetime.fromisoformat(item_data['modified_at'].replace('Z', '+00:00')) if item_data.get('modified_at') else datetime.now(),
            }
            item = await PolarOrderItem.create(**item_fields)
            items.append(item)
        return items

    @staticmethod
    async def get_order_by_polar_id(polar_order_id: str) -> Optional[Dict[str, Any]]:
        """Get order by Polar order ID."""
        order = await PolarOrder.get_or_none(polar_order_id=polar_order_id).prefetch_related('items')
        return dict(order) if order else None

    @staticmethod
    async def get_orders_by_customer_id(customer_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Get orders by customer ID."""
        orders = await PolarOrder.filter(customer_id=customer_id).prefetch_related('items').limit(limit).offset(offset)
        return [dict(order) for order in orders]

    @staticmethod
    async def get_orders_by_user_id(user_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Get orders by user ID."""
        orders = await PolarOrder.filter(user_id=user_id).prefetch_related('items').limit(limit).offset(offset)
        return [dict(order) for order in orders]
