import secrets
from datetime import datetime, timedelta, UTC
from typing import Dict, Any, Optional
from uuid import UUID

from models.verification import Verification


class VerificationRepository:
    @staticmethod
    async def create_one(
        user_id: UUID,
        verification_type: Verification.Type,
        expired_at: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Create a verification record.

        Args:
            user_id: The ID of the user
            verification_type: Type of verification (activation/forgot_password)
            expired_at: Optional expiration time, defaults to 24 hours from now

        Returns:
            Dict containing the created verification record
        """
        if expired_at is None:
            expired_at = datetime.now(UTC) + timedelta(hours=24)

        verification = await Verification.create(
            user_id=user_id,
            secret=secrets.token_urlsafe(32),
            verification_type=verification_type,
            expired_at=expired_at
        )
        return await verification.values()

    @staticmethod
    async def get_one(user_id: UUID, secret: str, verification_type: Verification.Type) -> Optional[Dict[str, Any]]:
        """Get a verification record by user_id, secret and type.

        Args:
            user_id: The ID of the user
            secret: The verification secret
            verification_type: Type of verification

        Returns:
            Optional[Dict] containing the verification record if found
        """
        return await Verification.get_or_none(
            user_id=user_id,
            secret=secret,
            verification_type=verification_type
        ).values()

    @staticmethod
    async def update(pk: UUID, data: Dict[str, Any]) -> int:
        """Update a verification record.
        
        Args:
            pk: Primary key of the verification record
            data: Dictionary containing fields to update
            
        Returns:
            Number of records updated
        """
        return await Verification.filter(id=pk).update(**data)
