import json
from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from repositories.base import Pagination, OrderBy
from models.user_saved_job import UserSavedJob
from utils.cache import CACHE_MANAGER


class UserSavedJobOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "user_id", "-user_id",
        "job_id", "-job_id",
        "created_at", "-created_at",
    )
    _default_order_by = ("created_at",)


class UserSavedJobQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None

    job_id: Optional[UUID] = None
    job_id__in: Optional[List[UUID]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", "user_id", "job_id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        return v

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "job_id__in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> List[UUID]:
        if isinstance(v, list):
            return [UUID(x) if isinstance(x, str) else x for x in v]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v


class UserSavedJobRepository:

    USER_SAVED_JOBS_KEY = 'user_saved_jobs:{{{user_id}}}'
    USER_SAVED_JOB_KEY = 'user_saved_job:{{{id}}}'

    @staticmethod
    async def create_one(saved_job: Dict[str, Any]) -> dict:
        """
        Create a new saved job
        """
        saved_job_obj, _ = await UserSavedJob.get_or_create(**saved_job)
        UserSavedJobRepository.clear_cache(saved_job_obj.user_id)
        return dict(saved_job_obj)

    @staticmethod
    async def create_many(saved_jobs: List[Dict[str, Any]]) -> List[UserSavedJob]:
        """
        Create multiple saved jobs
        """
        return await UserSavedJob.bulk_create([UserSavedJob(**saved_job) for saved_job in saved_jobs])

    @staticmethod
    async def get_one(pk: Union[str, UUID]) -> Optional[dict]:
        """
        Get a single saved job by ID
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        cache_key = UserSavedJobRepository.USER_SAVED_JOB_KEY.format(id=pk)
        redis = CACHE_MANAGER.get_connection('default')
        cached = await redis.get(cache_key)
        if cached:
            return json.loads(cached)

        saved_job = await UserSavedJob.get_or_none(id=pk)
        if saved_job:
            saved_job_dict = {
                "id": str(saved_job.id),
                "user_id": str(saved_job.user_id),
                "job_id": str(saved_job.job_id),
                "created_at": saved_job.created_at.isoformat() if saved_job.created_at else None,
            }
            await redis.set(cache_key, json.dumps(saved_job_dict))
            return saved_job_dict
        return None

    @staticmethod
    async def get_list(q: UserSavedJobQuery, p: Pagination) -> Tuple[List[dict], int]:
        """
        Get a list of saved jobs with pagination
        """
        query = UserSavedJob.all()

        if q.id:
            query = query.filter(id=q.id)
        if q.id__in:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in:
            query = query.filter(user_id__in=q.user_id__in)

        if q.job_id:
            query = query.filter(job_id=q.job_id)
        if q.job_id__in:
            query = query.filter(job_id__in=q.job_id__in)

        if q.created_at:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte:
            query = query.filter(created_at__lte=q.created_at__lte)

        order_by = UserSavedJobOrderBy(q.order_by)
        if order_by.fields:
            query = query.order_by(*order_by.fields)

        total = await query.count()
        saved_jobs = await query.offset(p.offset).limit(p.limit)

        result = []
        for saved_job in saved_jobs:
            saved_job_dict = {
                "id": str(saved_job.id),
                "user_id": str(saved_job.user_id),
                "job_id": str(saved_job.job_id),
                "created_at": saved_job.created_at.isoformat() if saved_job.created_at else None,
            }
            result.append(saved_job_dict)

        return result, total

    @staticmethod
    async def get_user_saved_jobs(user_id: str, pagination: Optional[Pagination] = None) -> Tuple[List[dict], int]:
        """
        Get all saved jobs for a user with optional pagination

        Args:
            user_id: The user ID
            pagination: Optional pagination parameters

        Returns:
            Tuple containing the list of saved jobs and the total count
        """
        # Convert string to UUID for database query
        user_id_uuid = UUID(user_id) if isinstance(user_id, str) else user_id

        query = UserSavedJob.filter(user_id=user_id_uuid)

        # Apply pagination if provided
        total = await query.count()

        if pagination:
            query = query.offset(pagination.offset).limit(pagination.limit)

        saved_jobs = await query

        result = []
        for saved_job in saved_jobs:
            saved_job_dict = {
                "id": str(saved_job.id),
                "user_id": str(saved_job.user_id),
                "job_id": str(saved_job.job_id),
                "created_at": saved_job.created_at.isoformat() if saved_job.created_at else None,
            }
            result.append(saved_job_dict)

        return result, total

    @staticmethod
    async def delete(user_id: str, job_id: str) -> Tuple[int, Optional[dict]]:
        """
        Delete a saved job
        """
        if isinstance(user_id, str):
            user_id = UUID(user_id)
        if isinstance(job_id, str):
            job_id = UUID(job_id)

        saved_job = await UserSavedJob.get_or_none(user_id=user_id, job_id=job_id)
        if not saved_job:
            return 0, None

        await saved_job.delete()
        UserSavedJobRepository.clear_cache(user_id)
        return 1, None

    @staticmethod
    async def delete_by_query(q: UserSavedJobQuery) -> int:
        """
        Delete multiple saved jobs based on query
        """
        query = UserSavedJob.all()

        if q.id:
            query = query.filter(id=q.id)
        if q.id__in:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in:
            query = query.filter(user_id__in=q.user_id__in)

        if q.job_id:
            query = query.filter(job_id=q.job_id)
        if q.job_id__in:
            query = query.filter(job_id__in=q.job_id__in)

        if q.created_at:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte:
            query = query.filter(created_at__lte=q.created_at__lte)

        # Get user IDs before deleting to clear their caches
        user_ids = set()
        async for saved_job in query:
            user_ids.add(saved_job.user_id)

        deleted_count = await query.delete()

        # Clear cache for all affected users
        for user_id in user_ids:
            UserSavedJobRepository.clear_cache(user_id)

        return deleted_count

    @staticmethod
    def clear_cache(user_id: UUID):
        """
        Clear cache for a user's saved jobs
        """
        cache_key = UserSavedJobRepository.USER_SAVED_JOBS_KEY.format(user_id=user_id)
        redis = CACHE_MANAGER.get_connection('default')
        redis.delete(cache_key)
