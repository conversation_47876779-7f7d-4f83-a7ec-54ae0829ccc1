from datetime import datetime
from typing import Dict, Any, <PERSON><PERSON>, Optional, List, Union
from uuid import UUID
from pydantic import BaseModel, ConfigDict, field_validator
from models.feature import Feature
from repositories.base import Pagination, OrderBy


class FeatureOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "code", "-code",
        "name", "-name",
        "visibility", "-visibility",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class FeatureQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    code: Optional[str] = None
    code__contains: Optional[str] = None
    code__icontains: Optional[str] = None
    code__startswith: Optional[str] = None
    code__endswith: Optional[str] = None

    name: Optional[str] = None
    name__contains: Optional[str] = None
    name__icontains: Optional[str] = None
    name__startswith: Optional[str] = None
    name__endswith: Optional[str] = None

    visibility: Optional[Feature.Visibility] = None
    visibility__in: Optional[List[Feature.Visibility]] = None
    visibility__not_in: Optional[List[Feature.Visibility]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator("id__in", "id__not_in", mode="before")
    @classmethod
    def validate_id_in(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "name", "name__contains", "name__icontains", "name__startswith", "name__endswith",
        "code", "code__contains", "code__icontains", "code__startswith", "code__endswith",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "visibility",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> Any:
        if isinstance(v, list):
            return int(v[0])
        return int(v)

    @field_validator(
        "visibility__in", "visibility__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v


class FeatureRepository:
    @staticmethod
    async def create_one(data: Dict) -> Feature:
        return await Feature.create(**data)

    @staticmethod
    async def create_many(data_list: List[Dict]) -> List[Feature]:
        return await Feature.bulk_create([Feature(**data) for data in data_list])

    @staticmethod
    async def get_one(pk: Union[str, UUID], prefetch_plans: bool = False) -> dict:
        if isinstance(pk, str):
            pk = UUID(pk)
        feature = await Feature.get_or_none(id=pk).values()
        if feature is None:
            return {}

        if prefetch_plans:
            feature['plans'] = await Feature.get(id=pk).mapped_plans.all().values('id', 'name')
        return feature

    @staticmethod
    async def get_by_code(code: str, prefetch_plans: bool = False) -> Optional[dict]:
        """Get a single feature by code."""
        feature = await Feature.get_or_none(code=code).values()
        if feature is None:
            return None

        if prefetch_plans:
            feature['plans'] = await Feature.get(code=code).mapped_plans.all().values('id', 'name')
        return feature

    @staticmethod
    async def get_list(q: FeatureQuery, p: Pagination) -> Tuple[List[dict], int]:
        query = Feature.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.code is not None:
            query = query.filter(code=q.code)
        if q.code__contains is not None:
            query = query.filter(code__contains=q.code__contains)
        if q.code__icontains is not None:
            query = query.filter(code__icontains=q.code__icontains)
        if q.code__startswith is not None:
            query = query.filter(code__startswith=q.code__startswith)
        if q.code__endswith is not None:
            query = query.filter(code__endswith=q.code__endswith)

        if q.name is not None:
            query = query.filter(name=q.name)
        if q.name__contains is not None:
            query = query.filter(name__contains=q.name__contains)
        if q.name__icontains is not None:
            query = query.filter(name__icontains=q.name__icontains)
        if q.name__startswith is not None:
            query = query.filter(name__startswith=q.name__startswith)
        if q.name__endswith is not None:
            query = query.filter(name__endswith=q.name__endswith)

        if q.visibility is not None:
            query = query.filter(visibility=q.visibility)
        if q.visibility__in is not None:
            query = query.filter(visibility__in=q.visibility__in)
        if q.visibility__not_in is not None:
            query = query.filter(visibility__not_in=q.visibility__not_in)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = FeatureOrderBy(q.order_by)
        records = query.order_by(*order_by.fields) \
            .offset(p.offset).limit(p.limit).values()
        return await records, total

    @staticmethod
    async def update_one(pk: Union[str, UUID], data: Dict) -> Tuple[int, Optional[dict]]:
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        affected_rows = await Feature.filter(id=pk).update(**data)

        updated_record = None
        if affected_rows > 0:
            updated_record = await Feature.get(id=pk).values()

        return affected_rows, updated_record
