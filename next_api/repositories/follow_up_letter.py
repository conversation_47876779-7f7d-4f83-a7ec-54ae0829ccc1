from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from models.follow_up_letter import FollowUpLetter
from repositories.base import Pagination, OrderBy


class FollowUpLetterOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class FollowUpLetterQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    jd_id: Optional[UUID] = None
    jd_id__in: Optional[List[UUID]] = None
    jd_id__not_in: Optional[List[UUID]] = None

    resume_id: Optional[UUID] = None
    resume_id__in: Optional[List[UUID]] = None
    resume_id__not_in: Optional[List[UUID]] = None

    follow_up: Optional[str] = None
    follow_up__contains: Optional[str] = None
    follow_up__icontains: Optional[str] = None

    customization_note: Optional[str] = None
    customization_note__contains: Optional[str] = None
    customization_note__icontains: Optional[str] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "user_id", "jd_id", "resume_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        "jd_id__in", "jd_id__not_in",
        "resume_id__in", "resume_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "follow_up", "follow_up__contains", "follow_up__icontains",
        "customization_note", "customization_note__contains", "customization_note__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class FollowUpLetterRepository:

    @staticmethod
    async def create_one(follow_up_letter: Dict[str, Any]) -> dict:
        """Create a single follow-up letter record."""
        record = await FollowUpLetter.create(**follow_up_letter)
        return dict(record)

    @staticmethod
    async def create_many(follow_up_letters: List[Dict[str, Any]]) -> List[FollowUpLetter]:
        """Create multiple follow-up letter records in bulk."""
        return await FollowUpLetter.bulk_create([FollowUpLetter(**letter) for letter in follow_up_letters])

    @staticmethod
    async def get_one(q: FollowUpLetterQuery) -> Optional[dict]:
        """Get a single follow-up letter that matches the ID in the query."""
        return await FollowUpLetter.get_or_none(id=q.id).values()

    @staticmethod
    async def get_list(q: FollowUpLetterQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of follow-up letters matching the query criteria."""
        query = FollowUpLetter.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.jd_id is not None:
            query = query.filter(jd_id=q.jd_id)
        if q.jd_id__in is not None:
            query = query.filter(jd_id__in=q.jd_id__in)
        if q.jd_id__not_in is not None:
            query = query.filter(jd_id__not_in=q.jd_id__not_in)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.resume_id__in is not None:
            query = query.filter(resume_id__in=q.resume_id__in)
        if q.resume_id__not_in is not None:
            query = query.filter(resume_id__not_in=q.resume_id__not_in)
        if q.follow_up is not None:
            query = query.filter(follow_up=q.follow_up)
        if q.follow_up__contains is not None:
            query = query.filter(follow_up__contains=q.follow_up__contains)
        if q.follow_up__icontains is not None:
            query = query.filter(follow_up__icontains=q.follow_up__icontains)
        if q.customization_note is not None:
            query = query.filter(customization_note=q.customization_note)
        if q.customization_note__contains is not None:
            query = query.filter(customization_note__contains=q.customization_note__contains)
        if q.customization_note__icontains is not None:
            query = query.filter(customization_note__icontains=q.customization_note__icontains)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = FollowUpLetterOrderBy(q.order_by)
        letters = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()

        return letters, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, FollowUpLetter]) -> Tuple[int, Optional[dict]]:
        """Update a follow-up letter record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await FollowUpLetter.filter(id=pk).update(**data)
        else:
            affected_rows = await FollowUpLetter.filter(id=pk).update(data)

        updated_letter = None
        if affected_rows > 0:
            updated_letter = await FollowUpLetter.get(pk=pk).values()

        return affected_rows, updated_letter
