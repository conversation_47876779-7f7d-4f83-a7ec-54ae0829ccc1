from datetime import datetime
from typing import Dict, Any, <PERSON><PERSON>, Optional, List
from uuid import UUID
from pydantic import BaseModel, ConfigDict, field_validator
from models.role_permission import Permission
from repositories.base import Pagination, OrderBy


class PermissionOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "name", "-name",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class PermissionQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    name: Optional[str] = None
    name__contains: Optional[str] = None
    name__icontains: Optional[str] = None
    name__startswith: Optional[str] = None
    name__endswith: Optional[str] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    roles__id: Optional[UUID] = None
    roles__id__in: Optional[List[UUID]] = None
    roles__id__not_in: Optional[List[UUID]] = None

    roles__users__id: Optional[UUID] = None
    roles__users__id__in: Optional[List[UUID]] = None
    roles__users__id__not_in: Optional[List[UUID]] = None

    order_by: Optional[List[str]] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_id(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator("id__in", "id__not_in", mode="before")
    @classmethod
    def validate_id_in(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "name", "name__contains", "name__icontains", "name__startswith", "name__endswith",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v


class PermissionRepository:
    @staticmethod
    async def create_one(data: Dict) -> Dict:
        return await Permission.create(**data)

    @staticmethod
    async def get_list(q: PermissionQuery, p: Pagination) -> Tuple[List[dict], int]:
        query = Permission.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.name is not None:
            query = query.filter(name=q.name)
        if q.name__contains is not None:
            query = query.filter(name__contains=q.name__contains)
        if q.name__icontains is not None:
            query = query.filter(name__icontains=q.name__icontains)
        if q.name__startswith is not None:
            query = query.filter(name__startswith=q.name__startswith)
        if q.name__endswith is not None:
            query = query.filter(name__endswith=q.name__endswith)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)
        if q.roles__id is not None:
            query = query.filter(roles__id=q.roles__id)
        if q.roles__id__in is not None:
            query = query.filter(roles__id__in=q.roles__id__in)
        if q.roles__id__not_in is not None:
            query = query.filter(roles__id__not_in=q.roles__id__not_in)
        if q.roles__users__id is not None:
            query = query.filter(roles__users__id=q.roles__users__id)
        if q.roles__users__id__in is not None:
            query = query.filter(roles__users__id__in=q.roles__users__id__in)
        if q.roles__users__id__not_in is not None:
            query = query.filter(
                roles__users__id__not_in=q.roles__users__id__not_in)

        total = await query.count()
        order_by = PermissionOrderBy(q.order_by)
        records = query.order_by(*order_by.fields) \
            .offset(p.offset).limit(p.limit).values(
                'id', 'name', 'description', 'created_at', 'updated_at',
        )
        return await records, total
