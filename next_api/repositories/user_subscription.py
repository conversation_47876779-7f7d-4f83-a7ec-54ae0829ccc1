from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from repositories.base import Pagination, OrderBy
from models.user_subscription import UserSubscription
from models.subscription_plan_feature import SubscriptionPlanFeature
from models.user_feature import UserFeature
from tortoise.transactions import atomic
from models.subscription_plan import SubscriptionPlan
from dateutil.relativedelta import relativedelta


class UserSubscriptionOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "user_id", "-user_id",
        "subscription_plan_id", "-subscription_plan_id",
        "status", "-status",
        "start_date", "-start_date",
        "end_date", "-end_date",
        "last_billing_date", "-last_billing_date",
        "next_billing_date", "-next_billing_date",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class UserSubscriptionQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    subscription_plan_id: Optional[UUID] = None
    subscription_plan_id__in: Optional[List[UUID]] = None
    subscription_plan_id__not_in: Optional[List[UUID]] = None

    status: Optional[UserSubscription.Status] = None
    status__in: Optional[List[UserSubscription.Status]] = None
    status__not_in: Optional[List[UserSubscription.Status]] = None

    start_date: Optional[datetime] = None
    start_date__gte: Optional[datetime] = None
    start_date__lte: Optional[datetime] = None

    end_date: Optional[datetime] = None
    end_date__gte: Optional[datetime] = None
    end_date__lte: Optional[datetime] = None
    end_date__isnull: Optional[bool] = None
    end_date__not_isnull: Optional[bool] = None

    last_billing_date: Optional[datetime] = None
    last_billing_date__gte: Optional[datetime] = None
    last_billing_date__lte: Optional[datetime] = None
    last_billing_date__isnull: Optional[bool] = None
    last_billing_date__not_isnull: Optional[bool] = None

    next_billing_date: Optional[datetime] = None
    next_billing_date__gte: Optional[datetime] = None
    next_billing_date__lte: Optional[datetime] = None
    next_billing_date__isnull: Optional[bool] = None
    next_billing_date__not_isnull: Optional[bool] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    partner: Optional[str] = None
    partner__contains: Optional[str] = None
    partner__icontains: Optional[str] = None
    partner__startswith: Optional[str] = None
    partner__endswith: Optional[str] = None
    partner__in: Optional[List[str]] = None
    partner__not_in: Optional[List[str]] = None

    partner_id: Optional[str] = None
    partner_id__contains: Optional[str] = None
    partner_id__icontains: Optional[str] = None
    partner_id__startswith: Optional[str] = None
    partner_id__endswith: Optional[str] = None
    partner_id__in: Optional[List[str]] = None
    partner_id__not_in: Optional[List[str]] = None

    @field_validator("id", "user_id", "subscription_plan_id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        "subscription_plan_id__in", "subscription_plan_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> List[UUID]:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(",")]
        return v

    @field_validator("status", mode="before")
    @classmethod
    def validate_int(cls, v: Any) -> Any:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator("status__in", "status__not_in", mode="before")
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(",")]
        return v

    @field_validator(
        "end_date__isnull", "end_date__not_isnull",
        "last_billing_date__isnull", "last_billing_date__not_isnull",
        "next_billing_date__isnull", "next_billing_date__not_isnull",
        mode="before"
    )
    @classmethod
    def validate_boolean(cls, v: Any) -> Any:
        if isinstance(v, list):
            v = v[0]
        if isinstance(v, str):
            return v.lower() in ("true", "1")
        if isinstance(v, int):
            return v == 1
        return v

    @field_validator(
        "start_date", "start_date__gte", "start_date__lte",
        "end_date", "end_date__gte", "end_date__lte",
        "last_billing_date", "last_billing_date__gte", "last_billing_date__lte",
        "next_billing_date", "next_billing_date__gte", "next_billing_date__lte",
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        if isinstance(v, str):
            return datetime.fromisoformat(v)
        return v

    @field_validator(
        "partner", "partner__contains", "partner__icontains",
        "partner__startswith", "partner__endswith",
        "partner_id", "partner_id__contains", "partner_id__icontains",
        "partner_id__startswith", "partner_id__endswith",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> str:
        if isinstance(v, list):
            return str(v[0])
        return str(v)

    @field_validator(
        "partner__in", "partner__not_in",
        "partner_id__in", "partner_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_string_list(cls, v: Any) -> List[str]:
        if isinstance(v, str):
            return [x.strip() for x in v.split(",")]
        return v


class UserSubscriptionRepository:
    # Map LemonSqueezy subscription statuses to our UserSubscription.Status
    LEMON_STATUS_MAP = {
        # Active statuses
        "on_trial": UserSubscription.Status.ACTIVE,
        "active": UserSubscription.Status.ACTIVE,
        # Cancelled status
        "cancelled": UserSubscription.Status.CANCELLED,
        # Inactive statuses
        "unpaid": UserSubscription.Status.INACTIVE,
        "expired": UserSubscription.Status.INACTIVE,
    }

    @staticmethod
    def map_lemon_status(lemon_status: str) -> UserSubscription.Status:
        """Map LemonSqueezy subscription status to internal status.

        Args:
            lemon_status: Status string from LemonSqueezy webhook

        Returns:
            Mapped UserSubscription.Status
        """
        return UserSubscriptionRepository.LEMON_STATUS_MAP.get(
            lemon_status.lower(),
            UserSubscription.Status.INACTIVE  # Default to INACTIVE for unknown statuses
        )

    @staticmethod
    async def create_one(subscription: Dict[str, Any]) -> dict:
        """Create a single user subscription record.

        Args:
            subscription (Dict[str, Any]): Dictionary containing subscription fields and values.

        Returns:
            dict: The created UserSubscription instance as a dictionary.
        """
        record = await UserSubscription.create(**subscription)
        return dict(record)

    @staticmethod
    async def create_many(subscriptions: List[Dict[str, Any]]) -> List[UserSubscription]:
        """Create multiple user subscription records in bulk.

        Args:
            subscriptions (List[Dict[str, Any]]): List of dictionaries containing subscription data.

        Returns:
            List[UserSubscription]: List of created UserSubscription instances.
        """
        subscription_models = [UserSubscription(**sub) for sub in subscriptions]
        return await UserSubscription.bulk_create(subscription_models)

    @staticmethod
    async def get_one(pk: Union[str, UUID]) -> Optional[dict]:
        """Get a single user subscription by ID.

        Args:
            pk (Union[str, UUID]): ID of subscription to retrieve.

        Returns:
            Optional[dict]: Matching UserSubscription instance if found, None otherwise.
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        return await UserSubscription.get_or_none(id=pk).values()

    @staticmethod
    async def get_list(
        q: UserSubscriptionQuery,
        p: Pagination,
        prefetch: bool = False
    ) -> Tuple[List[dict], int]:
        """Get a paginated list of user subscriptions matching the query criteria.

        Args:
            q (UserSubscriptionQuery): Query object containing filter criteria.
            p (Pagination): Pagination parameters for offset and limit.
            prefetch (bool, optional): Whether to prefetch related subscription plan. Defaults to False.

        Returns:
            Tuple[List[dict], int]: Tuple containing:
                - List of UserSubscription instances as dictionaries
                - Total count of matching records before pagination
        """
        query = UserSubscription.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)

        if q.subscription_plan_id is not None:
            query = query.filter(subscription_plan_id=q.subscription_plan_id)
        if q.subscription_plan_id__in is not None:
            query = query.filter(subscription_plan_id__in=q.subscription_plan_id__in)
        if q.subscription_plan_id__not_in is not None:
            query = query.filter(subscription_plan_id__not_in=q.subscription_plan_id__not_in)

        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if q.start_date is not None:
            query = query.filter(start_date=q.start_date)
        if q.start_date__gte is not None:
            query = query.filter(start_date__gte=q.start_date__gte)
        if q.start_date__lte is not None:
            query = query.filter(start_date__lte=q.start_date__lte)

        if q.end_date is not None:
            query = query.filter(end_date=q.end_date)
        if q.end_date__gte is not None:
            query = query.filter(end_date__gte=q.end_date__gte)
        if q.end_date__lte is not None:
            query = query.filter(end_date__lte=q.end_date__lte)
        if q.end_date__isnull is not None:
            query = query.filter(end_date__isnull=q.end_date__isnull)
        if q.end_date__not_isnull is not None:
            query = query.filter(end_date__not_isnull=q.end_date__not_isnull)

        if q.last_billing_date is not None:
            query = query.filter(last_billing_date=q.last_billing_date)
        if q.last_billing_date__gte is not None:
            query = query.filter(last_billing_date__gte=q.last_billing_date__gte)
        if q.last_billing_date__lte is not None:
            query = query.filter(last_billing_date__lte=q.last_billing_date__lte)
        if q.last_billing_date__isnull is not None:
            query = query.filter(last_billing_date__isnull=q.last_billing_date__isnull)
        if q.last_billing_date__not_isnull is not None:
            query = query.filter(last_billing_date__not_isnull=q.last_billing_date__not_isnull)

        if q.next_billing_date is not None:
            query = query.filter(next_billing_date=q.next_billing_date)
        if q.next_billing_date__gte is not None:
            query = query.filter(next_billing_date__gte=q.next_billing_date__gte)
        if q.next_billing_date__lte is not None:
            query = query.filter(next_billing_date__lte=q.next_billing_date__lte)
        if q.next_billing_date__isnull is not None:
            query = query.filter(next_billing_date__isnull=q.next_billing_date__isnull)
        if q.next_billing_date__not_isnull is not None:
            query = query.filter(next_billing_date__not_isnull=q.next_billing_date__not_isnull)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        # Add partner filters
        if q.partner is not None:
            query = query.filter(partner=q.partner)
        if q.partner__contains is not None:
            query = query.filter(partner__contains=q.partner__contains)
        if q.partner__icontains is not None:
            query = query.filter(partner__icontains=q.partner__icontains)
        if q.partner__startswith is not None:
            query = query.filter(partner__startswith=q.partner__startswith)
        if q.partner__endswith is not None:
            query = query.filter(partner__endswith=q.partner__endswith)
        if q.partner__in is not None:
            query = query.filter(partner__in=q.partner__in)
        if q.partner__not_in is not None:
            query = query.filter(partner__not_in=q.partner__not_in)

        # Add partner_id filters
        if q.partner_id is not None:
            query = query.filter(partner_id=q.partner_id)
        if q.partner_id__contains is not None:
            query = query.filter(partner_id__contains=q.partner_id__contains)
        if q.partner_id__icontains is not None:
            query = query.filter(partner_id__icontains=q.partner_id__icontains)
        if q.partner_id__startswith is not None:
            query = query.filter(partner_id__startswith=q.partner_id__startswith)
        if q.partner_id__endswith is not None:
            query = query.filter(partner_id__endswith=q.partner_id__endswith)
        if q.partner_id__in is not None:
            query = query.filter(partner_id__in=q.partner_id__in)
        if q.partner_id__not_in is not None:
            query = query.filter(partner_id__not_in=q.partner_id__not_in)

        total = await query.count()
        order_by = UserSubscriptionOrderBy(q.order_by)

        if prefetch:
            subscriptions = await query.prefetch_related('subscription_plan').order_by(*order_by.fields).offset(p.offset).limit(p.limit).values(
                'id',
                'user_id',
                'subscription_plan_id',
                'status',
                'start_date',
                'end_date',
                'last_billing_date',
                'next_billing_date',
                'created_at',
                'updated_at',
                'subscription_plan__id',
                'subscription_plan__name',
                'subscription_plan__description',
                'subscription_plan__billing_cycle',
                'subscription_plan__is_auto_renewable',
            )
        else:
            subscriptions = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).values()

        return subscriptions, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Dict[str, Any]) -> Tuple[int, Optional[dict]]:
        """Update a user subscription record.

        Args:
            pk (Union[str, UUID]): Primary key of the subscription to update
            data (Dict[str, Any]): Dictionary of subscription data to update

        Returns:
            Tuple[int, Optional[dict]]: Tuple containing:
                - Number of rows affected (0 or 1)
                - Updated UserSubscription instance if successful, None otherwise
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        if "updated_at" not in data:
            data["updated_at"] = datetime.now()

        affected_rows = await UserSubscription.filter(id=pk).update(**data)
        updated_subscription = None
        if affected_rows > 0:
            updated_subscription = await UserSubscription.get(id=pk).values()

        return affected_rows, updated_subscription

    @staticmethod
    async def update_by_query(update_data: dict, q: UserSubscriptionQuery) -> int:
        """Update multiple user subscription records that match the query criteria.

        Args:
            update_data (dict): Dictionary containing the fields and values to update
            q (UserSubscriptionQuery): Query object containing filter criteria

        Returns:
            int: Number of records affected by the update operation
        """
        query = UserSubscription.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)

        if q.subscription_plan_id is not None:
            query = query.filter(subscription_plan_id=q.subscription_plan_id)
        if q.subscription_plan_id__in is not None:
            query = query.filter(subscription_plan_id__in=q.subscription_plan_id__in)
        if q.subscription_plan_id__not_in is not None:
            query = query.filter(subscription_plan_id__not_in=q.subscription_plan_id__not_in)

        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if q.start_date is not None:
            query = query.filter(start_date=q.start_date)
        if q.start_date__gte is not None:
            query = query.filter(start_date__gte=q.start_date__gte)
        if q.start_date__lte is not None:
            query = query.filter(start_date__lte=q.start_date__lte)

        if q.end_date is not None:
            query = query.filter(end_date=q.end_date)
        if q.end_date__gte is not None:
            query = query.filter(end_date__gte=q.end_date__gte)
        if q.end_date__lte is not None:
            query = query.filter(end_date__lte=q.end_date__lte)
        if q.end_date__isnull is not None:
            query = query.filter(end_date__isnull=q.end_date__isnull)
        if q.end_date__not_isnull is not None:
            query = query.filter(end_date__not_isnull=q.end_date__not_isnull)

        if q.last_billing_date is not None:
            query = query.filter(last_billing_date=q.last_billing_date)
        if q.last_billing_date__gte is not None:
            query = query.filter(last_billing_date__gte=q.last_billing_date__gte)
        if q.last_billing_date__lte is not None:
            query = query.filter(last_billing_date__lte=q.last_billing_date__lte)
        if q.last_billing_date__isnull is not None:
            query = query.filter(last_billing_date__isnull=q.last_billing_date__isnull)
        if q.last_billing_date__not_isnull is not None:
            query = query.filter(last_billing_date__not_isnull=q.last_billing_date__not_isnull)

        if q.next_billing_date is not None:
            query = query.filter(next_billing_date=q.next_billing_date)
        if q.next_billing_date__gte is not None:
            query = query.filter(next_billing_date__gte=q.next_billing_date__gte)
        if q.next_billing_date__lte is not None:
            query = query.filter(next_billing_date__lte=q.next_billing_date__lte)
        if q.next_billing_date__isnull is not None:
            query = query.filter(next_billing_date__isnull=q.next_billing_date__isnull)
        if q.next_billing_date__not_isnull is not None:
            query = query.filter(next_billing_date__not_isnull=q.next_billing_date__not_isnull)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        if "updated_at" not in update_data:
            update_data["updated_at"] = datetime.now()

        return await query.update(**update_data)

    @staticmethod
    @atomic()
    async def subscribe_plan(
        user_id: UUID,
        plan_id: UUID,
        partner: str,
        partner_id: str,
        start_date: Optional[datetime] = None,
    ) -> Tuple[dict, List[dict]]:
        """Subscribe a user to a plan and assign all plan features to the user.

        Args:
            user_id (UUID): ID of the user
            plan_id (UUID): ID of the subscription plan
            partner (str): Partner name (e.g. "lemonsqueezy")
            partner_id (str): Partner's subscription ID
            start_date (datetime, optional): Start date of subscription. Defaults to current time.

        Returns:
            Tuple[dict, List[dict]]: Tuple containing:
                - Created/Updated UserSubscription record as dictionary
                - List of created UserFeature records as dictionaries

        Raises:
            ValueError: If subscription plan not found
            ValueError: If billing cycle is invalid
        """
        # Check if subscription already exists for this user, plan, and partner
        existing_subscription = await UserSubscription.get_or_none(
            user_id=user_id,
            subscription_plan_id=plan_id,
            partner=partner,
            partner_id=partner_id
        )

        # Get subscription plan to determine billing cycle
        plan = await SubscriptionPlan.get_or_none(id=plan_id)
        if not plan:
            raise ValueError("Subscription plan not found")

        start_time = start_date or datetime.now()

        # Calculate end date based on billing cycle
        billing_cycle_map = {
            SubscriptionPlan.BillingCycle.MONTHLY: {'months': 1},
            SubscriptionPlan.BillingCycle.QUARTERLY: {'months': 3},
            SubscriptionPlan.BillingCycle.SEMI_ANNUAL: {'months': 6},
            SubscriptionPlan.BillingCycle.ANNUAL: {'years': 1},
            SubscriptionPlan.BillingCycle.BI_ANNUAL: {'years': 2},
            SubscriptionPlan.BillingCycle.TRI_ANNUAL: {'years': 3},
            SubscriptionPlan.BillingCycle.LIFETIME: None,
        }

        cycle_delta = billing_cycle_map.get(plan.billing_cycle)
        if cycle_delta is None and plan.billing_cycle != SubscriptionPlan.BillingCycle.LIFETIME:
            raise ValueError("Invalid billing cycle")

        end_time = start_time + relativedelta(**cycle_delta) if cycle_delta else None

        subscription_data = {
            "user_id": user_id,
            "subscription_plan_id": plan_id,
            "partner": partner,
            "partner_id": partner_id,
            "status": UserSubscription.Status.ACTIVE,
            "start_date": start_time,
            "end_date": end_time,
            "next_billing_date": end_time if plan.is_auto_renewable else None,
            "updated_at": datetime.now()
        }

        if existing_subscription:
            # Update existing subscription
            await UserSubscription.filter(id=existing_subscription.id).update(**subscription_data)
            subscription = await UserSubscription.get(id=existing_subscription.id)
        else:
            # Create new subscription
            subscription = await UserSubscription.create(**subscription_data)

        # Get all features associated with the plan
        plan_features = await SubscriptionPlanFeature.filter(
            subscription_plan_id=plan_id
        ).values('feature_id', 'feature_value')

        # Create user features
        user_features = []
        for plan_feature in plan_features:
            user_feature, _ = await UserFeature.update_or_create(
                user_id=user_id,
                feature_id=plan_feature['feature_id'],
                defaults={
                    'feature_value': plan_feature['feature_value']
                }
            )
            user_features.append(dict(user_feature))

        return dict(subscription), user_features
