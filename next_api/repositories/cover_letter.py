from typing import Dict, Any, Tu<PERSON>, Optional, List, Union
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, ConfigDict, field_validator
from tortoise.expressions import RawSQL
from models.cover_letter import CoverLetter
from repositories.base import Pagination, OrderBy


class CoverLetterOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "title", "-title",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
    )
    _default_order_by = ("created_at",)


class CoverLetterQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    user_id: Optional[UUID] = None
    user_id__in: Optional[List[UUID]] = None
    user_id__not_in: Optional[List[UUID]] = None

    resume_id: Optional[UUID] = None
    resume_id__in: Optional[List[UUID]] = None
    resume_id__not_in: Optional[List[UUID]] = None

    title: Optional[str] = None
    title__contains: Optional[str] = None
    title__icontains: Optional[str] = None
    title__startswith: Optional[str] = None
    title__endswith: Optional[str] = None

    content: Optional[str] = None
    content__contains: Optional[str] = None
    content__icontains: Optional[str] = None

    target_position: Optional[List[str]] = None

    status: Optional[CoverLetter.Status] = None
    status__in: Optional[List[CoverLetter.Status]] = None
    status__not_in: Optional[List[CoverLetter.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    @field_validator(
        "id", "user_id", "resume_id",
        mode="before"
    )
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator(
        "id__in", "id__not_in",
        "user_id__in", "user_id__not_in",
        "resume_id__in", "resume_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_uuid_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(',')]
        return v

    @field_validator(
        "title", "title__contains", "title__icontains", "title__startswith", "title__endswith",
        "content", "content__contains", "content__icontains",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return v[0]
        return v

    @field_validator(
        "target_position",
        mode="before"
    )
    @classmethod
    def validate_str_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return v.split(',')
        if isinstance(v, list):
            result = []
            for item in v:
                result.extend(item.split(','))
            return result
        return v

    @field_validator(
        "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            v = int(v)
        elif isinstance(v, list):
            v = int(v[0])
        return v

    @field_validator(
        "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> Any:
        if isinstance(v, str):
            return [int(v) for v in v.split(',')]
        return v

    @field_validator(
        "created_at", "created_at__gte", "created_at__lte",
        "updated_at", "updated_at__gte", "updated_at__lte",
        mode="before"
    )
    @classmethod
    def validate_datetime(cls, v: Any) -> Any:
        return datetime.fromisoformat(v)


class CoverLetterRepository:
    @staticmethod
    async def create_one(cover_letter: Dict[str, Any]) -> CoverLetter:
        """Create a single cover letter record from a dictionary of cover letter data."""
        return await CoverLetter.create(**cover_letter)

    @staticmethod
    async def create_many(cover_letters: List[Dict[str, Any]]) -> List[CoverLetter]:
        """Create multiple cover letter records in bulk."""
        return await CoverLetter.bulk_create([CoverLetter(**cover_letter) for cover_letter in cover_letters])

    @staticmethod
    async def get_one(q: CoverLetterQuery) -> Optional[CoverLetter]:
        """Get a single cover letter that matches the ID in the query."""
        cover_letter = await CoverLetter.get_or_none(id=q.id)
        return cover_letter

    @staticmethod
    async def get_list(q: CoverLetterQuery, p: Pagination, as_dict: bool = True)\
            -> Tuple[Union[List[dict], List[CoverLetter]], int]:
        """Get a paginated list of cover letters matching the query criteria."""
        query = CoverLetter.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.resume_id__in is not None:
            query = query.filter(resume_id__in=q.resume_id__in)
        if q.resume_id__not_in is not None:
            query = query.filter(resume_id__not_in=q.resume_id__not_in)
        if q.title is not None:
            query = query.filter(title=q.title)
        if q.title__contains is not None:
            query = query.filter(title__contains=q.title__contains)
        if q.title__icontains is not None:
            query = query.filter(title__icontains=q.title__icontains)
        if q.title__startswith is not None:
            query = query.filter(title__startswith=q.title__startswith)
        if q.title__endswith is not None:
            query = query.filter(title__endswith=q.title__endswith)
        if q.content__contains is not None:
            query = query.filter(content__contains=q.content__contains)
        if q.content__icontains is not None:
            query = query.filter(content__icontains=q.content__icontains)
        if q.target_position is not None:
            query = query.annotate(overlapped=RawSQL(
                "target_position && ARRAY[" + ",".join([f"'{tp}'" for tp in q.target_position]) + "]::TEXT[]"))
            query = query.filter(overlapped=True)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        total = await query.count()
        order_by = CoverLetterOrderBy(q.order_by)
        if as_dict:
            cover_letters = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all().values()
        else:
            cover_letters = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).all()

        return cover_letters, total

    @staticmethod
    async def update(pk: Union[str, UUID], data: Union[dict, CoverLetter]) -> Tuple[int, Optional[CoverLetter]]:
        """Update a cover letter record."""
        if isinstance(pk, str):
            pk = UUID(pk)
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now()
        if isinstance(data, dict):
            affected_rows = await CoverLetter.filter(id=pk).update(**data)
        else:
            affected_rows = await CoverLetter.filter(id=pk).update(data)

        updated_cover_letter = None
        if affected_rows > 0:
            updated_cover_letter = await CoverLetter.get(pk=pk)

        return affected_rows, updated_cover_letter

    @staticmethod
    async def update_by_query(update_data: dict, q: CoverLetterQuery) -> int:
        """Update multiple cover letter records that match the given query criteria."""
        query = CoverLetter.all()

        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)
        if q.user_id is not None:
            query = query.filter(user_id=q.user_id)
        if q.user_id__in is not None:
            query = query.filter(user_id__in=q.user_id__in)
        if q.user_id__not_in is not None:
            query = query.filter(user_id__not_in=q.user_id__not_in)
        if q.resume_id is not None:
            query = query.filter(resume_id=q.resume_id)
        if q.resume_id__in is not None:
            query = query.filter(resume_id__in=q.resume_id__in)
        if q.resume_id__not_in is not None:
            query = query.filter(resume_id__not_in=q.resume_id__not_in)
        if q.title is not None:
            query = query.filter(title=q.title)
        if q.title__contains is not None:
            query = query.filter(title__contains=q.title__contains)
        if q.title__icontains is not None:
            query = query.filter(title__icontains=q.title__icontains)
        if q.title__startswith is not None:
            query = query.filter(title__startswith=q.title__startswith)
        if q.title__endswith is not None:
            query = query.filter(title__endswith=q.title__endswith)
        if q.content__contains is not None:
            query = query.filter(content__contains=q.content__contains)
        if q.content__icontains is not None:
            query = query.filter(content__icontains=q.content__icontains)
        if q.target_position is not None:
            query = query.annotate(overlapped=RawSQL(
                "target_position && ARRAY[" + ",".join([f"'{tp}'" for tp in q.target_position]) + "]::TEXT[]"))
            query = query.filter(overlapped=True)
        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)
        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)
        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        if 'updated_at' not in update_data:
            update_data['updated_at'] = datetime.now()
        return await query.update(**update_data)
