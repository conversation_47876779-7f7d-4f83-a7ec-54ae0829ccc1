from datetime import datetime
from typing import Dict, Any, Tuple, Optional, List, Union
from uuid import UUID
from decimal import Decimal
from pydantic import BaseModel, ConfigDict, field_validator
from models.subscription_plan import SubscriptionPlan
from repositories.base import Pagination, OrderBy
from models.feature import Feature
from models.subscription_plan_feature import SubscriptionPlanFeature


class SubscriptionPlanOrderBy(OrderBy):
    _allows_fields = (
        "id", "-id",
        "code", "-code",
        "name", "-name",
        "price", "-price",
        "billing_cycle", "-billing_cycle",
        "trial_period_days", "-trial_period_days",
        "sort_order", "-sort_order",
        "is_public", "-is_public",
        "status", "-status",
        "created_at", "-created_at",
        "updated_at", "-updated_at",
        "lemon_variant_id", "-lemon_variant_id",
        "polar_product_id", "-polar_product_id",
    )
    _default_order_by = ("sort_order", "created_at")


class SubscriptionPlanQuery(BaseModel):
    model_config = ConfigDict(extra="allow")

    id: Optional[UUID] = None
    id__in: Optional[List[UUID]] = None
    id__not_in: Optional[List[UUID]] = None

    code: Optional[str] = None
    code__in: Optional[List[str]] = None
    code__not_in: Optional[List[str]] = None
    code__contains: Optional[str] = None
    code__icontains: Optional[str] = None
    code__startswith: Optional[str] = None
    code__endswith: Optional[str] = None

    name: Optional[str] = None
    name__contains: Optional[str] = None
    name__icontains: Optional[str] = None
    name__startswith: Optional[str] = None
    name__endswith: Optional[str] = None

    price: Optional[Decimal] = None
    price__gt: Optional[Decimal] = None
    price__gte: Optional[Decimal] = None
    price__lt: Optional[Decimal] = None
    price__lte: Optional[Decimal] = None

    billing_cycle: Optional[SubscriptionPlan.BillingCycle] = None
    billing_cycle__in: Optional[List[SubscriptionPlan.BillingCycle]] = None

    is_auto_renewable: Optional[bool] = None

    trial_period_days: Optional[int] = None
    trial_period_days__gt: Optional[int] = None
    trial_period_days__gte: Optional[int] = None
    trial_period_days__lt: Optional[int] = None
    trial_period_days__lte: Optional[int] = None

    sort_order: Optional[int] = None
    sort_order__gt: Optional[int] = None
    sort_order__gte: Optional[int] = None
    sort_order__lt: Optional[int] = None
    sort_order__lte: Optional[int] = None

    is_public: Optional[bool] = None

    status: Optional[SubscriptionPlan.Status] = None
    status__in: Optional[List[SubscriptionPlan.Status]] = None
    status__not_in: Optional[List[SubscriptionPlan.Status]] = None

    created_at: Optional[datetime] = None
    created_at__gte: Optional[datetime] = None
    created_at__lte: Optional[datetime] = None

    updated_at: Optional[datetime] = None
    updated_at__gte: Optional[datetime] = None
    updated_at__lte: Optional[datetime] = None

    order_by: Optional[List[str]] = None

    lemon_variant_id: Optional[int] = None
    lemon_variant_id__in: Optional[List[int]] = None
    lemon_variant_id__not_in: Optional[List[int]] = None

    polar_product_id: Optional[str] = None
    polar_product_id__in: Optional[List[str]] = None
    polar_product_id__not_in: Optional[List[str]] = None

    @field_validator("id", mode="before")
    @classmethod
    def validate_uuid(cls, v: Any) -> UUID:
        if isinstance(v, str):
            return UUID(v)
        if isinstance(v, list):
            return UUID(v[0])
        if isinstance(v, UUID):
            return v
        return UUID(v)

    @field_validator("id__in", "id__not_in", mode="before")
    @classmethod
    def validate_uuid_list(cls, v: Any) -> List[UUID]:
        if isinstance(v, str):
            return [UUID(id) for id in v.split(",")]
        return v

    @field_validator(
        "name", "name__contains", "name__icontains", "name__startswith", "name__endswith",
        "code", "code__in", "code__not_in",
        mode="before"
    )
    @classmethod
    def validate_string(cls, v: Any) -> Any:
        if isinstance(v, list):
            return str(v[0])
        return str(v)

    @field_validator(
        "price", "price__gt", "price__gte", "price__lt", "price__lte",
        mode="before"
    )
    @classmethod
    def validate_decimal(cls, v: Any) -> Decimal:
        if isinstance(v, str):
            return Decimal(v)
        return v

    @field_validator(
        "billing_cycle", "status",
        mode="before"
    )
    @classmethod
    def validate_int(cls, v: Any) -> int:
        if isinstance(v, str):
            return int(v)
        return v

    @field_validator(
        "billing_cycle__in", "status__in", "status__not_in",
        mode="before"
    )
    @classmethod
    def validate_int_list(cls, v: Any) -> List[int]:
        if isinstance(v, str):
            return [int(x) for x in v.split(",")]
        return v

    @field_validator(
        "is_auto_renewable", "is_public",
        mode="before"
    )
    @classmethod
    def validate_boolean(cls, v: Any) -> bool:
        if isinstance(v, str):
            return v.lower() in ("true", "1", "yes")
        return bool(v)

    @field_validator(
        "trial_period_days", "trial_period_days__gt", "trial_period_days__gte",
        "trial_period_days__lt", "trial_period_days__lte",
        mode="before"
    )
    @classmethod
    def validate_integer(cls, v: Any) -> int:
        if isinstance(v, str):
            return int(v)
        return v

    @field_validator(
        "sort_order", "sort_order__gt", "sort_order__gte",
        "sort_order__lt", "sort_order__lte",
        mode="before"
    )
    @classmethod
    def validate_sort_order(cls, v: Any) -> int:
        if isinstance(v, str):
            return int(v)
        return v

    @field_validator(
        "lemon_variant_id", "lemon_variant_id__in", "lemon_variant_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_lemon_variant_id(cls, v: Any) -> Any:
        if isinstance(v, str):
            if ',' in v:  # Handle list case
                return [int(x.strip()) for x in v.split(',')]
            return int(v)
        return v

    @field_validator(
        "polar_product_id", "polar_product_id__in", "polar_product_id__not_in",
        mode="before"
    )
    @classmethod
    def validate_polar_product_id(cls, v: Any) -> Any:
        if isinstance(v, str):
            if ',' in v and cls.__name__ != "polar_product_id":  # Handle list case for __in and __not_in
                return [x.strip() for x in v.split(',')]
        return v


class SubscriptionPlanRepository:
    @staticmethod
    async def create_one(data: Dict[str, Any]) -> dict:
        """Create a single subscription plan."""
        record = await SubscriptionPlan.create(**data)
        return dict(record)

    @staticmethod
    async def create_many(data_list: List[Dict[str, Any]]) -> List[SubscriptionPlan]:
        """Create multiple subscription plans in bulk."""
        return await SubscriptionPlan.bulk_create(
            [SubscriptionPlan(**data) for data in data_list]
        )

    @staticmethod
    async def get_one(pk: Union[str, UUID], prefetch_features: bool = False) -> Optional[dict]:
        """Get a single subscription plan by ID.

        Args:
            pk: The ID of the subscription plan
            prefetch_features: Whether to include mapped features in response

        Returns:
            Dictionary containing plan info and features if found, None otherwise
        """
        if isinstance(pk, str):
            pk = UUID(pk)

        plan = await SubscriptionPlan.get_or_none(id=pk).values()
        if plan is None:
            return None

        if prefetch_features:
            # Get all mapped features with their details
            mapped_features = await SubscriptionPlanFeature.filter(
                subscription_plan_id=pk
            ).prefetch_related('feature').values(
                'id',  # mapping id
                'feature_value',
                'feature__id',
                'feature__code',
                'feature__name',
                'feature__description'
            )

            # Transform the feature data to match the response format
            features = []
            for feature in mapped_features:
                features.append({
                    'id': feature['feature__id'],
                    'code': feature['feature__code'],
                    'name': feature['feature__name'],
                    'description': feature['feature__description'],
                    'value': feature['feature_value'],
                    'mapping_id': feature['id']
                })

            plan['features'] = features

        return plan

    @staticmethod
    async def get_by_code(code: str, prefetch_features: bool = False) -> Optional[dict]:
        """Get a single subscription plan by code."""
        plan = await SubscriptionPlan.get_or_none(code=code).values()
        if plan is None:
            return None

        if prefetch_features:
            features = await SubscriptionPlan.get(code=code).mapped_features.all().values(
                'id', 'feature_id', 'feature_value'
            )
            plan['features'] = features

        return plan

    @staticmethod
    async def get_list(q: SubscriptionPlanQuery, p: Pagination) -> Tuple[List[dict], int]:
        """Get a paginated list of subscription plans."""
        query = SubscriptionPlan.all()

        # Apply filters
        if q.id is not None:
            query = query.filter(id=q.id)
        if q.id__in is not None:
            query = query.filter(id__in=q.id__in)
        if q.id__not_in is not None:
            query = query.filter(id__not_in=q.id__not_in)

        if q.code is not None:
            query = query.filter(code=q.code)
        if q.code__in is not None:
            query = query.filter(code__in=q.code__in)
        if q.code__not_in is not None:
            query = query.filter(code__not_in=q.code__not_in)

        if q.name is not None:
            query = query.filter(name=q.name)
        if q.name__contains is not None:
            query = query.filter(name__contains=q.name__contains)
        if q.name__icontains is not None:
            query = query.filter(name__icontains=q.name__icontains)
        if q.name__startswith is not None:
            query = query.filter(name__startswith=q.name__startswith)
        if q.name__endswith is not None:
            query = query.filter(name__endswith=q.name__endswith)

        if q.price is not None:
            query = query.filter(price=q.price)
        if q.price__gt is not None:
            query = query.filter(price__gt=q.price__gt)
        if q.price__gte is not None:
            query = query.filter(price__gte=q.price__gte)
        if q.price__lt is not None:
            query = query.filter(price__lt=q.price__lt)
        if q.price__lte is not None:
            query = query.filter(price__lte=q.price__lte)

        if q.billing_cycle is not None:
            query = query.filter(billing_cycle=q.billing_cycle)
        if q.billing_cycle__in is not None:
            query = query.filter(billing_cycle__in=q.billing_cycle__in)

        if q.is_auto_renewable is not None:
            query = query.filter(is_auto_renewable=q.is_auto_renewable)
        if q.is_public is not None:
            query = query.filter(is_public=q.is_public)

        if q.trial_period_days is not None:
            query = query.filter(trial_period_days=q.trial_period_days)
        if q.trial_period_days__gt is not None:
            query = query.filter(trial_period_days__gt=q.trial_period_days__gt)
        if q.trial_period_days__gte is not None:
            query = query.filter(trial_period_days__gte=q.trial_period_days__gte)
        if q.trial_period_days__lt is not None:
            query = query.filter(trial_period_days__lt=q.trial_period_days__lt)
        if q.trial_period_days__lte is not None:
            query = query.filter(trial_period_days__lte=q.trial_period_days__lte)

        if q.status is not None:
            query = query.filter(status=q.status)
        if q.status__in is not None:
            query = query.filter(status__in=q.status__in)
        if q.status__not_in is not None:
            query = query.filter(status__not_in=q.status__not_in)

        if q.created_at is not None:
            query = query.filter(created_at=q.created_at)
        if q.created_at__gte is not None:
            query = query.filter(created_at__gte=q.created_at__gte)
        if q.created_at__lte is not None:
            query = query.filter(created_at__lte=q.created_at__lte)

        if q.updated_at is not None:
            query = query.filter(updated_at=q.updated_at)
        if q.updated_at__gte is not None:
            query = query.filter(updated_at__gte=q.updated_at__gte)
        if q.updated_at__lte is not None:
            query = query.filter(updated_at__lte=q.updated_at__lte)

        if q.sort_order is not None:
            query = query.filter(sort_order=q.sort_order)
        if q.sort_order__gt is not None:
            query = query.filter(sort_order__gt=q.sort_order__gt)
        if q.sort_order__gte is not None:
            query = query.filter(sort_order__gte=q.sort_order__gte)
        if q.sort_order__lt is not None:
            query = query.filter(sort_order__lt=q.sort_order__lt)
        if q.sort_order__lte is not None:
            query = query.filter(sort_order__lte=q.sort_order__lte)

        if q.code__contains is not None:
            query = query.filter(code__contains=q.code__contains)
        if q.code__icontains is not None:
            query = query.filter(code__icontains=q.code__icontains)
        if q.code__startswith is not None:
            query = query.filter(code__startswith=q.code__startswith)
        if q.code__endswith is not None:
            query = query.filter(code__endswith=q.code__endswith)

        if q.lemon_variant_id is not None:
            query = query.filter(lemon_variant_id=q.lemon_variant_id)
        if q.lemon_variant_id__in is not None:
            query = query.filter(lemon_variant_id__in=q.lemon_variant_id__in)
        if q.lemon_variant_id__not_in is not None:
            query = query.filter(lemon_variant_id__not_in=q.lemon_variant_id__not_in)

        if q.polar_product_id is not None:
            query = query.filter(polar_product_id=q.polar_product_id)
        if q.polar_product_id__in is not None:
            query = query.filter(polar_product_id__in=q.polar_product_id__in)
        if q.polar_product_id__not_in is not None:
            query = query.filter(polar_product_id__not_in=q.polar_product_id__not_in)

        total = await query.count()
        order_by = SubscriptionPlanOrderBy(q.order_by)
        records = await query.order_by(*order_by.fields).offset(p.offset).limit(p.limit).values()

        return records, total

    @staticmethod
    async def update_one(pk: Union[str, UUID], data: Dict[str, Any]) -> Tuple[int, Optional[dict]]:
        """Update a subscription plan."""
        if isinstance(pk, str):
            pk = UUID(pk)

        if "updated_at" not in data:
            data["updated_at"] = datetime.now()

        affected_rows = await SubscriptionPlan.filter(id=pk).update(**data)
        updated_record = None
        if affected_rows > 0:
            updated_record = await SubscriptionPlan.get(id=pk).values()

        return affected_rows, updated_record

    @staticmethod
    async def map_features(
        plan_id: Union[str, UUID],
        feature_mappings: Dict[str, Optional[Any]]
    ) -> dict:
        """Map features to a subscription plan.

        Args:
            plan_id: The ID of the subscription plan
            feature_mappings: Dictionary mapping feature codes to their values
                - key: feature_code (str)
                - value: Optional override value (if None, uses feature's default_value)

        Returns:
            Dictionary containing plan information and its features:
            - Empty dict if plan not found
            - Plan info with mapped features if successful
        """
        if isinstance(plan_id, str):
            plan_id = UUID(plan_id)

        # Get the plan to ensure it exists
        plan = await SubscriptionPlan.get_or_none(id=plan_id)
        if plan is None:
            return {}

        # Get plan data
        plan_data = await SubscriptionPlan.get(id=plan_id).values()
        plan_data['features'] = []

        # Create the feature mappings
        for feature_code, override_value in feature_mappings.items():
            # Verify feature exists and get its default value
            feature = await Feature.get_or_none(code=feature_code)
            if feature is None:
                continue

            # Use override value if provided, otherwise use feature's default value
            feature_value = override_value if override_value is not None else feature.default_value

            # Create the mapping
            mapping_data = {
                'subscription_plan_id': plan_id,
                'feature_id': feature.id,
                'feature_value': feature_value
            }
            mapping_record = await SubscriptionPlanFeature.create(**mapping_data)

            # Add feature info to response
            feature_info = {
                'id': feature.id,
                'code': feature.code,
                'name': feature.name,
                'description': feature.description,
                'value': feature_value,
                'mapping_id': mapping_record.id
            }
            plan_data['features'].append(feature_info)

        return plan_data

    @staticmethod
    async def get_by_lemon_variant_id(lemon_variant_id: int, prefetch_features: bool = False) -> Optional[dict]:
        """Get a single subscription plan by lemon_variant_id.

        Args:
            lemon_variant_id: The Lemonsqueezy variant ID
            prefetch_features: Whether to include mapped features in response

        Returns:
            Dictionary containing plan info and features if found, None otherwise
        """
        plan = await SubscriptionPlan.get_or_none(lemon_variant_id=lemon_variant_id).values()
        if plan is None:
            return None

        if prefetch_features:
            # Get all mapped features with their details
            mapped_features = await SubscriptionPlanFeature.filter(
                subscription_plan_id=plan['id']
            ).prefetch_related('feature').values(
                'id',  # mapping id
                'feature_value',
                'feature__id',
                'feature__code',
                'feature__name',
                'feature__description'
            )

            # Transform the feature data to match the response format
            features = []
            for feature in mapped_features:
                features.append({
                    'id': feature['feature__id'],
                    'code': feature['feature__code'],
                    'name': feature['feature__name'],
                    'description': feature['feature__description'],
                    'value': feature['feature_value'],
                    'mapping_id': feature['id']
                })

            plan['features'] = features

        return plan

    @staticmethod
    async def get_by_polar_product_id(polar_product_id: str, prefetch_features: bool = False) -> Optional[dict]:
        """Get a single subscription plan by polar_product_id.

        Args:
            polar_product_id: The Polar product ID
            prefetch_features: Whether to include mapped features in response

        Returns:
            Dictionary containing plan info and features if found, None otherwise
        """
        plan = await SubscriptionPlan.get_or_none(polar_product_id=polar_product_id).values()
        if plan is None:
            return None

        if prefetch_features:
            # Get all mapped features with their details
            mapped_features = await SubscriptionPlanFeature.filter(
                subscription_plan_id=plan['id']
            ).prefetch_related('feature').values(
                'id',  # mapping id
                'feature_value',
                'feature__id',
                'feature__code',
                'feature__name',
                'feature__description'
            )

            # Transform the feature data to match the response format
            features = []
            for feature in mapped_features:
                features.append({
                    'id': feature['feature__id'],
                    'code': feature['feature__code'],
                    'name': feature['feature__name'],
                    'description': feature['feature__description'],
                    'value': feature['feature_value'],
                    'mapping_id': feature['id']
                })

            plan['features'] = features

        return plan