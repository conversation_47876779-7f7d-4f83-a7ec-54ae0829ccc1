[MASTER]
# Add the next_api directory to Python path
init-hook='import sys; sys.path.append("./next_api")'

[FORMAT]
# Maximum number of characters on a single line
max-line-length=120

[MESSAGES CONTROL]
# Disable specific warnings
disable=
    missing-module-docstring,
    missing-class-docstring,
    missing-function-docstring,
    raise-missing-from,
    broad-exception-caught,
    ungrouped-imports,
    too-few-public-methods,
    too-many-branches,
    too-many-statements,
    too-many-positional-arguments,
    duplicate-code

[BASIC]
# Good variable names
good-names=i,j,k,ex,Run,_,id

[REPORTS]
# Set the output format
output-format=text

# Tells whether to display a full report or only the messages
reports=no

# Python expression which should return a score less than or equal to 10.
evaluation=10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

# Template used to display messages
msg-template={path}:{line}: [{msg_id}({symbol}), {obj}] {msg}